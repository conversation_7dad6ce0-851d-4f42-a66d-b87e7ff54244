// vue.config.js
const CopyWebpackPlugin = require('copy-webpack-plugin')
const path = require('path')
require('events').EventEmitter.defaultMaxListeners = 0;

module.exports = {
  configureWebpack: (config) => {
    if (process.env.UNI_PLATFORM === 'h5') {
      config.plugins.push(
        new CopyWebpackPlugin({
          patterns: [
            {
              from: path.resolve(__dirname, 'X9Dq6az4wh.txt'), // 项目根目录中的文件
              to: path.resolve(__dirname, 'dist/build/h5') // 打包输出的 h5 目录
            }
          ]
        })
      )
    }
  }
}
