首页

```
/miniapp/help/userInfo 用户信息
/miniapp/help/progressAssist 进度情况 参数：role,值为登录用户的roleCode,bbAdmin与其他
/miniapp/help/progressDetail 进度详情，参数：pageNo，pageSize，role,值为登录用户的roleCode,bbAdmin与其他
```

呼叫中心

```
/miniapp/help/helpHandleTelRecordList 参数：hasHandled，assistantId hasHandled为null 查询待处理的，为1用于帮办员收件显示，assistantId不传则为管理员页面，传则是对应的帮办员
/miniapp/help/receiveList 收件中心，参数assistant_id 可不填
/miniapp/help/send 发件
/miniapp/help/reject 拒接
/miniapp/help/receiveAndBack 接收与退回
HelpHandleTelRecordSendRejectDTO为发件与拒接操作的参数body，发件需传除refuseReason的所有参数
拒接传id和refuseReason
ReceiveAndBackDTO 为接收与退回的参数body，接收传id,isReceive(1),recordId,退回填id,isReceive(0),recordId，refuseReason
```

```java
public class HelpHandleTelRecordSendRejectDTO {

    private String id;

    /**事项类型*/
    @ApiModelProperty(value = "事项类型")
    private String itemType;
    /**事项编号*/
    @ApiModelProperty(value = "事项编号")
    private String itemId;
    /**事项名称*/
    @ApiModelProperty(value = "事项名称")
    private String itemName;
    @ApiModelProperty(value = "事项级别")
    private String divisionLevel;

    private String refuseReason;
}
```

```java
public class ReceiveAndBackDTO {
    /**主键*/
    private String id;

    private Integer isReceive;

    private String recordId;

    private String refuseReason;
}
```

帮办相关功能

```
/miniapp/help/helpHandList 帮办员分页
/miniapp/help/helpHandAdminList 帮办管理员分页
传参参考pc端

/miniapp/help/transfer 转派 recordId
/miniapp/help/back 退回 recordId，reason
```

```java
    @AutoLog(value = "添加补充字段")
    @PostMapping(value = "/addJyHelpHandleRecordField")
    public Result<?> addJyHelpHandleRecordField(@RequestBody JyHelpHandleRecordField jyHelpHandleRecordField) {
    }


    @AutoLog(value = "补充字段-编辑")
    @PutMapping(value = "/editJyHelpHandleRecordField")
    public Result<?> editJyHelpHandleRecordField(@RequestBody JyHelpHandleRecordField jyHelpHandleRecordField) {
    }

    @AutoLog(value = "补充字段-通过id删除")
    @DeleteMapping(value = "/deleteJyHelpHandleRecordField")
    public Result<?> deleteJyHelpHandleRecordField(@RequestParam(name="id",required=true) String id) {
        jyHelpHandleRecordFieldService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog(value = "帮办委托书管理表-分页列表查询")
    @GetMapping(value = "/listJyProxyManagementService")
    public Result<?> queryJyProxyManagementServicePageList(JyProxyManagement jyProxyManagement,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   HttpServletRequest req) {
    }


    @AutoLog(value = "帮办委托书管理表-添加")
    @PostMapping(value = "/addJyProxyManagementService")
    public Result<?> addJyProxyManagementService(@RequestBody JyProxyManagement jyProxyManagement) {
    }


    @AutoLog(value = "帮办委托书管理表-编辑")
    @PutMapping(value = "/editJyProxyManagementService")
    public Result<?> editJyProxyManagementService(@RequestBody JyProxyManagement jyProxyManagement) {
    }

    @AutoLog(value = "帮办委托书管理表-通过id删除")
    @DeleteMapping(value = "/deleteJyProxyManagementService")
    public Result<?> deleteJyProxyManagementService(@RequestParam(name="id",required=true) String id) {
    }
```

```java
public class JyProxyManagement implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private java.lang.String id;
	/**办理事项*/
	@Excel(name = "办理事项", width = 15)
    @ApiModelProperty(value = "办理事项")
    @Dict(dictTable = "jy_help_handle_item ",dicCode = "item_id",dicText = "item_name")
    private java.lang.String item;
	/**标题*/
	@Excel(name = "标题", width = 15)
    @ApiModelProperty(value = "标题")
    private java.lang.String title;
	/**内容*/
	@Excel(name = "内容", width = 15)
    @ApiModelProperty(value = "内容")
    private java.lang.String content;
	/**委托书状态*/
	@Excel(name = "委托书状态", width = 15)
    @ApiModelProperty(value = "委托书状态")
    private java.lang.String status;
	/**提交状态*/
	@Excel(name = "提交状态", width = 15)
    @ApiModelProperty(value = "提交状态")
    @Dict(dicCode = "jy_submit_status")
    private java.lang.String submitStatus;
	/**文件地址*/
	@Excel(name = "文件地址", width = 15)
    @ApiModelProperty(value = "文件地址")
    private java.lang.String fileUrl;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    private java.lang.String deleted;
	/**创建者*/
    @ApiModelProperty(value = "创建者")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新者*/
    @ApiModelProperty(value = "更新者")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
}
```

```java
public class JyHelpHandleRecordField implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**帮办记录ID*/
	@Excel(name = "帮办记录ID", width = 15)
    @ApiModelProperty(value = "帮办记录ID")
    private java.lang.String recordId;
	/**字段名称*/
	@Excel(name = "字段名称", width = 15)
    @ApiModelProperty(value = "字段名称")
    private java.lang.String fieldName;
	/**字段类型*/
	@Excel(name = "字段类型", width = 15)
    @ApiModelProperty(value = "字段类型")
    private java.lang.String fieldType;
	/**是否必填*/
	@Excel(name = "是否必填", width = 15)
    @ApiModelProperty(value = "是否必填")
    private java.lang.String isRequired;
	/**创建者*/
    @ApiModelProperty(value = "创建者")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新者*/
    @ApiModelProperty(value = "更新者")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**填入内容*/
	@Excel(name = "填入内容", width = 15)
    @ApiModelProperty(value = "填入内容")
    private java.lang.String fieldValue;
	/**字段顺序*/
	@Excel(name = "字段顺序", width = 15)
    @ApiModelProperty(value = "字段顺序")
    private java.lang.Integer filedSort;
	/**字段单位*/
	@Excel(name = "字段单位", width = 15)
    @ApiModelProperty(value = "字段单位")
    private java.lang.String fieldUnit;
	/**文本框类型*/
	@Excel(name = "文本框类型", width = 15)
    @ApiModelProperty(value = "文本框类型")
    private java.lang.String textType;
}

```

