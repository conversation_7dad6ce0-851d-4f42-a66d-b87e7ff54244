import Vue from 'vue'
import Vuex from 'vuex'
import user from '@/store/modules/user'
import getters from './getters'
import tabBar from '@/utils/tabBar.js'
Vue.use(Vuex)

const store = new Vuex.Store({
	state: {
		fontSizeSuperSmall: '20rpx',
		fontSizeSmall: '24rpx',
		fontSizeSuper26: '26rpx',
		fontSizeMedium: '28rpx',
		fontSizeLarge: '30rpx',
		fontSizeSuperLarge: '34rpx',
		tabBarList: []
	},
	mutations: {
		// mutation用于更改字体大小
		setNormalFontSize(state) {
			state.fontSizeSuperSmall = '20rpx'
			state.fontSizeSmall = '24rpx'
			state.fontSize26 = '26rpx'
			state.fontSizeMedium = '28rpx'
			state.fontSizeLarge = '30rpx'
			state.fontSizeSuperLarge = '34rpx'
		},
		setLargeFontSize(state) {
			state.fontSizeSuperSmall = '24rpx'
			state.fontSizeSmall = '28rpx'
			state.fontSize26 = '30rpx'
			state.fontSizeMedium = '32rpx'
			state.fontSizeLarge = '34rpx'
			state.fontSizeSuperLarge = '38rpx'
		},
		setRoleId(state, data) {
			if (data === 0) {
				state.tabBarList = tabBar.admin
			}
			if (data === 1) {
				state.tabBarList = tabBar.client
			}
			uni.setStorageSync('tabBarList', state.tabBarList)
		}
	},
	actions: {
		// action用来触发mutation
		updateNormalFontSize({
			commit
		}) {
			commit('setNormalFontSize')
		},
		updateLargeFontSize({
			commit
		}) {
			commit('setLargeFontSize')
		}
	},
	modules: {
		user
	},
	getters
})

export default store