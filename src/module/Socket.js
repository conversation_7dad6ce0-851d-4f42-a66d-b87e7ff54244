import Vue from 'vue';
import io from 'socket.io-client';


const origin = 'wss://wss.lke.cloud.tencent.com'
let path = '/v1/qbot/chat/conn/';
let initSocket = 1;
const mainToken = ''
import config from '@/config'
const baseUrl = config.baseUrl

// let tokenArr = window.webimToken || [];
// let mainToken = tokenArr.filter(item => {
//     return item.type !== 4;
// })[0].token;

// uni.request({
// 	url: 'http://10.110.115.11:8032/boot/common/miniapp/getToken', // 你的API地址
// 	method: 'GET',
// 	header: {
// 		'content-type': 'application/json' // 请求头
// 	},
// 	success: (res) => {
// 		console.log(res, 'socket-------------------------2'); // 
// 		let mainToken = res.data.result;

// 	},
// 	fail: (err) => {
// 		console.log('socket-------------------------3')
// 		console.error(err); // 请求失败的错误信息
// 	}
// });
// uni.connectSocket({
// 	url: 'wss://wss.lke.cloud.tencent.com/v1/qbot/chat/conn/?EIO=4&transport=websocket'
// });
// uni.onSocketOpen(function(res) {
// 	console.log(res, 'res==============================')
// 	console.log('WebSocket连接已打开！');
// });
let socket = io(origin, {
	path: path,
	transports: ['websocket', 'polling'],
	withCredentials: true,
	auth: async (cb) => {
		/* eslint-disable */
		try {
			console.log(baseUrl, 'base---------------')
			const url = baseUrl + '/boot/common/miniapp/getToken'
			if (initSocket === 1) {
				uni.request({

					url: url, // 你的API地址
					method: 'GET',
					header: {
						'content-type': 'application/json' // 请求头
					},
					success: (res) => {

						let mainToken = res.data.result;
						const token = mainToken || '';
						if (token) {
							cb({
								token: token
							});
						} else {
							cb({
								token: ''
							});
						}
						initSocket++;
					},
					fail: (err) => {

						console.error(err); // 请求失败的错误信息
					}
				});

			} else {
				const token = mainToken || '';
				cb({
					token: token
				});
				initSocket++;
			}
		} catch (e) {
			cb({
				token: ''
			});
			Vue.prototype.$message.error('获取token失败');
		}
	},
}); // 建立连接

console.log('socket===================', socket);
let systemEventEmit = (eventName, data) => {
	console.log(eventName, 'eventName')
	console.log(data, 'data')
	Vue.prototype.$eventHub.$emit(eventName, data);
};

// 监听
socket.on('connect', () => {
	console.log('connect');
	// 监听连接是否成功
	systemEventEmit('connect');
});
socket.on('connect_error', () => {
	systemEventEmit('connectError');
	socket.connect();
});
socket.on('error', (error) => {
	console.error('【error】----->', error)
	if (error && error.payload && error.payload.error) {
		Vue.prototype.$message(error.payload.error.message || '服务出错了！', 'error', null, 5000);
	}
	systemEventEmit('error');
});
socket.on('disconnect', (reason) => {
	// 监听连接异常中断
	systemEventEmit('disconnect', reason);
});
socket.io.on('error', (error) => {
	systemEventEmit('SocketError', error);
});
socket.io.on('reconnect', (attemptNumber) => {
	systemEventEmit('reconnect', `Reconnected to server after ${attemptNumber} attempts`);
});
socket.io.on('reconnect_attempt', (attemptNumber) => {
	systemEventEmit('reconnectAttempt', `Attempt number ${attemptNumber} to reconnect to server`);
});
socket.io.on('reconnect_error', (error) => {
	systemEventEmit('reconnectError', error);
});
socket.io.on('reconnect_failed', () => {
	systemEventEmit('reconnectFailed');
});

let Socket = {
	emit(eventName, params) {
		console.log('emit', eventName, params);
		let data = {
			payload: params
		};
		socket.emit(eventName, data);
	},
	on(eventName, cb) {
		console.log(eventName, 'eventName')
		console.log(cb, 'cb')
		let dataCB = (data) => {
			cb(data ? data.payload : data);
		};
		socket.on(eventName, dataCB);
	}
};

Vue.prototype.$socket = Socket;