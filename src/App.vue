<script>
import config from './config'
// import store from '@/store'
// import { getAccessToken } from '@/utils/auth'
// import global from '@/utils/global.js'

export default {
  globalData: {
    statusBarHeight: '', //导航栏高度
    capsuleTop: '', //胶囊距离顶部位置
    capsuleHeight: '' //胶囊高度
  },
  onShow() {
    // uni.hideTabBar({
    //   animation: false,
    //   success: () => {},
    //   fail: () => {
    //     console.log('隐藏失败')
    //   }
    // })
  },
  onLaunch() {
    this.initApp()
    // let custom = uni.getMenuButtonBoundingClientRect() //获取右上角胶囊信息
    let system = uni.getSystemInfoSync() //获取设备信息
    // let system = wx.getWindowInfo() //获取设备信息
    this.globalData.statusBarHeight = system.statusBarHeight + system.safeArea.top
    // this.globalData.capsuleTop = custom.top
  },
  methods: {
    // 初始化应用
    initApp() {
      // 初始化应用配置
      this.initConfig()
      // 检查用户登录状态
      // this.checkLogin()
    },
    initConfig() {
      //配置全局的存储globalData
      this.globalData.config = config
    }
  }
}
</script>

<style lang="scss">
@import '@/uni_modules/uview-ui/index.scss';
@import '@/static/scss/index.scss';
</style>
