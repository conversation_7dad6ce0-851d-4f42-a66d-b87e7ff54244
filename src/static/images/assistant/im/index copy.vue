<template>
  <view class="chat-container">
    <!-- 标题栏 -->
    <view class="title-bar">
      <text class="title">{{ currentChatPartner }}</text>
    </view>

    <!-- 消息容器 -->
    <scroll-view class="messages-container" scroll-y>
      <view v-for="message in messages" :key="message.id" class="message">
        <view class="message-bubble">{{ message.text }}</view>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-area">
      <textarea 
        v-model="newMessage" 
        placeholder="输入消息..." 
        @keydown.enter="sendMessage"
        show-confirm-bar="false"
        confirm-type="发送"
      ></textarea>
      <button class="attachment-button" @click="toggleAttachmentMenu">+</button>
    </view>

    <!-- 附件菜单 -->
    <view v-if="showAttachments" class="attachments-menu">
      <button @click="sendImage">发送图片</button>
      <button @click="sendFile">发送文件</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentChatPartner: '<PERSON>',
      messages: [],
      newMessage: '',
      showAttachments: false
    };
  },
  methods: {
    toggleAttachmentMenu() {
      this.showAttachments = !this.showAttachments;
    },
    sendMessage() {
      if (this.newMessage.trim() !== '') {
        this.messages.push({ id: Date.now(), text: this.newMessage });
        this.newMessage = '';
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },
    sendImage() {
      // 触发图片上传
    },
    sendFile() {
      // 触发文件上传
    },
    scrollToBottom() {
      const messagesContainer = this.$el.querySelector('.messages-container');
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  }
};
</script>

<style lang="scss">
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;

  .title-bar {
    background-color: #007aff;
    color: white;
    padding: 15px;
    text-align: center;
    .title {
      font-size: 18px;
      font-weight: bold;
    }
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    background-color: #ffffff;
  }

  .message {
    margin-bottom: 10px;
    display: flex;
    justify-content: flex-start;
    &.self {
      justify-content: flex-end;
    }
    .message-bubble {
      max-width: 70%;
      padding: 10px;
      border-radius: 10px;
      background-color: #e1ffc7;
      word-wrap: break-word;
    }
  }

  .input-area {
    display: flex;
    padding: 10px;
    background-color: #ffffff;
    border-top: 1px solid #e0e0e0;
    textarea {
      flex: 1;
      margin-right: 10px;
      border-radius: 5px;
      padding: 8px;
      border: 1px solid #ddd;
      font-size: 16px;
    }
    .attachment-button {
      width: 40px;
      height: 40px;
      background-color: #007aff;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .attachments-menu {
    display: flex;
    justify-content: space-around;
    padding: 10px;
    background-color: #f0f0f0;
    position: absolute;
    bottom: 60px; // 根据输入区高度调整
    width: 100%;
  }
}
</style>