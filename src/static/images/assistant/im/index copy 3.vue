<template>
	<view class="wrapper">
		<uni-nav-bar left-icon="back" @clickLeft="goBack" :title="currentChatPartner" backgroundColor="#286bff" color="#FFF">
		</uni-nav-bar>
		<scroll-view class="messages-container" scroll-y>
			<view class="messages">
				<view v-for="(message, index) in messages" :key="index" :class="['message', message.from]">
				  <view class="message-style">{{ message.text }}</view>
					<view class="image-wrapper">
						<image v-if="message.from === 'server'" src="/static/images/interaction/you.jpg"></image>
						<image v-else src="/static/images/interaction/me.jpg"></image>
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="input-area">
		  <textarea 
		    v-model="newMessage" 
		    placeholder="输入消息..." 
		    @keydown.enter="sendMessage"
		    show-confirm-bar="false"
		    confirm-type="发送"
		  ></textarea>
		  <button class="attachment-button" @click="toggleAttachmentMenu">+</button>
		</view>
	</view>
</template>

<script>
export default {
  data() {
    return {
      currentChatPartner: '小乔客服',
			messages: [
				// 动态消息列表
				{ text: '你好，有什么可以帮助你的吗？', from: 'server' },
				{ text: '你好，暂时还没有', from: 'client' },
				{ text: '相对论（英语：Theory of relativity）是关于时空和引力的理论，主要由爱因斯坦创立。20世纪初，相对论和量子力学的提出给物理学带来了革命性的变化，它们共同奠定了现代物理学的基础。相对论建立的“同时的相对性”、“四维时空”、“弯曲时空”等全新的时空观是人类对物理现象认识的一个飞跃', from: 'server' },
				{ text: '狭义相对论与广义相对论的描述的对象不同。传统上，在爱因斯坦提出相对论的初期，人们以所讨论的问题是否涉及非惯性参考系来作为狭义与广义相对论分类的标志。', from: 'client' }
			]
    };
  },
  methods: {
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style lang="scss">
	.wrapper {
		position: relative;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		
		.messages-container {
			padding: 30rpx 30rpx 5rpx 30rpx;
			.messages {
				display: flex;
				flex-direction: column;
				gap: 25rpx;
				
				.message {
					display: flex;
					max-width: 80%; /* 限制气泡最大宽度 */
					gap: 20rpx;
					image {
						height: 75rpx;
						width: 75rpx;
						border-radius: 10rpx;
					}
			
					&.server {
						align-self: flex-start;
						flex-direction: row-reverse;
						.message-style{
							padding: 20rpx;
							color: #333;
							font-size: 30rpx;
							background-color: rgba(255, 255, 255, 0.9);
							border-radius: 15rpx;
							word-break: break-word; /* 使长文本自动换行 */
						}
					}
					
					&.client {
						
						align-self: flex-end;
						.message-style{
							padding: 20rpx;
							color: #333;
							font-size: 30rpx;
							background-color: rgba(40, 107, 255, 0.2);
							border-radius: 15rpx;
							word-break: break-word; /* 使长文本自动换行 */
						}
					}
				}
			}
		}
		.input-area {
			position: absolute;
			bottom: 0rpx;
		  display: flex;
		  padding: 10px;
		  background-color: #ffffff;
		  border-top: 1px solid #e0e0e0;
		  textarea {
		    flex: 1;
		    margin-right: 10px;
		    border-radius: 5px;
		    padding: 8px;
		    border: 1px solid #ddd;
		    font-size: 16px;
		  }
		}
	}
</style>