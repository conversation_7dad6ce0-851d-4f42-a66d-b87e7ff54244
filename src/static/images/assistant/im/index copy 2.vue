<template>
  <view class="chat-container">
    <!-- 聊天内容显示区域 -->
    <view class="chat-content">
      <!-- 聊天记录显示 -->
    </view>

    <!-- 输入框和附加功能按钮 -->
    <view class="chat-input-container">
      <input type="text" class="chat-input" />
      <button @click="toggleExtraOptions" class="plus-button">+</button>
    </view>

    <!-- 弹出区域 -->
    <view class="extra-options" v-if="showExtraOptions">
      <!-- 可以添加的额外功能，例如图片、文件等 -->
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showExtraOptions: false
    };
  },
  methods: {
    toggleExtraOptions() {
      this.showExtraOptions = !this.showExtraOptions;
    }
  }
};
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 100vh;
}

.chat-content {
  flex: 1;
  overflow-y: auto;
}

.chat-input-container {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f5f5f5;
}

.chat-input {
  flex: 1;
  border: none;
  border-radius: 4px;
  padding: 10px;
}

.plus-button {
  margin-left: 10px;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 10px;
}

.extra-options {
  background-color: #fff;
  padding: 10px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
}
</style>