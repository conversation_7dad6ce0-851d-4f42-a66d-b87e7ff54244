<template>
	<view class="wrapper">
		<scroll-view :scroll-into-view="scrollIntoId" class="messages-container" scroll-y>
			<view v-for="(message, index) in messages" :key="index" :id="'chat-' + index"
				:class="['message', message.from]">
				<view class="message-style">{{ message.text }}</view>
				<view class="image-wrapper">
					<image v-if="message.from === 'server'" src="/static/images/interaction/you.jpg"></image>
					<image v-else src="/static/images/interaction/me.jpg"></image>
				</view>
			</view>
			<view class="scroll-wrapper-bottom" id="scroll-wrapper-bot" style="height: 20rpx;"></view>
		</scroll-view>
		<view class="bottom-area">
			<view class="input-area">
				<textarea auto-height v-model="newMessage" placeholder="输入消息..." maxlength="100"
					@keydown.enter="sendMessage" confirm-type="send" @confirm="sendMessage"
					:show-confirm-bar='false'></textarea>
				<image class="plus-button" src="../../../static/images/chat/plus.svg" mode="aspectFit" @click="goBack">
				</image>
			</view>
			<view class="options" v-if="showOptions">
				dadajsidojaiod
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentChatPartner: '小乔客服',
				newMessage: '',
				scrollIntoId: 'scroll-wrapper-bot',
				showOptions: false,
				messages: [
					// 动态消息列表
					{
						text: '你好，有什么可以帮助你的吗？',
						from: 'server'
					},
					{
						text: '你好，暂时还没有',
						from: 'client'
					},
					{
						text: '相对论（英语：Theory of relativity）是关于时空和引力的理论，主要由爱因斯坦创立。20世纪初，相对论和量子力学的提出给物理学带来了革命性的变化，它们共同奠定了现代物理学的基础。相对论建立的“同时的相对性”、“四维时空”、“弯曲时空”等全新的时空观是人类对物理现象认识的一个飞跃',
						from: 'server'
					},
					{
						text: '狭义相对论与广义相对论的描述的对象不同。传统上，在爱因斯坦提出相对论的初期，人们以所讨论的问题是否涉及非惯性参考系来作为狭义与广义相对论分类的标志。',
						from: 'client'
					},
					{
						text: '狭义相对论与广义相对论的描述的对象不同。传统上，在爱因斯坦提出相对论的初期，人们以所讨论的问题是否涉及非惯性参考系来作为狭义与广义相对论分类的标志。',
						from: 'client'
					},
					{
						text: '狭义相对论与广义相对论的描述的对象不同。传统上，在爱因斯坦提出相对论的初期，人们以所讨论的问题是否涉及非惯性参考系来作为狭义与广义相对论分类的标志。',
						from: 'client'
					},
					{
						text: '狭义相对论与广义相对论的描述的对象不同。传统上，在爱因斯坦提出相对论的初期，人们以所讨论的问题是否涉及非惯性参考系来作为狭义与广义相对论分类的标志。',
						from: 'client'
					}
				]
			};
		},
		onLoad() {
			uni.onKeyboardHeightChange(res => {
				this.setData({
					KeyboardHeight: res.height
				})
			})
		},
		methods: {
			sendMessage() {
				this.messages.push({
					text: this.newMessage,
					from: 'client'
				})
				setTimeout(() => {
					this.scrollIntoId = 'chat-' + (this.messages.length - 1);
				}, 300)
				this.goBack()
				this.newMessage = ''
				
			},
			goBack() {
				// uni.navigateBack();
				uni.request({
					url: 'https://wss.lke.cloud.tencent.com/v1/qbot/chat/sse', // 你的API地址
					method: 'POST',
					header: {
						'content-type': 'application/json' // 请求头
					},
					data: {
						// 在这里放置你要发送的数据
						content: this.newMessage,
						bot_app_key: "bZLYKMwb",
						visitor_biz_id: "test",
						session_id: "test",
					},
					success: (res) => {
						console.log(res, 'res'); // 
						console.log(res.data, 'data'); // 
						const event = JSON.parse(res.data);
						if (event.type === 'reply') {
							let data = event.payload;
							console.log(data, '09999')
						}
					},
					fail: (err) => {
						console.log(1111)
						console.error(err); // 请求失败的错误信息
					}
				});
			},
			toggleOptions() {
				this.showOptions = !this.showOptions;
			}
		}
	}
</script>

<style lang="scss">
	uni-page-body {
		height: 100%;
	}

	page {
		height: 100%;
	}

	.wrapper {
		display: flex;
		background-color: #FFF;
		flex-direction: column;
		width: 100%;
		height: 100%;
		// background-color: red;
		overflow: hidden;
		padding-bottom: 50rpx;

		.messages-container {
			padding: 0rpx 30rpx;
			background-color: #f1f1f1;
			// height: 50rpx;
			flex: 1;
			/* 让.messages-container占据剩余空间 */
			overflow-y: auto;

			/* 允许垂直滚动 */
			.message {
				margin-top: 20rpx;
				display: flex;
				justify-content: flex-end;
				width: 100%;
				/* 限制气泡最大宽度 */
				gap: 20rpx;

				image {
					height: 75rpx;
					width: 75rpx;
					border-radius: 10rpx;
				}

				&.server {
					flex-direction: row-reverse;

					.message-style {
						transition: all 0.3s ease-in-out;
						max-width: 73%;
						padding: 20rpx;
						color: #333;
						font-size: 30rpx;
						background-color: rgba(255, 255, 255, 0.9);
						border-radius: 15rpx;
						word-break: break-word;
						/* 使长文本自动换行 */
					}
				}

				&.client {
					.message-style {
						transition: all 0.3s ease-in-out;
						max-width: 73%;
						padding: 20rpx;
						color: #333;
						font-size: 30rpx;
						background-color: rgba(40, 107, 255, 0.2);
						border-radius: 15rpx;
						word-break: break-word;
						/* 使长文本自动换行 */
					}
				}
			}
		}

		.bottom-area {
			display: flex;
			flex-direction: column;

			.input-area {
				display: flex;
				padding: 15rpx 20rpx;
				align-items: flex-end;
				gap: 20rpx;
				background-color: #ffffff;
				border-top: 1rpx solid #e0e0e0;

				textarea {
					flex: 1;
					border-radius: 10rpx;
					// padding: 8px;
					border: 1rpx solid #ddd;
					font-size: 32rpx;
					min-height: 70rpx;
					line-height: 32rpx;
				}

				.plus-button {
					background-color: white;
					height: 70rpx;
					width: 70rpx;
				}
			}

			.options {
				display: flex;
				height: 300rpx;
				background-color: #FFF;
				padding: 10rpx;
				// box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
				transition: all 0.5s ease-in-out;
			}
		}
	}
</style>