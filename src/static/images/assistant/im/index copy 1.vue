<template>
	<view class="wrapper">
		<uni-nav-bar left-icon="back" @clickLeft="goBack" :title="currentChatPartner" backgroundColor="#286bff" color="#FFF"></uni-nav-bar>
	</view>
</template>

<script>
export default {
  data() {
    return {
      currentChatPartner: '小乔客服',
			messages: [
				// 动态消息列表
				{ text: '你好，有什么可以帮助你的吗？', isMine: false },
			],
			
    };
  },
  methods: {
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style lang="scss">
	.wrapper {
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
	}
</style>
