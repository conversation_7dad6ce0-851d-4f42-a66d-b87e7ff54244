// 应用全局配置
module.exports = {
  // baseUrl: 'http://api-dashboard.yudao.iocoder.cn',
  // baseUrl: 'http://localhost:48080',
  // baseUrl: 'http://*************:48180',
  // baseUrl: 'http://************:42877',
  // 判断是否是开发环境还是生产环境
  baseUrl:
    // process.env.NODE_ENV === 'development' ? 'https://jyxq.jialangshuchan.cn' : window.location.origin,
    process.env.NODE_ENV === 'development' ? 'http://*************:8032' : window.location.origin,
    // process.env.NODE_ENV === 'development' ? 'http://************:42877' : window.location.origin,
  // baseApi: '/app-api',
  baseApi: '/boot',
  staticDomainURL: 'http://************:42877/boot/sys/common/static/',
  // 应用信息
  appInfo: {
    // 应用名称
    name: 'xiao<PERSON>ao-app',
    // 应用版本
    version: '1.0.0',
    // 应用logo
    logo: '/static/logo.png',
    // 官方网站
    site_url: 'https://iocoder.cn',
    // 政策协议
    agreements: [
      {
        title: '隐私政策',
        url: 'https://iocoder.cn'
      },
      {
        title: '用户服务协议',
        url: 'https://iocoder.cn'
      }
    ]
  }
}
