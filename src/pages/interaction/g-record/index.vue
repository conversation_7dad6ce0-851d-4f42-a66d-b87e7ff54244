<template>
	<view class=""  :style="{paddingTop:  Props.statusBarHeight + 'px'}">
		<navbar :Props="Props" :title='headtitle' @black="black"></navbar>
		<view class="boxs">
			<view class="content">
				<view class="search-bar">
					<input :style="{ fontSize: fontSizeLarge }" @input="emptyButton" maxlength="18" v-model="searchInput"
						class="search" focus placeholder="请输入搜索关键词" />
					<image class="search-icon" src="/static/images/interaction/search.svg" mode="aspectFit"
						@click="getComplaintList"></image>
				</view>
				<view class="reports" v-for="(report, index) in reportList" :key="index"
					@click="goto('g-moreinfo/index', report)">
					<view class="report">
						<image class="record_bg" src="@/static/images/interaction/record_bg.png" mode=""></image>
						<view :style="{ fontSize: fontSizeLarge }" class="type">{{report.type_dictText}}</view>
						<view :style="{ fontSize: fontSizeSmall }" class="date">{{report.createTime}}</view>
						<view :style="{ fontSize: fontSizeMedium }" class="text">{{report.content}}</view>
						<view :class="report.isFeedback == 1 ? 'isFeedback' : 'noisFeedback'" >{{report.isFeedback_dictText}}</view>
						<image v-show="report.type == 2" class="icon" src="@/static/images/interaction/gnx.png" mode=""></image>
						<image v-show="report.type == 1" class="icon" src="@/static/images/interaction/yhty.png" mode=""></image>
						<image v-show="report.type == 3" class="icon" src="@/static/images/interaction/bsxl.png" mode="">
						</image>
						<image v-show="report.type == 4" class="icon" src="@/static/images/interaction/fwtd.png" mode="">
						</image>
					</view>
				</view>
			</view>
		</view>

	</view>

</template>

<script>
	import navbar from '@/components/Navbar/index.vue'
	import {
		getComplaintPage,
		deleteComplaintPage
	} from "@/api/record/record.js"
	export default {
		components: {
			navbar
		},
		onReachBottom() {
			this.pageNo += 1
			this.getMoreList()
		},
		onLoad: function() {
			this.Props.statusBarHeight = getApp().globalData.statusBarHeight
			this.Props.capsuleTop = getApp().globalData.capsuleTop
		},
		data() {
			return {
				searchInput: '',
				reportList: [{
					type: '功能性',
					createTime: '2022-08-21 18:09',
					content: '该小程序评论页面延迟非常低，帮办业务效率高，继续努力',
					telNumber: '13389975361',
					id: '001'
				}],
				selectList: ['用户体验', '功能性', '办事效率', '服务态度'],
				searchList: [],
				pageNo: 1,
				pageSize: 10,
				filterList: [],
				showButtons: [],
				msgType: '',
				messageText: '',
				headtitle: '投诉举报记录',
				Props: {
					imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: "", //导航高度(动态获取传参)
					bgColor: "", //导航栏背景色,不传参则默认#9CF
					capsuleTop: "", //胶囊顶部距离(动态获取传参)
					textColor: "", //导航标题字体颜色(不传默认#FFF)
					iconColor: "", //icon图标颜色(不传默认#FFF)
					blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
					backText: "", //默认字体(返回)
				},
			}
		},
		methods: {
			// initData() {
			// 	const param = {
			// 		pageNo: this.pageNo,
			// 		pageSize: this.pageSize
			// 	}
			// 	const res = getComplaintPage()
			// 	console.log(res)
			// },
			goto(url, report) {
				const sendItem = this.reportList.find(item => item.id === report.id)
				uni.navigateTo({
					url: `${url}?id=${sendItem.id}&type=${sendItem.type}&createTime=${sendItem.createTime}&phone=${sendItem.telNumber}&text=${sendItem.content}&isFeedback_dictText=${sendItem.isFeedback_dictText}&updateTime=${sendItem.updateTime}&feedbackContent=${sendItem.feedbackContent}`
				})
			},
			async getMoreList() {
				const param = {
					pageNo: this.pageNo,
					pageSize: this.pageSize
				}
				const res = await getComplaintPage(param)
				const newList = res.result.records
				this.reportList = [...this.reportList, ...newList]
			},
			emptyButton(event) {
				this.showButtons = []
			},
			toggleButtons(index) {
				this.$set(this.showButtons, index, !this.showButtons[index]);
			},
			textFilter(text) {
				if (text.length > 36) {
					return text.substring(0, 37) + '...'
				}
				return text
			},
			async getComplaintList() {
				this.pageNo = 1
				// let retype = this.selectList.indexOf(this.searchInput) === -1 ? '' : this.selectList.indexOf(this.searchInput)
				const param = {
					pageNo: this.pageNo,
					pageSize: this.pageSize,
					// type: retype,
					content: this.searchInput,
					// 	telNumber: this.searchInput,
				}
				console.log(param)
				const res = await getComplaintPage(param)
				this.reportList = res.result.records
				for (let i = 0; i < this.reportList.length; i++) {
					this.reportList[i].type = this.selectList[this.reportList[i].type - 1]
				}
			}
		},
		onShow() {
			this.getComplaintList()
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			},
			fontSizeSuperLarge() {
				return this.$store.state.fontSizeSuperLarge; // 从Vuex获取大号字体大小
			},
			filteredReports() {
				return this.reportList.filter(report => {
					return report.content.includes(this.searchInput) ||
						report.type.includes(this.searchInput) ||
						report.createTime.includes(this.searchInput) ||
						report.telNumber.includes(this.searchInput);
				});
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f5f6f7;
	}
	.boxs {
		background: #F4F8FC;
		margin: 32rpx;
	}
	.content {
		background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 100%);
		box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 32rpx;
		// padding: 32rpx;
		height: 100%;
		width: 100%;
		display: flex;
		gap: 25rpx;
		flex-direction: column;
		align-items: center;
		padding: 40rpx 20rpx;

		.search-bar {
			position: relative;
			width: 100%;
			height: 80rpx;
			margin-bottom: 10rpx;

			.search {
				height: 100%;
				padding: 20rpx;
				background-color: #FFF;
				border: 2rpx solid rgba(255, 255, 255, 0.8);
				border-radius: 30rpx;
				box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
			}

			.search-icon {
				position: absolute;
				height: 60rpx;
				width: 60rpx;
				right: 3%;
				top: 14%;
			}
		}

		.reports {
			background-color: #FFF;
			display: flex;
			flex-direction: column;
			width: 100%;
			height: 188rpx;
			gap: 30rpx;
			position: relative;
			border-bottom: 2rpx solid #E7F0FF;
			.report {
				.record_bg {
					width: 424rpx;
					height: 180rpx;
					position: absolute;
				}
				
				.isFeedback {
					text-align: center;
					position: absolute;
					font-weight: 500;
					font-size: 24rpx;
					color: #FFFFFF;
					line-height: 44rpx;
					top: 16rpx;
					right: 14rpx;
					width: 98rpx;
					height: 44rpx;
					background: #0EC060;
					border-radius: 4rpx 22rpx 4rpx 22rpx;
				}
				.noisFeedback {
					text-align: center;
					position: absolute;
					font-weight: 500;
					font-size: 24rpx;
					color: #FFFFFF;
					line-height: 44rpx;
					top: 16rpx;
					right: 14rpx;
					width: 98rpx;
					height: 44rpx;
					background: #F01A1A;
					border-radius: 4rpx 22rpx 4rpx 22rpx;
				}
				.type {
					position: absolute;
					font-weight: 500;
					font-size: 32rpx;
					color: #181818;
					line-height: 45rpx;
					font-style: normal;
					top: 16rpx;
					left: 77rpx;
				}
				
				.date {
					position: absolute;
					font-weight: 400;
					font-size: 24rpx;
					color: #ACACAC;
					line-height: 33rpx;
					font-style: normal;
					top: 22rpx;
					right: 128rpx;
				}
				
				.text {
					position: absolute;
					font-weight: 400;
					font-size: 28rpx;
					color: #666666;
					line-height: 40rpx;
					font-style: normal;
					left: 14rpx;
					top: 78rpx;
				}
				
				.icon {
					width: 48rpx;
					height: 48rpx;
					position: absolute;
					top: 14rpx;
					left: 14rpx;
				}
			}

		}
	}
</style>