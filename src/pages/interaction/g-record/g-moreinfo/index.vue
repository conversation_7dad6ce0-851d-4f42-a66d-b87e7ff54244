<template>
	<view class="" :style="{paddingTop:  Props.statusBarHeight + 'px'}">
		<navbar :Props="Props" :title='headtitle' @black="black"></navbar>
		<view class="boxs">
			<view class="content">
				<image class="g_background" src="@/static/images/interaction/g_background.png" mode=""></image>
				<view class="report bg-card">
					<view :style="{ fontSize: fontSizeMedium }" class="cell type">
						<view>类型:</view>
						<view>{{ reportItem.type }}</view>
					</view>
					<view :style="{ fontSize: fontSizeMedium }" class="cell phone">
						<view>手机号:</view>
						<view>{{ reportItem.phone }}</view>
					</view>
					<view :style="{ fontSize: fontSizeMedium }" class="cells">
						<view class="text">
							投诉内容：
						</view>
						<view class="text">
							<textarea disabled placeholder-class="textarea" class="u-border" placeholder="" rows="2"
								v-model="reportItem.text"></textarea>
						</view>
					</view>
					<view :style="{ fontSize: fontSizeMedium }" class="cell">
						<view>投诉时间:</view>
						<view>{{ reportItem.createTime }}</view>
					</view>
					<view :style="{ fontSize: fontSizeMedium }" class="cell">
						<view>是否反馈:</view>
						<view>{{ reportItem.isFeedback_dictText }}</view>
					</view>
					<view :style="{ fontSize: fontSizeMedium }" class="cells">
						<view class="text">反馈结果:</view>
						<view>
							<textarea :disabled='disabled' placeholder-class="textarea" class="u-border"
								placeholder="请输入反馈结果" rows="2" v-model="reportItem.feedbackContent"></textarea>
						</view>
					</view>
					<view :style="{ fontSize: fontSizeMedium }" class="cell">
						<view>反馈时间:</view>
						<view>{{ reportItem.updateTime }}</view>
					</view>
				</view>
				<view class="btn-box">
					<view :style="{ fontSize: fontSizeMedium }" class="feedback" v-show="!disabled" @click="validateInput()">反馈</view>
					<view :style="{ fontSize: fontSizeMedium }" class="return" @click="back">返回</view>
				</view>
			</view>
		</view>

		<uni-popup ref="message" type="message">
			<uni-popup-message :type="msgType" :message="messageText" :duration="2000"></uni-popup-message>
		</uni-popup>
	</view>
</template>

<script>
	import {
		createFeedback
	} from '@/api/record/record.js'
	import navbar from '@/components/Navbar/index.vue'
	export default {
		components: {
			navbar
		},
		onLoad: function(report) {
			this.Props.statusBarHeight = getApp().globalData.statusBarHeight
			this.Props.capsuleTop = getApp().globalData.capsuleTop
			this.reportItem = report
			if (this.reportItem.updateTime == 'null') {
				this.reportItem.updateTime = ''
			}
			if (this.reportItem.feedbackContent == 'null') {
				this.reportItem.feedbackContent = ''
			}
			if (this.reportItem.isFeedback_dictText == '已反馈') {
				this.disabled = true
			}
		},
		data() {
			return {
				disabled: false,
				reportItem: {},
				feedback: '',
				headtitle: '投诉举报',
				msgType: '',
				messageText: '',
				Props: {
					imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: "", //导航高度(动态获取传参)
					bgColor: "", //导航栏背景色,不传参则默认#9CF
					capsuleTop: "", //胶囊顶部距离(动态获取传参)
					textColor: "", //导航标题字体颜色(不传默认#FFF)
					iconColor: "", //icon图标颜色(不传默认#FFF)
					blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
					backText: "", //默认字体(返回)
				},
			}
		},
		methods: {
			back() {
				uni.navigateBack();
			},
			validateInput() {
				this.inputError = false;
				if (!this.reportItem.feedbackContent) {
					this.msgType = 'warn'
					this.messageText = '请输入反馈结果！'
				} else {
					// this.createReport()
					const param = {
						id: this.reportItem.id,
						feedbackContent: this.reportItem.feedbackContent,
					}
					createFeedback(param).then(() => {
						this.msgType = 'succuss'
						this.messageText = '您的投诉举报我们已经收到，感谢您的宝贵意见！'
						setTimeout(() => {
							uni.navigateBack();
						}, 1000)
					})

				}
				this.$refs.message.open()
			},
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			},
			fontSizeSuperLarge() {
				return this.$store.state.fontSizeSuperLarge; // 从Vuex获取大号字体大小
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f5f6f7;
	}

	.boxs {
		margin: 32rpx;
	}

	.content {

		padding: 30rpx 40rpx;
		height: 100%;
		width: 100%;
		display: flex;
		gap: 15rpx;
		flex-direction: column;
		align-items: center;
		position: relative;
		background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 100%);
		box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 32rpx;

		.g_background {
			width: 686rpx;
			height: 180rpx;
			position: absolute;
			top: 0;
		}

		.cell {
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
			padding-bottom: 20rpx;
			margin-bottom: 20rpx;
		}
		.cells {
			margin-bottom: 20rpx;
			.text {
				margin-bottom: 20rpx;
			}
		}
		.report {
			display: flex;
			flex-direction: column;

			.type {
				position: absolute;
				width: 610rpx;
			}

			.phone{
				padding-top: 100rpx;
			}

			.text {
				width: 100%;
				color: #333;
			}
		}
	}

	.btn-box {
		width: 750rpx;
		display: flex;
		justify-content: space-around;
		margin-top: 30rpx;

		::v-deep button {
			width: 150rpx;
		}
	}

	.u-border {
		width: 610rpx;
		height: 207rpx;
		background: #F5F7FB;
		border-radius: 20rpx;
		padding: 20rpx;
	}

	.feedback {
		width: 282rpx;
		height: 80rpx;
		background: #057FFE;
		border-radius: 8rpx;
		font-weight: 500;
		font-size: 32rpx;
		color: #FFFFFF;
		line-height: 80rpx;
		letter-spacing: 2px;
		text-align: center;
		font-style: normal;
	}

	.return {
		width: 282rpx;
		height: 80rpx;
		border-radius: 8rpx;
		border: 2rpx solid #057FFE;
		font-weight: 500;
		font-size: 34rpx;
		color: #057FFE;
		line-height: 80rpx;
		letter-spacing: 2px;
		text-align: center;
		font-style: normal;
	}
</style>