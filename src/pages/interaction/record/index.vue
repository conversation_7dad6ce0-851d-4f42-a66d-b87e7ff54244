<template>
	<view class=""  :style="{paddingTop:  Props.statusBarHeight + 'px'}">
		<navbar :Props="Props" :title='headtitle' @black="black"></navbar>
		<view class="box">
			<view class="content">
				<view class="reports" v-for="(report, index) in reportList" :key="index">
					<image class="record_bg" src="@/static/images/interaction/record_bg.png" mode=""></image>
					<view :style="{ fontSize: fontSizeLarge }" class="type">{{report.type_dictText}}</view>
					<view :style="{ fontSize: fontSizeSmall }" class="date">{{report.createTime}}</view>
					<view :style="{ fontSize: fontSizeMedium }" class="text">{{report.content}}</view>
					<image v-show="report.type == 2" class="icon" src="@/static/images/interaction/gnx.png" mode="">
					</image>
					<image v-show="report.type == 1" class="icon" src="@/static/images/interaction/yhty.png" mode="">
					</image>
					<image v-show="report.type == 3" class="icon" src="@/static/images/interaction/bsxl.png" mode="">
					</image>
					<image v-show="report.type == 4" class="icon" src="@/static/images/interaction/fwtd.png" mode="">
					</image>
				</view>
				<uni-load-more iconType="circle" :status="loadMoreStatus" />
			</view>
		</view>
	</view>
</template>

<script>
	import navbar from '@/components/Navbar/index.vue'
	import {
		getComplaintList
	} from "@/api/interaction/report.js"
	export default {
		components: {
			navbar
		},
		onLoad: function() {},
		data() {
			return {
				params: {
					pageNo: 1,
					pageSize: 10,
					total: 0
				},
				headtitle: '投诉举报记录',
				Props: {
					imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: "", //导航高度(动态获取传参)
					bgColor: "", //导航栏背景色,不传参则默认#9CF
					capsuleTop: "", //胶囊顶部距离(动态获取传参)
					textColor: "", //导航标题字体颜色(不传默认#FFF)
					iconColor: "", //icon图标颜色(不传默认#FFF)
					blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
					backText: "", //默认字体(返回)
				},
				reportList: [],
				loadMoreStatus: 'more',
			}
		},
		onLoad() {
			uni.$on('loadMore', this.loadMore)
			this.getList()
			this.Props.statusBarHeight = getApp().globalData.statusBarHeight
			this.Props.capsuleTop = getApp().globalData.capsuleTop
		},
		methods: {
			async getList() {
				const params = {
					pageNo: this.params.pageNo,
					pageSize: this.params.pageSize,
				}
				const res = await getComplaintList(params)
				console.log(res, '09999')
				this.reportList = res?.result?.records
				this.params.total = res?.result?.total
				//加载更多状态处理
				this.setloadMoreStatus()
			},
			//加载更多状态处理
			setloadMoreStatus() {
				console.log(1111)
				if (this.params.total / this.params.pageSize > this.params.pageNo) {
					this.loadMoreStatus = 'more'
				} else {
					this.loadMoreStatus = 'noMore'
				}
			},
			//加载更多
			loadMore() {
				if (this.params.total / this.params.pageSize > this.params.pageNo) {
					this.loadMoreStatus = 'loading'
					this.params.pageNo++
					this.getList()
				}
			}
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f5f6f7;
	}

	.box {
		margin: 32rpx;

		.content {
			background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 100%);
			box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
			border-radius: 32rpx;
			// padding: 32rpx;
			height: 100%;
			width: 100%;
			display: flex;
			gap: 25rpx;
			flex-direction: column;
			align-items: center;
			padding: 40rpx 20rpx;

			.reports {
				background-color: #FFF;
				display: flex;
				flex-direction: column;
				width: 100%;
				height: 188rpx;
				gap: 30rpx;
				position: relative;
				border-bottom: 2rpx solid #E7F0FF;

				.record_bg {
					width: 424rpx;
					height: 180rpx;
					position: absolute;
				}

				.type {
					position: absolute;
					font-weight: 500;
					font-size: 32rpx;
					color: #181818;
					line-height: 45rpx;
					font-style: normal;
					top: 16rpx;
					left: 77rpx;
				}

				.date {
					position: absolute;
					font-weight: 400;
					font-size: 24rpx;
					color: #ACACAC;
					line-height: 33rpx;
					font-style: normal;
					top: 22rpx;
					right: 14rpx;
				}

				.text {
					position: absolute;
					font-weight: 400;
					font-size: 28rpx;
					color: #666666;
					line-height: 40rpx;
					font-style: normal;
					left: 14rpx;
					top: 78rpx;
				}

				.icon {
					width: 48rpx;
					height: 48rpx;
					position: absolute;
					top: 14rpx;
					left: 14rpx;
				}
			}
		}
	}
</style>