<template>
  <view>
    <div class="back_box" @click="goBack">
      <img src="/static/images/admin/xq_back.png" alt="" />
      返回
    </div>
    <div class="main_bxo" v-if="!userInfo.isAssistManger && !userInfo.isAssist">
      <view v-for="(item, index) in list" :key="index" class="item">
        <view class="code">{{ item.recordName }}({{ item.recordId }})</view>
        <waves>
          <view class="item_box" @click="go(item)">
            <img
              class="avatar"
              v-if="item.assistantAvatar"
              :src="imgBaseUrl + item.assistantAvatar"
            />
            <img class="avatar" v-else src="/static/images/mine/tx.png" />
            <view class="content">
              <view class="top">
                <view class="name">{{ item.assistantName }}</view>
                <view class="time">{{ item.createTime || todayTime }}</view></view
              >
              <view class="text">
                <text> {{ item.msgInfo || '' }} </text>
              </view>
            </view>
          </view>
        </waves>
      </view>
      <view>
        <uni-load-more iconType="circle" :status="loadMoreStatus" />
      </view>
    </div>
    <div class="main_bxo" v-else-if="userInfo.isAssistManger || userInfo.isAssist">
      <view v-for="(item, index) in list" :key="index" class="item">
        <view class="code">{{ item.recordId }}</view>
        <waves>
          <view class="item_box" @click="go(item)">
            <!-- <image
              class="avatar"
              mode="aspectFill"
              :src="imgBaseUrl + item.userAvatar && '/static/images/mine/tx.jpg'"
            >
            </image> -->
            <img
              class="avatar"
              v-if="item.assistantAvatar"
              :src="imgBaseUrl + item.assistantAvatar"
            />
            <img class="avatar" v-else src="/static/images/mine/tx.png" />
            <view class="content">
              <view class="top">
                <view class="name">{{ item.userNickName }}</view>
                <view class="time">{{ item.createTime || todayTime }}</view></view
              >
              <view class="text">
                <text> {{ item.msgInfo || '' }} </text>
                <!-- <u-badge
                  v-if="item.totalCount"
                  :isDot="true"
                  type="error"
                  max="99"
                  :value="item.totalCount"
                ></u-badge> -->
              </view>
            </view>
          </view>
        </waves>
      </view>
      <view>
        <uni-load-more iconType="circle" :status="loadMoreStatus" />
      </view>
    </div>
  </view>
</template>

<script>
// import { debounce } from 'lodash'
import navbar from '@/components/Navbar/index.vue'
import waves from '@/components/waves/waves.vue'
import { getPageMsgListd, getHelpList } from '@/api/im/index.js'
import config from '@/config'
export default {
  components: {
    navbar,
    waves
  },
  data() {
    return {
      userInfo: {},
      loginType: 1,
      list: [],
      imgBaseUrl: config.baseUrl + '/boot/',
      loadMoreStatus: 'more',
      pageNo: 1,
      pageSize: 10
    }
  },
  watch: {},
  async onShow() {
    this.userInfo =
      typeof this.$store.state.user.userInfo == 'string'
        ? JSON.parse(this.$store.state.user.userInfo)
        : this.$store.state.user.userInfo
    this.loginType = uni.getStorageSync('loginType')

    this.pageNo = 1
    this.list = []
    this.getList()
  },
  onLoad() {},
  onReachBottom() {
    if (this.loadMoreStatus !== 'more') return
    this.loadMoreStatus = 'loading'
    this.pageNo++
    this.getList()
  },
  computed: {
    // 当前时间 小时:分  补0
    todayTime() {
      let date = new Date()
      let hour = date.getHours()
      let min = date.getMinutes()
      function _fullZeroToTwo(num) {
        return num < 10 ? '0' + num : num
      }
      return `${_fullZeroToTwo(hour)}:${_fullZeroToTwo(min)}`
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    go(item) {
      if (!item.assistantName) {
        // 暂无对话
        uni.showToast({
          title: '暂无对话',
          icon: 'none'
        })
        return
      }
      uni.navigateTo({
        url:
          '/pages/interaction/im/chat?recordId=' +
          item.recordId +
          '&assistantId=' +
          this.userInfo.id +
          '&title=' +
          item.assistantName +
          '&bbyAvatar=' +
          item.assistantAvatar
      })
    },
    async getList() {
      let res = null
      if (!this.userInfo.isAssistManger && !this.userInfo.isAssist) {
        res = await getPageMsgListd({
          pageNo: this.pageNo,
          pageSize: this.pageSize
        })
      }
      if (this.userInfo.isAssistManger || this.userInfo.isAssist) {
        res = await getHelpList({
          pageNo: this.pageNo,
          pageSize: this.pageSize
        })
      }
      if (res.result.records.length < this.pageSize) {
        this.loadMoreStatus = 'noMore'
      } else {
        this.loadMoreStatus = 'more'
      }
      this.list = this.list.concat(res.result.records)
      const uniqueMap = new Map()
      this.list.forEach((item) => {
        if (
          !uniqueMap.has(item.recordId) ||
          uniqueMap.get(item.recordId).createTime < item.createTime
        ) {
          uniqueMap.set(item.recordId, item)
        }
      })
      this.list = Array.from(uniqueMap.values())
    }
  }
}
</script>

<style>
page {
  background-color: #f4f8fc;
  padding: 0 30rpx;
}
</style>
<style lang="scss" scoped>
.back_box {
  height: 60rpx;
  display: flex;
  align-items: center;
  padding: 20rpx 0 0;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  img {
    width: 28rpx;
    height: 40rpx;
    transform: rotate(180deg);
    margin-right: 10rpx;
  }
}
.main_bxo {
  // width: 686rpx;
  margin: 30rpx auto;
  background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
  box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
  border-radius: 32rpx;
  padding: 30rpx 0rpx;
  .item {
    padding: 30rpx 0rpx;
    border-bottom: 2rpx solid #e7f0ff;
    .code {
      text-align: left;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #acacac;
      margin-bottom: 10rpx;
      padding: 0 30rpx;
    }
    .item_box {
      display: flex;
      align-items: center;
      padding: 0 30rpx;
      position: relative;
    }
    .avatar {
      min-width: 100rpx;
      max-width: 100rpx;
      height: 100rpx;
      border-radius: 16rpx;
      margin-right: 20rpx;
    }
    .content {
      flex: 1;
      max-width: 500rpx;
      overflow: hidden;
      // white-space: nowrap;
      text-overflow: ellipsis;
      // width: 300rpx;
    }
    .top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
      .name {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32rpx;
        color: #181818;
      }
      .time {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #acacac;
      }
    }
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
