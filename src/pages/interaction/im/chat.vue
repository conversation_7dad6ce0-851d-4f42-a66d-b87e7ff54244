<template>
  <div>
    <div class="main_bxo" v-if="!isCall">
      <div class="headtitle">
        <img src="/static/back.png" alt="" @click.stop="goBack" />
        {{ headtitle }}
      </div>
      <div class="content">
        <div class="item" v-for="(item, index) in list" :key="index">
          <div
            class="bby"
            v-if="(loginType == 1 && item.isMyself == 0) || (loginType == 0 && item.isMyself == 0)"
          >
            <image
              class="avatar"
              mode="aspectFill"
              :src="getAvatar(item.isMyself)"
              v-if="!getAvatar(item.isMyself)"
            />
            <img v-else class="avatar" src="/static/images/mine/tx.png" />
            <div class="text" v-if="item.msgType == 0">{{ item.msgInfo }}</div>
            <div
              class="file"
              style="margin-left: 20rpx"
              v-if="item.msgType == 1"
              @click="downFile(item.filePath)"
            >
              <text class="fileName">
                {{ item.fileName || '文件' }}.{{
                  item.filePath.substr(item.filePath.lastIndexOf('.') + 1)
                }}</text
              >
              <image
                v-show="item.filePath.substr(item.filePath.lastIndexOf('.') + 1) == 'docx'"
                src="/static/images/chat/word.png"
              ></image>
              <image
                v-show="item.filePath.substr(item.filePath.lastIndexOf('.') + 1) == 'pdf'"
                src="/static/images/chat/pdf.png"
              ></image>
              <image
                v-show="item.filePath.substr(item.filePath.lastIndexOf('.') + 1) == 'xsx'"
                src="/static/images/chat/elxs.png"
              ></image>
            </div>
            <div v-if="item.msgType == 2" @tap="showImg(imgBaseUrl + item.filePath)">
              <image class="img" mode="aspectFill" :src="imgBaseUrl + item.filePath"></image>
            </div>
            <div class="time">{{ item.createTime }}</div>
          </div>
          <div
            class="my"
            v-if="(loginType == 1 && item.isMyself == 1) || (loginType == 0 && item.isMyself == 1)"
          >
            <div class="text" v-if="item.msgType == 0">{{ item.msgInfo }}</div>
            <div class="file" v-if="item.msgType == 1" @click="downFile(item.filePath)">
              <text class="fileName">
                {{ item.fileName || '文件' }}.{{
                  item.filePath.substr(item.filePath.lastIndexOf('.') + 1)
                }}</text
              >
              <image
                v-show="item.filePath.substr(item.filePath.lastIndexOf('.') + 1) == 'docx'"
                src="/static/images/chat/word.png"
              ></image>
              <image
                v-show="item.filePath.substr(item.filePath.lastIndexOf('.') + 1) == 'pdf'"
                src="/static/images/chat/pdf.png"
              ></image>
              <image
                v-show="item.filePath.substr(item.filePath.lastIndexOf('.') + 1) == 'xsx'"
                src="/static/images/chat/elxs.png"
              ></image>
            </div>
            <div v-if="item.msgType == 2" @tap="showImg(imgBaseUrl + item.filePath)">
              <image class="img" mode="aspectFill" :src="imgBaseUrl + item.filePath"></image>
            </div>
            <image
              class="avatar"
              mode="aspectFill"
              :src="getAvatar(item.isMyself)"
              v-if="!getAvatar(item.isMyself)"
            />
            <img v-else class="avatar" src="/static/images/mine/tx.png" />
            <div class="time">{{ item.createTime }}</div>
          </div>
        </div>
        <!-- <div>
          <uni-load-more iconType="circle" :status="loadMoreStatus" />
        </div> -->
      </div>

      <!-- <div>
        <input
          type="number"
          id="channelName"
          v-model="channelName"
          name="房间名称"
          placeholder="房间名称"
          required
        />
        <input type="number" v-model="uid" id="uid" name="UID" placeholder="uid" required />
        <button type="button" id="startCall" @click="startCall">开始通话</button>
        <button type="button" id="finishCall" @click="finishCall">结束通话</button>
        <h4>对方视频</h4>
        <div class="video" id="remoteVideoContent"></div>

        <h4>本地视频</h4>
        <div class="video" id="localVideoContent"></div>
      </div> -->
      <!-- <div>
        <div ref="local-container"></div>
        <button @click="checkDevice">检测设备</button>
      </div> -->

      <!-- 底部 -->
      <div class="footer">
        <!-- <div class="left" @click="audioHandle">
          <image src="/static/images/im/yy.png"></image>
        </div> -->
        <div class="center">
          <input type="text" v-model="formData.msgInfo" />
        </div>
        <div class="right">
          <div class="send" @click="sendMessage(0)">发送</div>
          <!-- <image @click="chooseFile" src="/static/images/im/ad.png"></image> -->
          <image @click="openPopup" src="/static/images/im/ad.png"></image>
        </div>
      </div>
    </div>
    <div class="main_call" v-else>
      <div v-if="isVideo" style="text-align: center; font-size: 33rpx; padding: 10rpx 0">
        {{ keepTime }}
      </div>
      <div class="video_box" v-if="isVideo">
        <div class="video" id="remoteVideoContent"></div>
        <div class="video" id="localVideoContent"></div>
      </div>
      <!-- isVideo top:50 -->
      <!-- <div class="avatar" :style="{ top: isVideo ? '50%' : '30%' }"> -->
      <div class="avatar" v-if="!isVideo">
        <img src="/static/images/mine/tx.png" alt="" />
        <span>{{ keepTime }}</span>
      </div>
      <div class="footer">
        <img @tap="finishCall" src="/static/images/gd.png" alt="" />
      </div>
    </div>
    <uni-popup ref="popup" border-radius="10px 10px 0 0">
      <div class="popup-content">
        <div @click="chooseFile">
          <img src="/static/images/wd.png" alt="" />
          <span>上传文件</span>
        </div>
        <!-- <div @click="startCall(0)">
          <img src="/static/images/yy.png" alt="" />
          <span>语音通话</span>
        </div>
        <div @click="startCall(1)">
          <img src="/static/images/sp.png" alt="" />
          <span>视频通话</span>
        </div> -->
      </div>
    </uni-popup>
  </div>
</template>

<script>
import { debounce } from 'lodash'
import navbar from '@/components/Navbar/index.vue'
import waves from '@/components/waves/waves.vue'
import { getAccessToken } from '@/utils/auth'
import NERTC from 'nertc-web-sdk/NERTC'
import {
  getPageRecordMsg,
  sendTxtMsg,
  getHelpRecord,
  getHelpList,
  sendMediaMsg,
  getPageMsgListd
} from '@/api/im/index.js'
import config from '@/config'
export default {
  components: {
    navbar,
    waves
  },
  data() {
    return {
      headtitle: '对话',
      userInfo: {},
      loginType: 1,
      list: [{}],
      imgBaseUrl: config.baseUrl + '/boot/',
      baseUrl: config.baseUrl,
      loadMoreStatus: 'more',
      pageNo: 1,
      pageSize: 10,
      formData: {
        msgFrom: '',
        msgInfo: '',
        msgTo: '',
        msgType: '',
        recordId: ''
      },
      bbyAvatar: '',
      assistantAvatar: '',
      userAvatar: '',
      extensions: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'jpg', 'png', 'pdf'],
      appkey: '7aeb80cedebcc3ea1b775778c3b0d319',
      channelName: 1348470433173461, // '您指定的房间号'
      uid: 17686840513, // '您指定的用户 ID'
      client: null,
      localStream: null,
      isCall: false,
      isVideo: false,
      startTime: null,
      elapsedTime: 0,
      socket: null,
      reconnectTimer: null,
      heartbeatTimer: null
    }
  },
  watch: {},
  async onShow() {},
  beforeDestroy() {
    this.socket = null
    this.stopHeartbeat()
  },
  async onLoad(options) {
    this.userInfo =
      typeof this.$store.state.user.userInfo == 'string'
        ? JSON.parse(this.$store.state.user.userInfo)
        : this.$store.state.user.userInfo

    if (this.userInfo.isAssistManger || this.userInfo.isAssist) {
      this.loginType = 0
    } else {
      this.loginType = 1
    }
    console.log('loginType', this.loginType)

    // this.loginType = uni.getStorageSync('loginType')
    this.headtitle = options.title !== 'null' ? options.title : '对话'
    this.bbyAvatar = options.bbyAvatar
    this.formData.recordId = options.recordId
    this.formData.msgFrom = this.userInfo.nickName
    this.formData.msgTo = options.title
    // 获取用户和帮办员头像
    let res = null
    if (this.loginType == 0) {
      res = await getHelpList({
        recordId: options.recordId,
        pageNo: 1,
        pageSize: 10
      })
    }
    if (this.loginType == 1) {
      res = await getPageMsgListd({
        recordId: options.recordId,
        pageNo: 1,
        pageSize: 10
      })
    }
    this.assistantAvatar = res.result.records[0].assistantAvatar
    this.userAvatar = res.result.records[0].userAvatar

    this.pageNo = 1
    this.list = []
    this.getList()

    let id = ''
    if (options.assistantId) {
      id = options.assistantId
    } else {
      id = this.userInfo.userId
    }

    // 启动 WebSocket
    this.initWebSocket(id)
  },
  // 下拉加载  enablePullDownRefresh
  onPullDownRefresh() {
    this.pageNo++
    this.getList(0)
  },
  computed: {
    // 计算格式化的时间，例如：hh:mm:ss
    keepTime() {
      const hours = Math.floor(this.elapsedTime / 3600)
        .toString()
        .padStart(2, '0')
      const minutes = Math.floor((this.elapsedTime % 3600) / 60)
        .toString()
        .padStart(2, '0')
      const seconds = (this.elapsedTime % 60).toString().padStart(2, '0')
      return `${hours}:${minutes}:${seconds}`
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    // 初始化 WebSocket
    initWebSocket(id) {
      // ws
      let url =
        process.env.NODE_ENV === 'development' ? 'jyxq.jialangshuchan.cn' : window.location.host

      const WS_URL = 'wss://' + url + '/boot/miniapp/chat/' + id

      // 获取 token
      const getToken = () => getAccessToken()
      this.socket = uni.connectSocket({
        url: WS_URL,
        header: {
          Authorization: getToken()
        },
        success: () => {
          console.log('WebSocket 连接成功')
          this.startHeartbeat()
        },
        fail: (err) => {
          console.log('WebSocket 连接失败', err)
          this.reconnect() // 如果连接失败，尝试重连
        }
      })

      // WebSocket 连接打开事件
      uni.onSocketOpen(() => {
        console.log('WebSocket 已打开')
      })

      // 接收消息事件
      uni.onSocketMessage((res) => {
        console.log('收到消息:', res)
        // 自定义处理消息逻辑
        this.pageNo = 1
        this.list = []
        this.getList(1)
      })

      // 连接关闭事件
      uni.onSocketClose(() => {
        console.log('WebSocket 已关闭')
        this.reconnect() // 连接关闭后尝试重连
      })

      // 错误事件
      uni.onSocketError((err) => {
        console.log('WebSocket 错误', err)
        this.reconnect() // 出现错误后尝试重连
      })
    },
    // 重连机制
    reconnect() {
      if (this.reconnectTimer) return // 防止重复调用

      console.log('尝试重新连接...')
      this.reconnectTimer = setTimeout(() => {
        initWebSocket()
        this.reconnectTimer = null // 清除定时器
      }, 5000) // 5秒后重连
    },
    // 心跳检测
    startHeartbeat() {
      this.stopHeartbeat() // 避免重复启动

      this.heartbeatTimer = setInterval(() => {
        if (this.socket) {
          console.log('发送心跳')
          uni.sendSocketMessage({
            // data: JSON.stringify('heartcheck'),
            data: 'heartcheck',
            fail: (err) => {
              console.log('心跳发送失败', err)
              this.reconnect() // 如果心跳发送失败，尝试重连
            }
          })
        }
      }, 30000) // 每30秒发送一次心跳
    },
    // 停止心跳检测
    stopHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer)
        this.heartbeatTimer = null
      }
    },
    startTimer() {
      // 记录开始时间
      this.startTime = Date.now()
      // 设置每秒更新一次计时器
      this.timer = setInterval(() => {
        // 计算已过去的时间（秒）
        this.elapsedTime = Math.floor((Date.now() - this.startTime) / 1000)
      }, 1000)
    },
    stopTimer() {
      clearInterval(this.timer)
      this.timer = null
    },
    openPopup() {
      this.$refs.popup.open('bottom')
    },
    closePopup() {
      this.$refs.popup.close()
    },
    async startCall(type) {
      this.closePopup()
      this.isCall = true
      this.startTimer()
      type === 0 ? (this.isVideo = false) : (this.isVideo = true)
      this.channelName = parseInt(this.channelName)
      this.uid = parseInt(this.uid)
      this.client = NERTC.createClient({ appkey: this.appkey, debug: true })
      // 监听事件
      this.client.on('stream-added', (event) => {
        const remoteStream = event.stream
        console.warn('收到别人的发布消息: ', remoteStream.streamID, 'mediaType: ', event.mediaType)
        //订阅远端流
        this.client.subscribe(remoteStream).then(() => {
          console.warn(`subscribe 成功 ${remoteStream.streamID}`)
        })
      })
      this.client.on('stream-subscribed', (event) => {
        // 远端流订阅成功
        const remoteStream = event.stream
        console.warn(
          '订阅别人的流成功的通知: ',
          remoteStream.streamID,
          'mediaType: ',
          event.mediaType
        )
        // 设置远端视频画布
        remoteStream.setRemoteRenderMode({
          width: 640,
          height: 480
        })
        // 播放远端流
        remoteStream.play('remoteVideoContent')
      })
      this.client.on('peer-online', (evt) => {
        console.log(`${evt.uid} 加入房间`)
        // addLog(`${evt.uid} 加入房间`)
        uni.showToast({
          title: '已接通',
          icon: 'none'
        })
      })
      // 离开房间监听
      this.client.on('peer-leave', (evt) => {
        console.log(`${evt.uid} 退出房间`)
        // leaveLog(`${evt.uid} 退出房间`)
        uni.showToast({
          title: '对方已经挂断',
          icon: 'none'
        })
        setTimeout(() => {
          this.finishCall()
        }, 500)
      })
      // 进房成功后开始推流
      try {
        await this.client.join({ channelName: this.channelName, uid: this.uid })
        this.localStream = NERTC.createStream({
          uid: this.uid,
          audio: true,
          video: type == 0 ? false : true,
          client: this.client
        })
        await this.localStream.init()
        // 设置本地视频画布
        this.localStream.setLocalRenderMode({
          width: 640,
          height: 480
        })
        // 播放本地流
        this.localStream.play('localVideoContent')
        await this.client.publish(this.localStream)

        // 初始化音频
      } catch (error) {
        console.error(error)
      }
    },
    async finishCall() {
      this.isCall = false
      this.stopTimer()
      await this.client.leave()
    },
    checkDevice() {
      this.client = NERTC.createClient({ appkey: this.appkey, debug: true })
      NERTC.getDevices().then((devices) => {
        var audioDevices = devices.audioIn //数组，麦克风设备列表
        var videoDevices = devices.video //数组，摄像头设备列表
        console.log('audioDevices', audioDevices, 'videoDevices', videoDevices)

        var uid = Math.floor(Math.random() * 10000)
        var selectedMicrophoneId = audioDevices[0].deviceId
        var selectedCameraId = videoDevices[0].deviceId
        var localStream = NERTC.createStream({
          uid: uid,
          audio: true,
          microphoneId: selectedMicrophoneId, //指定要开启的mic
          video: true,
          cameraId: selectedCameraId, //指定要开启的camera
          screen: false,
          client: this.client
        })
        console.log('localStream', localStream)

        localStream.init().then(() => {
          //用于播放视频的div元素
          // let div = document.getElementById('local-container')
          let div = this.$refs['local-container']
          localStream.play(div)
          //设置播放的视频容器大小
          localStream.setLocalRenderMode({
            width: 180,
            height: 150,
            cut: true
          })

          setInterval(function () {
            console.log(`获取mic采集的音量： ${localStream.getAudioLevel()}`)
          }, 1000)
        })
      })
    },
    audioHandle() {
      // 暂不支持
      uni.showToast({
        title: '暂不支持',
        icon: 'none'
      })
    },
    showImg(url) {
      uni.previewImage({
        urls: [url]
      })
    },
    downFile(fileUrl) {
      let fileFormat = fileUrl.substr(fileUrl.lastIndexOf('.') + 1)
      if (fileFormat === 'png' || fileFormat === 'jpg') {
        uni.previewImage({
          urls: [this.baseUrl + '/boot/' + fileUrl]
        })
        return
      }
      uni.showLoading({
        title: '正在打开...'
      })
      uni.downloadFile({
        url: this.baseUrl + '/boot/' + fileUrl,
        header: {
          Authorization: getAccessToken()
        },
        // filePath: `${wx.env.USER_DATA_PATH}/${'文件'}.${fileFormat}`,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              fileType: fileFormat,
              showMenu: true,
              success: () => {
                uni.hideLoading()
              },
              fail: () => {
                uni.$u.toast('打开文档失败,请检查格式是否正确')
              }
            })
          }
        }
      })
    },
    getAvatar(isMyself) {
      const getFullAvatar = (avatar) => {
        return avatar && avatar.includes('http') ? avatar : this.imgBaseUrl + avatar
      }

      const defaultAvatar = '/static/images/mine/tx.jpg'

      if (this.loginType == 1) {
        return isMyself === 0
          ? this.assistantAvatar
            ? getFullAvatar(this.assistantAvatar)
            : defaultAvatar
          : this.userAvatar
          ? getFullAvatar(this.userAvatar)
          : defaultAvatar
      } else {
        return isMyself === 0
          ? this.userAvatar
            ? getFullAvatar(this.userAvatar)
            : defaultAvatar
          : this.assistantAvatar
          ? getFullAvatar(this.assistantAvatar)
          : defaultAvatar
      }
    },
    async getList(isScroll = 1) {
      let res = {}
      if (this.loginType == 1) {
        res = await getPageRecordMsg({
          recordId: this.formData.recordId,
          pageNo: this.pageNo,
          pageSize: this.pageSize
        })
      }
      if (this.loginType == 0) {
        res = await getHelpRecord({
          recordId: this.formData.recordId,
          pageNo: this.pageNo,
          pageSize: this.pageSize
        })
      }

      this.list = this.list.concat(res.result.records)
      this.list = this.list.sort((a, b) => {
        return this.parseDateString(a.createTime) - this.parseDateString(b.createTime)
      })
      uni.stopPullDownRefresh()
      // 平滑滚动到最底部
      if (isScroll == 1) {
        console.log('滚动到最底部')
        setTimeout(() => {
          uni.pageScrollTo({
            scrollTop: 99999,
            duration: 300
          })
        }, 50)
      }
    },
    async sendMessage(type) {
      if (!this.formData.msgInfo) {
        uni.showToast({
          icon: 'none',
          title: '请输入内容'
        })
        return
      }
      this.formData.msgType = type
      const res = await sendTxtMsg(this.formData)
      if (res.code == 200) {
        this.formData.msgInfo = ''
        this.pageNo = 1
        this.list = []
        this.getList()
      }
    },
    // 文件上传
    chooseFile() {
      this.closePopup()
      uni.chooseFile({
        count: 1,
        type: 'file',
        extensions: this.extensions,
        success: (res) => {
          const str = res.tempFiles[0].name.substring(0, res.tempFiles[0].name.lastIndexOf('.'))
          const fileType = res.tempFiles[0].name.substring(
            res.tempFiles[0].name.lastIndexOf('.') + 1
          )
          console.log('name', str)

          if (!this.extensions.includes(fileType)) {
            uni.showToast({
              icon: 'none',
              title: '文件格式不正确'
            })
            return
          }
          uni.uploadFile({
            header: {
              Authorization: uni.getStorageSync('ACCESS_TOKEN')
            },
            url: config.baseUrl + '/boot' + '/miniapp/api/help/sendHelpMediaMsg',
            filePath: res.tempFiles[0].path,
            name: 'file',
            formData: {
              biz: `record/${this.formData.recordId}/chat`,
              recordId: this.formData.recordId,
              fileFormat: fileType,
              fileName: str,
              fileSize: res.tempFiles[0].size
            },
            success: (uploadFileRes) => {
              if (uploadFileRes.statusCode !== 413) {
                // console.log('上传成功', JSON.parse(uploadFileRes.data))
                this.pageNo = 1
                this.list = []
                this.getList()
              } else {
                uni.showToast({
                  icon: 'none',
                  title: '上传文件过大'
                })
              }
            },
            fail: (err) => {
              uni.showToast({
                icon: 'none',
                title: '上传失败'
              })
            }
          })
        }
      })
    },
    parseDateString(dateString) {
      return new Date(dateString.replace(/-/g, '/'))
    }
  }
}
</script>

<style>
page {
  background-color: #ffffff;
}
</style>
<style lang="scss" scoped>
.main_bxo {
  position: relative;
  .headtitle {
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    background: #2d8cf0;
    color: #fff;
    text-align: center;
    font-size: 28rpx;
    font-weight: 600;
    padding: 20rpx 0;
    z-index: 2;
    img {
      width: 33rpx;
      height: 40rpx;
      left: 20rpx;
      position: absolute;
      z-index: 3;
    }
  }
  .content {
    position: relative;
    padding: 100rpx 30rpx 0 30rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #444444;
    padding-bottom: 200rpx;
    .avatar {
      width: 66rpx;
      height: 66rpx;
    }
    .bby {
      display: flex;
      position: relative;
      padding-bottom: 40rpx;
      margin-bottom: 20rpx;
      .text {
        margin-top: 30rpx;
        margin-left: 20rpx;
        background: #ffffff;
        box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(0, 0, 0, 0.1);
        border-radius: 0rpx 20rpx 20rpx 20rpx;
        padding: 20rpx 30rpx;
        max-width: 600rpx;
        overflow: hidden;
        // white-space: nowrap;
        text-overflow: ellipsis;
      }
      .time {
        position: absolute;
        bottom: 0;
        left: 86rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #acacac;
      }
    }
    .my {
      display: flex;
      justify-content: flex-end;
      position: relative;
      padding-bottom: 40rpx;
      margin-bottom: 20rpx;
      .text {
        margin-top: 30rpx;
        margin-right: 20rpx;
        background: rgba(5, 127, 254, 0.1);
        box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(0, 0, 0, 0.1);
        border-radius: 0rpx 20rpx 20rpx 20rpx;
        padding: 20rpx 30rpx;
      }
      .time {
        position: absolute;
        bottom: 0;
        right: 86rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #acacac;
      }
    }
    .file {
      background: rgba(5, 127, 254, 0.1);
      box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1);
      margin-right: 20rpx;
      border-radius: 0rpx 20rpx 20rpx 20rpx;
      padding: 20rpx 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .fileName {
        flex: 1;
      }
      image {
        width: 60rpx;
        height: 60rpx;
        margin-left: 20rpx;
      }
    }
    .img {
      width: 120rpx;
      height: 120rpx;
      margin-top: 30rpx;
      margin-left: 20rpx;
      margin-right: 20rpx;
    }
  }
  .footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 161rpx;
    background: #f9f9f9;
    box-shadow: 0rpx -4rpx 16rpx 0rpx rgba(0, 26, 55, 0.08);
    // 底部安全距离
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    display: flex;
    align-items: center;
    justify-content: space-around;
    // .left {
    //   width: 42rpx;
    //   height: 52rpx;
    //   margin: 0 10rpx;
    //   image {
    //     width: 100%;
    //     height: 100%;
    //   }
    // }
    .right {
      display: flex;
      align-items: center;
      margin: 0 20rpx 0 10rpx;
      image {
        width: 52rpx;
        height: 52rpx;
      }
      .send {
        width: 120rpx;
        height: 70rpx;
        line-height: 70rpx;
        background: #057ffe;
        border-radius: 8rpx;
        text-align: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28rpx;
        color: #ffffff;
        margin-right: 10rpx;
      }
    }
    .center {
      flex: 1;
      margin-left: 20rpx;
      input {
        width: 100%;
        height: 74rpx;
        line-height: 74rpx;
        background: #ffffff;
        border-radius: 8rpx;
        border: 1rpx solid #dddddd;
        padding-left: 20rpx;
      }
    }
  }
}
// .video{
//   height: 400rpx;
// }
::v-deep .nertc-video-container {
  width: 100% !important;
  height: 600rpx !important;
}
::v-deep .nertc-video-container-remote {
  width: 100% !important;
  height: 300rpx !important;
}

.popup-content {
  min-height: 300rpx;
  background: #fff;
  display: flex;
  justify-content: space-around;
  & > div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  img {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 20rpx;
  }
}

.main_call {
  width: 100%;
  height: 100vh;
  position: relative;
  background: rgba(0, 0, 0, 0.1);
  .avatar {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    img {
      width: 150rpx;
      height: 150rpx;
      border-radius: 50%;
      margin-bottom: 20rpx;
    }
    span {
      text-align: center;
      font-size: 33rpx;
    }
  }
  .footer {
    width: 70%;
    position: absolute;
    left: 50%;
    bottom: 3%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    img {
      width: 100rpx;
      height: 100rpx;
    }
  }
}
</style>
