<template>
	<view class="">
		<!-- <navbar :Props="Props" :title='headtitle' @black="black"></navbar> -->
		<view class="content">
			<image class="report_title" src="@/static/images/interaction/report_title.png" mode=""></image>
			<view class="intro">
				<view class="intro_text">
					请写下您宝贵的意见，我们将及时的改进各项服务，更好的满足您的需求，如方便也可以留下联系方式
				</view>
			</view>
			<view class="selector">
				<view :class="{ 'error': selectError, 'shake': selectError }" class="selector-item" @click="showPopUp">
					{{selectValue}}
				</view>
				<image class="arrow" src="@/static/images/interaction/arrow.png" mode=""></image>
			</view>
			<textarea v-model="textareaValue" class="text" name="rich" cols="30" rows="10"
				placeholder="请输入反馈信息"></textarea>
			<button class="submit-button" @click="validateInput">提交</button>
			<uni-popup class="popup" ref="pop" type="bottom" mask-background-color="rgba(0,0,0,0.1)">
				<view class="popup-content">
					<image class="bg" src="@/static/images/interaction/popup.png" mode=""></image>
					<view class="popup-title">
						<image class="false" src="@/static/images/interaction/false.png" mode=""></image>
						<view class="title">投诉举报类型</view>

						<image class="title_icon" src="@/static/images/interaction/title_icon.png" mode=""></image>
						<!-- <image class="true" src="@/static/images/interaction/true.png" mode=""></image> -->
					</view>
					<view class="select">
						<view v-for="(item, index) in selectList" :key="index"
							:class="nowProvince == index ? 'select-change' : 'select-item'" class=""
							@click="handleSelect(index)">
							{{item}}
						</view>
					</view>
				</view>
			</uni-popup>
			<uni-popup ref="message" type="message">
				<uni-popup-message :type="msgType" :message="messageText" :duration="2000"></uni-popup-message>
			</uni-popup>
		</view>
	</view>
</template>

<script>
	import navbar from '@/components/Navbar/index.vue'
	import {
		createComplaint
	} from "@/api/interaction/report.js"
	export default {
		components: {
			navbar
		},
		onLoad: function() {
			this.Props.statusBarHeight = getApp().globalData.statusBarHeight
			this.Props.capsuleTop = getApp().globalData.capsuleTop
		},
		data() {
			return {
				textareaValue: '',
				selectValue: '选择投诉举报类型',
				selectList: ['用户体验', '功能性', '办事效率', '服务态度'],
				selectError: false,
				inputError: false,
				msgType: '',
				messageText: '',
				nowProvince: '-1',
				headtitle: '投诉举报',
				Props: {
					imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: "", //导航高度(动态获取传参)
					bgColor: "", //导航栏背景色,不传参则默认#9CF
					capsuleTop: "", //胶囊顶部距离(动态获取传参)
					textColor: "", //导航标题字体颜色(不传默认#FFF)
					iconColor: "", //icon图标颜色(不传默认#FFF)
					blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
					backText: "", //默认字体(返回)
				},
			}
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		},
		methods: {
			handleSelect(index) {
				console.log('imherer')
				this.nowProvince = index;
				this.selectValue = this.selectList[index]
				this.selectError = false
				this.closePopUp()
			},
			onInput(event) {
				// 只允许输入数字
				this.value = event.target.value.replace(/\D/g, '')
				this.inputError = false
			},
			validateInput() {
				this.selectError = false;
				this.inputError = false;

				if (!this.selectList.includes(this.selectValue)) {
					this.selectError = true;
				}

				const phoneRegex = /^1[3-9]\d{9}$/;

				if (this.selectError) {
					this.msgType = 'error'
					this.messageText = '请选择您的投诉举报类型'
				} else {
					// this.createReport()

					const type = this.selectList.indexOf(this.selectValue) + 1
					const param = {
						type: type,
						content: this.textareaValue,
					}
					createComplaint(param).then(() => {
						this.msgType = 'succuss'
						this.messageText = '您的投诉举报我们已经收到，感谢您的宝贵意见！'
						setTimeout(() => {
							uni.navigateBack();
						}, 1000)
					})

				}
				this.$refs.message.open()
			},
			showPopUp() {
				this.$refs.pop.open();
			},
			closePopUp() {
				this.$refs.pop.close();
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f5f6f7;
	}

	.content {
		margin: 32rpx;
		padding: 55rpx 34rpx 30rpx 34rpx;
		display: flex;
		gap: 30rpx;
		flex-direction: column;
		align-items: center;
		background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 100%);
		box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 32rpx;
		position: relative;
		.report_title {
			width: 686rpx;
			height: 180rpx;
			position: absolute;
			top: 0;
			left: 0;
		}

		.intro {
			position: relative;
			width: 618rpx;
			height: 200rpx;
			padding: 0 5rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #444444;
			line-height: 44rpx;
			font-style: normal;

			.intro_text {
				position: absolute;
			}
		}

		.popup {
			height: 200rpx;
		}

		.selector {
			position: relative;

			.selector-item {
				display: flex;
				padding: 12rpx 0 0 31rpx;
				width: 610rpx;
				height: 60rpx;
				background: #FFFFFF;
				border-radius: 30rpx;
				border: 2rpx solid #DDDDDD;
				font-weight: 500;
				font-size: 24rpx;
				color: #444444;
				line-height: 33rpx;
				font-style: normal;

				&:hover {
					color: #aaa;
					border: solid 1rpx rgba(200, 200, 200, 0.7);
				}
			}

			.arrow {
				width: 28rpx;
				height: 28rpx;
				position: absolute;
				top: 16rpx;
				right: 32rpx;
			}
		}

		.text {
			width: 610rpx;
			height: 590rpx;
			background: #F5F7FB;
			border-radius: 20rpx;
			padding: 30rpx;

			&:hover {
				border: solid 1rpx rgba(200, 200, 200, 0.7);
			}

			&.row {
				display: flex;
				height: 100%;
			}
		}

		.submit-button {
			width: 590rpx;
			height: 80rpx;
			background: #057FFE;
			border-radius: 8rpx;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 80rpx;
			letter-spacing: 2px;
			text-align: center;
			font-style: normal;
		}

		.error {
			border-color: red;
		}

		.shake {
			animation: shake 0.5s;
		}

		@keyframes shake {
			0% {
				transform: translateX(0);
			}

			25% {
				transform: translateX(-5px);
			}

			50% {
				transform: translateX(5px);
			}

			75% {
				transform: translateX(-5px);
			}

			100% {
				transform: translateX(0);
			}
		}

		.popup {
			height: 100%;

			.popup-content {
				height: 100%;
				display: flex;
				align-items: center;
				flex-direction: column;
				justify-content: center;
				background: #fff;
				border-radius: 35rpx;
				background-color: #fff;
				width: 100%;
				position: relative;
				padding-top: 40rpx;

				.bg {
					width: 750rpx;
					height: 260rpx;
					position: absolute;
					top: 0;
					left: 0;
				}

				:first-child {
					border-radius: 35rpx 35rpx 0 0;
				}

				.popup-title {
					width: 750rpx;
					height: 72rpx;
					padding: 0 52rpx;
					text-align: center;
					z-index: 99999;

					.false {
						width: 28rpx;
						height: 28rpx;
						position: absolute;
						top: 47rpx;
						left: 52rpx;
					}

					.title_icon {
						width: 107rpx;
						height: 42rpx;
						position: absolute;
						top: 44rpx;
						left: 322rpx;
						z-index: 99999;
					}

					.title {
						font-weight: 500;
						font-size: 30rpx;
						color: #141414;
						line-height: 42rpx;
						font-style: normal;
						z-index: 100010;
						position: absolute;
						top: 47rpx;
						left: 285rpx;
					}

					.true {
						width: 40rpx;
						height: 28rpx;
						position: absolute;
						top: 47rpx;
						right: 52rpx;
					}
				}

				.select {
					margin-top: 0rpx;
					z-index: 100001;

					.select-change {
						width: 702rpx;
						height: 88rpx;
						background: #F4F8FF;
						border-radius: 8rpx;
						font-weight: 500;
						font-size: 30rpx;
						color: #057FFE;
						line-height: 88rpx;
						text-align: center;
						font-style: normal;
					}

					.select-item {
						width: 100%;


						display: flex;
						padding: 30rpx;
						align-items: center;
						justify-content: center;
						transition: background-color 0.3s;


						font-weight: 400;
						font-size: 30rpx;
						color: #000000;
						line-height: 42rpx;

						font-style: normal;

						&:active {
							background-color: #f5f5f5;
						}
					}
				}

			}
		}
	}
</style>