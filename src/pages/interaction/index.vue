<template>
	<view  :style="{paddingTop:  Props.statusBarHeight + 'px'}">
		<navbar :Props="Props" :title="headtitle" @black="black"></navbar>
		<view class="content">
			<view v-show="loginType == 0" class="box" @click="goto('g-record/index')">
				<image class="tsjb_img" src="@/static/images/interaction/tsjb_background.png" mode=""></image>
				<image class="title" src="@/static/images/interaction/tsjb_title.png" mode=""></image>
				<!-- 			<view class="title">
					投诉举报（管理端）
				</view>
				<view class="goto-button iconfont icon-right">
				</view> -->
			</view>
			<view v-show="loginType == 1" class="box">
				<image class="tsjb_img" src="@/static/images/interaction/tsjb_background.png" mode=""></image>
				<image class="title" src="@/static/images/interaction/tsjb_title.png" mode=""></image>
				<image class="record" src="@/static/images/interaction/record.png" mode=""></image>
				<image class="submit" src="@/static/images/interaction/submit.png" mode=""></image>
				<image class="left_dot" src="@/static/images/interaction/dot.png" mode=""></image>
				<image class="right_dot" src="@/static/images/interaction/dot.png" mode=""></image>
				<view @click="goto('report/index')" class="submit-button" hover-class="is-hover">提交</view>
				<view @click="goto('record/index')" class="record-button" hover-class="is-hover">记录</view>
				<!-- 			<view class="title">
					投诉举报
				</view>
				<view :style="{ fontSize: fontSizeLarge }" class="button-wrapper">
					<view @click="goto('report/index')" class="submit-button" hover-class="is-hover">提交</view>
					<view @click="goto('record/index')" class="submit-button" hover-class="is-hover">记录</view>
				</view> -->
			</view>
			<view class="box" @click="goto('im/index')">
				<image class="tsjb_img" src="@/static/images/interaction/bbdh_background.png" mode=""></image>
				<image class="title" src="@/static/images/interaction/bbdh_title.png" mode=""></image>
				<image class="golook_bg" src="@/static/images/interaction/golook.png" mode=""></image>
				<image class="bottom_dot" src="@/static/images/interaction/dot.png" mode=""></image>
				<view class="golook">去看看</view>
				<!-- 			<view class="title">
					帮办对话
				</view>
				<view @click="goto('im/index')" class="goto-button iconfont icon-right">
				</view> -->
			</view>

			<TabBar :current="2" :tabBarList="tabBerLists" />
		</view>
	</view>
</template>

<script>
	import global from '@/utils/global.js'
	import TabBar from '@/components/TabBar/index.vue'
	import navbar from '@/components/Navbar/index.vue'
	export default {
		components: {
			navbar,
			TabBar
		},
		data() {
			return {
				tabBerLists: [],
				headtitle: '互动',
				Props: {
					imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: '', //导航高度(动态获取传参)
					bgColor: '', //导航栏背景色,不传参则默认#9CF
					capsuleTop: '', //胶囊顶部距离(动态获取传参)
					textColor: '', //导航标题字体颜色(不传默认#FFF)
					iconColor: '', //icon图标颜色(不传默认#FFF)
					blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
					backText: '' //默认字体(返回)
				},
				loginType: 1,
				count: '',
				isAssist: 1
			}
		},
		onShow() {
			this.tabBerLists = uni.getStorageSync('tabBarList')
		},
		onLoad: function() {
			const that = this
			that.Props.statusBarHeight = getApp().globalData.statusBarHeight
			that.Props.capsuleTop = getApp().globalData.capsuleTop
			that.loginType = uni.getStorageSync('loginType')
			let storageData = uni.getStorageSync('storage_data') || {}
			console.log(that.loginType, 'loginType-------')
			that.isAssist = storageData.vuex_userInfo.isAssist
			
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge // 从Vuex获取大号字体大小
			}
		},
		methods: {
			goto(url) {
				uni.navigateTo({
					url: url
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f5f6f7;
	}

	.content {
		padding: 15rpx;
		height: 100%;
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		background: #f4f8fc;

		.box {
			// padding: 20px;
			// // background-color: rgba(240, 248, 255, 0.5);
			// background-color: rgba(40, 107, 255, 0.9);
			// // background-color: rgba(113, 161, 255, 1);
			// width: 100%;
			// height: 230rpx;
			// display: flex;
			// align-items: center;
			// justify-content: space-between;
			// border-radius: 20rpx;
			// box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
			position: relative;

			.tsjb_img {
				width: 720rpx;
				height: 214rpx;
			}

			.title {
				width: 199rpx;
				height: 59rpx;
				position: absolute;
				top: 50rpx;
				right: 93rpx;
			}

			.submit {
				width: 130rpx;
				height: 43rpx;
				position: absolute;
				top: 127rpx;
				right: 223rpx;
			}

			.record {
				width: 130rpx;
				height: 43rpx;
				position: absolute;
				top: 127rpx;
				right: 94rpx;
			}

			.left_dot {
				width: 20rpx;
				height: 21rpx;
				position: absolute;
				top: 138rpx;
				right: 254rpx;
			}

			.right_dot {
				width: 20rpx;
				height: 21rpx;
				position: absolute;
				top: 138rpx;
				right: 125rpx;
			}

			.submit-button {
				font-weight: 400;
				font-size: 20rpx;
				color: #ffffff;
				line-height: 28rpx;
				font-style: normal;
				position: absolute;
				top: 133rpx;
				right: 282rpx;
			}

			.golook_bg {
				width: 151rpx;
				height: 42rpx;
				position: absolute;
				top: 128rpx;
				right: 93rpx;
			}

			.record-button {
				font-weight: 400;
				font-size: 20rpx;
				color: #ffffff;
				line-height: 28rpx;
				font-style: normal;
				position: absolute;
				top: 133rpx;
				right: 153rpx;
			}

			.bottom_dot {
				width: 20rpx;
				height: 21rpx;
				position: absolute;
				top: 139rpx;
				right: 124rpx;
			}

			.golook {
				font-weight: 400;
				font-size: 20rpx;
				color: #ffffff;
				line-height: 28rpx;
				font-style: normal;
				position: absolute;
				top: 133rpx;
				right: 152rpx;
			}

			.button-wrapper {
				display: flex;
				flex-direction: column;
				gap: 20rpx;

				.submit-button {
					display: flex;
					color: #fff;
					background-color: rgba(255, 255, 255, 0.2);
					border-radius: 10rpx;
					align-items: center;
					padding: 10rpx;
					justify-content: center;
					width: 200rpx;
					box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;

					&:hover {
						background-color: rgba(255, 255, 255, 0.1);
					}
				}
			}
		}
	}

	.title {
		font-size: 50rpx;
		color: #fff;
	}
</style>