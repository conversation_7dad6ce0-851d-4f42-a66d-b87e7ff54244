<template>
  <view class="box">
    <view class="backboxs">
      <image
        class="back"
        @click="back()"
        src="@/static/images/map/back.png"
        mode=""
      ></image>
    </view>
    <!-- <navbar :Props="Props" :title='headtitle' @black="black"></navbar> -->
    <view class="boxs">
      <view class="content">
        <!-- <image class="g_background" src="@/static/images/interaction/g_background.png" mode=""></image> -->
        <view class="report">
          <view :style="{ fontSize: fontSizeMedium }" class="title">
            <view class="round"></view>
            <view class="text">业务信息</view>
          </view>
          <view class="cell phone">
            <view class="f30">业务流水号</view>
            <view class="">
              <u--input
                v-model="FormData.businessId"
                style="width: 400rpx"
                class="input"
                placeholder="请输入业务流水号"
                clearable
                disabled
              ></u--input>
            </view>
          </view>
          <view :style="{ fontSize: fontSizeMedium }" class="cell">
            <view class="f30">姓名</view>
            <view class="">
              <u--input
                disabled
                v-model="FormData.appointmentName"
                style="width: 400rpx"
                class="input"
                placeholder="请输入姓名"
                clearable
              ></u--input>
            </view>
          </view>
          <view class="line"></view>
          <view :style="{ fontSize: fontSizeMedium }" class="cell">
            <view class="f30">联系方式</view>
            <view class="">
              <u--input
                disabled
                v-model="FormData.appointmentTel"
                style="width: 400rpx"
                class="input"
                placeholder="请输入联系方式"
                clearable
              ></u--input>
            </view>
          </view>
          <view class="line"></view>

          <view :style="{ fontSize: fontSizeMedium }" class="cell">
            <view class="f30">办理事项</view>
            <view class="">
              <u--input
                disabled
                v-model="FormData.itemName"
                style="width: 400rpx"
                class="input"
                placeholder="请输入办理事项"
                clearable
              ></u--input>
            </view>
          </view>
          <!-- <view class="line"></view> -->
          <!-- <view class="cell">
						<view class="f30">事项级别</view>
						<view class="">
							<u--input disabled v-model="name" style="width: 400rpx;" class="input" placeholder="请输入事项级别"
								clearable></u--input>
						</view>
					</view>
					<view class="line"></view>
					<view class="cell">
						<view class="f30">事项类型</view>
						<view class="">
							<u--input disabled v-model="FormData.itemType" style="width: 400rpx;" class="input" placeholder="请输入事项级别"
								clearable></u--input>
						</view>
					</view> -->
          <view class="line"></view>

          <view :style="{ fontSize: fontSizeMedium }" class="cell">
            <view class="f30">预约网点</view>
            <view class="">
              <u--input
                disabled
                v-model="FormData.appointmentPoint"
                style="width: 400rpx"
                class="input"
                placeholder="请输入预约网点"
                clearable
              ></u--input>
            </view>
          </view>
          <view class="line"></view>
          <view class="cell">
            <view class="f30">申请时间</view>
            <view class="">
              <u--input
                disabled
                v-model="FormData.applyTime"
                style="width: 400rpx"
                class="input"
                placeholder="请输入申请时间"
                clearable
              ></u--input>
            </view>
          </view>
        </view>
      </view>
      <view class="order">
        <scroll-view class="text" scroll-x="true" style="width: 100%">
          <view class="date_boxs">
            <view
              class="items"
              v-for="(report, index) in dateList"
              :key="index"
            >
              <view
                class="weeklist"
                :class="dateChange == index ? 'line-bottom' : ''"
              >
                <view :class="dateChange == index ? 'blue' : ''" class="week"
                  >{{ report.weekday }}
                </view>
                <view :class="dateChange == index ? 'blue' : ''" class="text">{{
                  report.date
                }}</view>
              </view>
            </view>
          </view>
        </scroll-view>
        <view class="government">
          <view class="places">
            <view
              :class="timeChange == index ? 'timeChange' : ''"
              class="placesitem"
              v-for="(item, index) in timeList"
              :key="index"
            >
              <view>{{ item.timePeriod }}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="tips" v-if="FormData.handleStatus != 1">
        <image
          class="tips_img"
          src="@/static/images/map/order_title.png"
          mode=""
        ></image>
        <view class="line"> </view>
        <span class="tips_text">
          {{ FormData.appointmentResult }}
        </span>
        <!-- 				<view class="tips_text">
					
					预约成功请您在{{ FormData.appointmentTime}} {{ FormData.appointmentTimePeriod }}
					到{{FormData.serviceLocal}}{{ FormData.windowLocal}}进行办理
				</view> -->
      </view>
    </view>

    <!-- 		<view class="btn-box">
			<view :style="{ fontSize: fontSizeMedium }" v-if="FormData.handleStatus != 1 " class="feedback"
				@click="validateInput()">
				再次预约</view>
		</view> -->
    <uni-popup ref="message" type="message">
      <uni-popup-message
        :type="msgType"
        :message="messageText"
        :duration="2000"
      ></uni-popup-message>
    </uni-popup>
  </view>
</template>

<script>
import { getDictItem } from "@/api/assistant/index.js";
import qiaoSelect from "@/uni_modules/qiao-select/components/qiao-select/qiaoSelect.vue";
import { appointmentAdd } from "@/api/map/map.js";
import navbar from "@/components/Navbar/index.vue";
import {
  getAppointmentTimeList,
  getAppointmentQueryByld,
} from "@/api/map/map.js";
export default {
  components: {
    navbar,
    qiaoSelect,
  },
  onLoad: function (report) {
    this.Props.statusBarHeight = getApp().globalData.statusBarHeight;
    this.Props.capsuleTop = getApp().globalData.capsuleTop;
    this.reportItem = report;
    this.getQueryByld();
  },
  data() {
    return {
      disabled: false,
      reportItem: {},
      feedback: "",
      headtitle: "预约",
      msgType: "",
      messageText: "",
      Props: {
        imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: "", //导航高度(动态获取传参)
        bgColor: "", //导航栏背景色,不传参则默认#9CF
        capsuleTop: "", //胶囊顶部距离(动态获取传参)
        textColor: "", //导航标题字体颜色(不传默认#FFF)
        iconColor: "", //icon图标颜色(不传默认#FFF)
        blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
        backText: "", //默认字体(返回)
      },
      orgArray: [
        {
          id: 1,
          title: "办事大厅",
        },
        {
          id: 2,
          title: "自助终端机",
        },
        {
          id: 3,
          title: "便民服务站",
        },
        {
          id: 4,
          title: "医院",
        },
        {
          id: 5,
          title: "卫生院",
        },
        {
          id: 6,
          title: "银行",
        },
      ],
      showObj: "",
      dateList: [],
      timeList: [],
      dateChange: 0,
      timeChange: 0,
      FormData: {
        appointmentName: "",
        itemName: "",
        appointmentTel: "",
        appointmentTime: "",
        appointmentTimePeriod: "",
        itemLevel: "",
      },
      levelList: [],
      name: "",
      type: "",
    };
  },
  methods: {
    back() {
      uni.navigateBack({
        delta: 1,
      });
    },
    async getlevelList() {
      const res = await getDictItem({
        dictCode: "jy_administrative_level",
      });
      this.levelList = res.result;
      const result = this.levelList.filter(
        (item) => item.value == this.FormData.itemLevel
      );
      this.name = result[0].title;
    },
    async getItemType() {
      const res = await getDictItem({
        dictCode: "jy_item_type",
      });
      console.log(res, "----------------2--------------");
      this.typeList = res.result;
      const result = this.typeList.filter(
        (item) => item.value == this.FormData.itemType
      );
      console.log(result, "1111122222");
      this.type = result[0].title;
    },
    async getQueryByld() {
      const query = {
        id: this.reportItem.id,
      };
      const res = await getAppointmentQueryByld(query);
      console.log(res, "res-------");
      this.FormData = res.result;

      if (this.FormData.appointmentTimePeriod == "9:30") {
        this.timeChange = 0;
      } else if (this.FormData.appointmentTimePeriod === "10:00") {
        this.timeChange = 1;
      } else if (this.FormData.appointmentTimePeriod === "11:30") {
        this.timeChange = 2;
      } else if (this.FormData.appointmentTimePeriod === "12:00") {
        this.timeChange = 3;
      } else if (this.FormData.appointmentTimePeriod === "15:00") {
        this.timeChange = 4;
      } else if (this.FormData.appointmentTimePeriod === "15:30") {
        this.timeChange = 5;
      } else if (this.FormData.appointmentTimePeriod === "16:00") {
        this.timeChange = 6;
      } else if (this.FormData.appointmentTimePeriod === "16:30") {
        this.timeChange = 7;
      } else if (this.FormData.appointmentTimePeriod === "17:00") {
        this.timeChange = 8;
      }
      this.FormData.appointmentTel =
        this.FormData.appointmentTel.slice(0, 3) +
        "****" +
        this.FormData.appointmentTel.slice(7);
      console.log(this.timeChange, "22222");
      //   this.getlevelList();
      // this.getItemType()
      this.getList();
    },
    async getAppointmentTimeList() {
      const query = {
        appointmentTime: this.FormData.appointmentTime,
      };
      const res = await getAppointmentTimeList(query);
      this.timeList = res.result;
    },
    selectChange(e) {
      //返回选择的对象，如果输入框清空，返回null
      if (e) {
        this.mechId = e.id;
      } else {
        this.mechId = "";
      }
    },
    inputChange(e) {
      //返回搜索结果集合,一般用不到
      console.log(e);
    },
    getList() {
      const dateString = this.FormData.appointmentTime;
      const date = new Date(dateString); // 如果在中国或系统时区为中国，则此日期已经是CST
      // 获取当前日期
      const today = date;
      // 创建一个数组来存储日期和对应的星期
      const dateList = [];

      // 循环30次，每次增加一天
      for (let i = 0; i < 30; i++) {
        // 复制当前日期以避免修改原始日期对象，并增加i天
        const nextDate = new Date(
          today.getFullYear(),
          today.getMonth(),
          today.getDate() + i
        );

        // 获取年份、月份和日期
        const year = nextDate.getFullYear();
        const month = nextDate.getMonth() + 1; // 月份从0开始，所以需要加1
        const day = nextDate.getDate();

        // 获取星期几（0代表周日，1代表周一，以此类推）
        const weekday = nextDate.getDay();
        const weekdays = [
          "周日",
          "周一",
          "周二",
          "周三",
          "周四",
          "周五",
          "周六",
        ];
        const weekdayName = weekdays[weekday];

        // 将日期、星期几添加到数组中
        dateList.push({
          date: `${month < 10 ? "0" + month : month}.${
            day < 10 ? "0" + day : day
          }`,
          weekday: weekdayName,
          time: `${year}-${month < 10 ? "0" + month : month}-${
            day < 10 ? "0" + day : day
          }`,
        });
      }
      this.dateList = dateList;
      this.FormData.appointmentTime = this.dateList[0].time;
      this.getAppointmentTimeList();
      // 输出日期列表和对应的星期几
    },
    back() {
      uni.navigateBack();
    },
    receiveDataFromChild(data) {
      console.log(data, "22222");
    },
    changeDate(index, data) {
      this.dateChange = index;
      this.FormData.appointmentTime = data.time;
    },
    changeTime(index, data) {
      this.timeChange = index;
      this.FormData.appointmentTimePeriod = data.timePeriod;
    },
    validateInput() {
      uni.navigateTo({
        url: "/pages/map/order",
      });
    },
  },
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
    },
    fontSizeSuperLarge() {
      return this.$store.state.fontSizeSuperLarge; // 从Vuex获取大号字体大小
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: #f5f6f7;
}

.box {
  width: 100vw;
  height: 100vh;
  background-color: #f4f8fc;
}

.boxs {
  margin: 32rpx;
}

.content {
  padding: 30rpx 40rpx;
  height: 100%;
  width: 100%;
  display: flex;
  gap: 15rpx;
  flex-direction: column;
  align-items: center;
  position: relative;

  background: linear-gradient(
    171deg,
    #d1efff 0%,
    rgba(252, 245, 255, 0.32) 48%,
    #feffff 100%
  );
  box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.06);
  border-radius: 32rpx;
  opacity: 0.79;

  .g_background {
    width: 686rpx;
    height: 180rpx;
    position: absolute;
    top: 0;
  }

  .cell {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;

    .selector {
      position: relative;

      .selector-item {
        display: flex;
        padding: 12rpx 0 0 31rpx;
        width: 310rpx;
        height: 60rpx;
        background: #ffffff;
        border-radius: 30rpx;
        border: 2rpx solid #dddddd;
        font-weight: 500;
        font-size: 24rpx;
        color: #444444;
        line-height: 33rpx;
        font-style: normal;

        &:hover {
          color: #aaa;
          border: solid 1rpx rgba(200, 200, 200, 0.7);
        }
      }

      .arrow {
        width: 28rpx;
        height: 28rpx;
        position: absolute;
        top: 16rpx;
        right: 32rpx;
      }
    }
  }

  .cells {
    margin-bottom: 20rpx;

    .text {
      margin-bottom: 20rpx;
    }
  }

  .report {
    width: 100%;
    display: flex;
    flex-direction: column;

    .title {
      display: flex;
      align-items: center;
      // margin-bottom: 50rpx;
      // position: absolute;

      .round {
        width: 18rpx;
        height: 18rpx;
        background: #057ffe;
      }

      .text {
        font-weight: 600;
        font-size: 36rpx;
        color: #057ffe;
        line-height: 50rpx;
        font-style: normal;
        margin-left: 14rpx;
      }
    }

    .type {
      position: absolute;
      width: 610rpx;
    }

    .phone {
      padding-top: 50rpx;
    }

    .text {
      width: 100%;
      color: #333;
    }
  }
}

.btn-box {
  width: 750rpx;
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  margin-bottom: 40rpx;
  padding-bottom: 40rpx;

  ::v-deep button {
    width: 150rpx;
  }
}

.u-border {
  width: 610rpx;
  height: 207rpx;
  background: #f5f7fb;
  border-radius: 20rpx;
  padding: 20rpx;
}

.feedback {
  width: 590rpx;
  height: 80rpx;
  background: #057ffe;
  border-radius: 40rpx;
  background: #057ffe;
  font-weight: 500;
  font-size: 32rpx;
  color: #ffffff;
  line-height: 80rpx;
  letter-spacing: 2px;
  text-align: center;
  font-style: normal;
}

.return {
  width: 282rpx;
  height: 80rpx;
  border-radius: 8rpx;
  border: 2rpx solid #057ffe;
  font-weight: 500;
  font-size: 34rpx;
  color: #057ffe;
  line-height: 80rpx;
  letter-spacing: 2px;
  text-align: center;
  font-style: normal;
}

.popup {
  height: 100%;

  .popup-content {
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    background: #fff;
    border-radius: 35rpx;
    background-color: #fff;
    width: 100%;
    position: relative;
    padding-top: 40rpx;

    .bg {
      width: 750rpx;
      height: 260rpx;
      position: absolute;
      top: 0;
      left: 0;
    }

    :first-child {
      border-radius: 35rpx 35rpx 0 0;
    }

    .popup-title {
      width: 750rpx;
      height: 72rpx;
      padding: 0 52rpx;
      text-align: center;
      z-index: 99999;

      .false {
        width: 28rpx;
        height: 28rpx;
        position: absolute;
        top: 47rpx;
        left: 52rpx;
      }

      .title_icon {
        width: 107rpx;
        height: 42rpx;
        position: absolute;
        top: 44rpx;
        left: 322rpx;
        z-index: 99999;
      }

      .title {
        font-weight: 500;
        font-size: 30rpx;
        color: #141414;
        line-height: 42rpx;
        font-style: normal;
        z-index: 100010;
        position: absolute;
        top: 47rpx;
        left: 285rpx;
      }

      .true {
        width: 40rpx;
        height: 28rpx;
        position: absolute;
        top: 47rpx;
        right: 52rpx;
      }
    }

    .select {
      margin-top: 0rpx;
      z-index: 100001;

      .select-change {
        width: 702rpx;
        height: 88rpx;
        background: #f4f8ff;
        border-radius: 8rpx;
        font-weight: 500;
        font-size: 30rpx;
        color: #057ffe;
        line-height: 88rpx;
        text-align: center;
        font-style: normal;
      }

      .select-item {
        width: 100%;

        display: flex;
        padding: 30rpx;
        align-items: center;
        justify-content: center;
        transition: background-color 0.3s;

        font-weight: 400;
        font-size: 30rpx;
        color: #000000;
        line-height: 42rpx;

        font-style: normal;

        &:active {
          background-color: #f5f5f5;
        }
      }
    }
  }
}

.order {
  margin-top: 34rpx;

  .date_boxs {
    display: flex;

    .items {
      margin-bottom: 40rpx;
      margin-right: 20rpx;

      .weeklist {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0 20rpx;
        padding-bottom: 20rpx;

        .image {
          width: 560rpx;
          // height: 238rpx;
        }

        .week {
          font-weight: 600;
          font-size: 30rpx;
          color: #999999;
          line-height: 56rpx;
          text-align: center;
          font-style: normal;
          margin-bottom: 10rpx;
        }

        .text {
          font-weight: 400;
          font-size: 34rpx;
          color: #999999;
          line-height: 48rpx;
          text-align: center;
          font-style: normal;
        }
      }
    }
  }
}

.places {
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  .placesitem {
    flex: 0 0 auto;
    /* 让盒子按内容宽度宽度 */
    margin: 5px;
    /* 添加间距 */
    text-align: center;
    font-weight: 400;
    font-size: 30rpx;
    color: #666666;
    line-height: 50rpx;
    font-style: normal;
    line-height: 101rpx;
    width: 209rpx;
    height: 101rpx;
    background: #ffffff;
    border-radius: 8rpx;
  }

  .timeChange {
    width: 209rpx;
    height: 101rpx;
    background: #057ffe;
    border-radius: 8rpx;
    font-weight: 600;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 101rpx;
    text-align: center;
    font-style: normal;
  }
}

.f30 {
  font-weight: 500;
  font-size: 30rpx;
  color: #333333;
  line-height: 42rpx;
  text-align: center;
  font-style: normal;
}

.line {
  width: 634rpx;
  height: 1rpx;
  border: 2rpx solid #e7f0ff;
  margin-bottom: 16rpx;
}

.line-bottom {
  border-bottom: #057ffe solid 4rpx;
}

.blue {
  color: #057ffe !important;
}

.input {
  width: 200rpx;
}

.u-border {
  height: 70rpx;
  border-radius: 0;
}

.tips {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  width: 686rpx;
  // height: 144rpx;
  background: #ffffff;
  border-radius: 8rpx;
  margin-top: 30rpx;

  .tips_img {
    width: 90rpx;
    height: 80rpx;
    margin-right: 23rpx;
  }

  .line {
    width: 1rpx;
    height: 78rpx;
    background: #d8d8d8;
    border-radius: 1rpx;
    border: 1rpx solid #979797;
    margin-right: 23rpx;
  }

  .tips_text {
    width: 500rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #444444;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    word-wrap: break-word;
    /* 旧版浏览器支持 */
    overflow-wrap: break-word;
    /* 标准属性 */
  }
}

.backboxs {
  background-color: #fff;
  padding: 10rpx 30rpx;
}

.back {
  width: 40rpx;
  height: 40rpx;
}
</style>