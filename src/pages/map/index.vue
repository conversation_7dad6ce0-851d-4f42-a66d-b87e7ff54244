<template>
	<view style="background-color: #fff;min-height: 100vh;">
		<!-- <navbar :Props="Props" :title='headtitle' @black="black"></navbar> -->
		<view class="container" v-show="!landmarkShow">
			<view class="header">
				<!--绑定点击事件-->
				<view class="tip" @click="goSearch()">
					<view class="" style="width: 80vw;margin-left: 40rpx;color: grey;">
						请输入关键字
					</view>
					<!-- <input class="uni-input" v-model="changeValue" focus @input="clearInput" placeholder="请输入关键字" /> -->
					<!-- <text class="uni-icon" v-if="showClearIcon" @click="clearIcon">&#xe434;</text> -->
					<view class="cacel_button" v-if="showClearIcon" @click="clearIcon">
						<image class="cacel" src="@/static/images/map/cancel.png" mode=""></image>
					</view>
					<view class="search_button">
						<!-- <view class="search_button" @click="nearby_search"> -->
						<image style="z-index: 9999;" class="search" src="@/static/images/map/search.png" mode="">
						</image>
					</view>
				</view>

				<view class="sieving">
					<view class="Province">
						<view class="ProvinceBox" v-for="(item,index) in placeListMore" :key="index"
							@click="selectFun(item.id)">
							<view
								:class="['select',SelectId.includes(item.id)?'line-bottom':'']" class="text">
								{{ item.title }}
							</view>
							<image v-show="SelectId.includes(item.id)" class="line" src="@/static/images/map/line.png"
								mode="">
							</image>
						</view>
					</view>
					<view class="more" @click="more()" :style="{bottom:showMore?'20rpx':'92rpx'}">
						<view v-show="showMore">收起</view>
						<image v-show="showMore" class="put" src="@/static/images/map/put.png" mode=""></image>
						<view v-show="!showMore">更多</view>
						<image v-show="!showMore" class="put" src="@/static/images/map/more.png" mode=""></image>
					</view>
				</view>
			</view>
			<u-popup customStyle="top:600rpx" :show="show" :overlayOpacity="0" :round="10" mode="top" @close="close">
				<view class="conter">
					<view class="result_list" v-for="(report, index) in suggestion" :key="index">
						<image class="rect" src="@/static/images/map/rect.png" mode=""></image>
						<view class="information">
							<view class="title">
								{{report.name}}
							</view>
							<view class="content">
								<view :style="{ fontSize: fontSizeSuperSmall }" class="work">{{report.type_dictText}}
								</view>
							</view>
							<view class="localization">
								<image class="list_locate" src="@/static/images/map/list_locate.png" mode=""></image>
								<view class="distance">{{report.distance}}公里</view>
								<view class="text">{{report.address}}</view>
							</view>
						</view>
						<view class="tips" @click="getDetails(report)">
							<image class="Gohere" src="@/static/images/map/here.png" mode=""></image>
							<view class="date">导航</view>
						</view>
					</view>
					<view class="nomore">
						暂无更多
					</view>
				</view>
			</u-popup>


			<u-popup :show="filter" :round="10" mode="center" @close="filterClose" :overlay="false">
				<view class="filter_boxs">
					<!-- <image class="filterTop" src="@/static/images/map/filterTop.png" mode=""></image> -->
					<view class="place_box">
						<view class="region">
							<view class="f12">范围</view>
							<view class="rangeboxs">
								<view @click="changLately()" :class="lately == true ? 'lately' : ''">离我最近</view>
								<view class="rangeitems">
									<view class="">其他范围</view>
									<u-number-box @change="valChange" style="margin: 0 10rpx;" :min="0" :max="100"
										v-model="range" integer></u-number-box>
									<view>公里</view>
								</view>
							</view>
						</view>
						<view class="government">
							<view class="f12">政务场所</view>
							<view class="places">
								<view class="placesitem" v-for="(item,index) in placeList" :key="index">
									<view :class="['select',SelectId.includes(item.id)?'palceChange':'']"
										@click="selectFun(item.id)">{{item.title}}</view>
								</view>
							</view>
						</view>
						<view class="territory">
							<view class="f12">地区</view>
							<view>
								<view class="main-box">
									<view class="type-list">
										<scroll-view class="text" scroll-y="true" style="height: 480rpx;">
											<view v-for="(item, index) in typeList" :key="index">
												<view :class="currentType === index ? 'active' : ''"
													@click="changeType(index, item.id)" class="list">
													{{ item.name }}
												</view>
											</view>
										</scroll-view>
									</view>
									<view class="type-list">
										<scroll-view class="text" scroll-y="true" style="height: 294rpx;">
											<view v-for="(item, index) in townList" :key="index">
												<view :class="townType === index ? 'active' : ''"
													@click="changeTown(index, item.id)" class="list">
													{{ item.name }}
												</view>
											</view>
										</scroll-view>
									</view>
									<view class="type-list">
										<scroll-view class="text" scroll-y="true" style="height: 294rpx;">
											<view v-for="(item, index) in teamList" :key="index">
												<view :class="village === index ? 'active' : ''"
													@click="changeVillage(index, item.id,item.parentId)" class="list">
													{{ item.name }}
												</view>
											</view>
										</scroll-view>
									</view>
								</view>
							</view>
						</view>
						<view class="buttons">
							<view class="reset" @click="reset()">重置</view>
							<view class="define" @click="define()">确定</view>
						</view>
					</view>
				</view>
			</u-popup>

			<!--地图容器-->
			<view class="" style="position: relative;">
				<!-- 				<map v-show="MapShow" :enable-satellite="isMap" id="myMap" :circles="circles" @markertap="onMarkerTap"
					:markers="markers" style="width:100%;" :style="{height:mapHeight?'20vh':'40vh'}"
					:longitude="longitudeCenter" :latitude="latitudeCenter" scale='12' @regionchange='regionchange'>
				</map> -->

				<view id="mapContainer" style="width:100%;" :style="{height:mapHeight?'20vh':'40vh'}"></view>
				<view v-show="bubblingShow" class="bubbling">
					<view class="bubblingText">{{ bubblingList.name }}</view>
					<view class="bubblingDialog">
						<view class="bubblingText">{{ bubblingList.address }}</view>
						<view class="nav_boxs" @click="getDetails(bubblingList)">
							<view class="text">
								导航
							</view>
							<image class="nav_icon" src="@/static/images/map/nav_icon.png" mode=""></image>
						</view>
					</view>
				</view>
			</view>

			<view v-show="!mapHeight" class="filter_icon" @click="gofilter()">
				<image class="background" src="@/static/images/map/filter.png" mode=""></image>
			</view>
			<image v-show="!mapHeight" @click="getCurrent()" class="current" src="@/static/images/map/orient.png">
			</image>
			<!-- 			<view v-show="MapShow" class="Status" @click="ListShow()">
				<image class="background" src="@/static/images/map/list_background.png" mode=""></image>
				<view class="text">
					列表
				</view>
			</view>
			<view v-show="!MapShow" class="Status" @click="PlatShow()">
				<image class="background" src="@/static/images/map/map_background.png" mode=""></image>
				<view class="text">
					地图
				</view>
			</view> -->

			<view class="reportboxs">
				<image class="listbg" src="@/static/images/map/detail_background.png" mode=""></image>
				<view class="changeHeight" @click="changeHeight()"></view>
				<view class="reportbox">
					<scroll-view scroll-y="true" :style="{height:mapHeight?'63vh':'43vh'}">
						<view class="map_list" v-for="(report, index) in reportList" :key="index">
							<image class="rect" src="@/static/images/map/rect.png" mode=""></image>
							<view class="information" @click="goDetails(report.id)">
								<view class="title">
									{{report.name}}
								</view>
								<view class="localization">
									<image class="list_locate" src="@/static/images/map/list_locate.png" mode="">
									</image>
									<view :style="{ fontSize: fontSizeSmall }" class="distance">{{report.distance}}公里
									</view>
									<view :style="{ fontSize: fontSizeSmall }" class="text">{{report.address}}</view>
								</view>
							</view>
							<view class="tips" @click="getDetails(report)">
								<image class="Gohere" src="@/static/images/map/here.png" mode=""></image>
								<view :style="{ fontSize: fontSizeSmall }" class="date">导航</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>

		<!-- 		<view v-show="landmarkShow">
			<landmark @custom-event="receiveFromChild" ref='xText' :longitude="marklongitude" :latitude="marklatitude">
			</landmark>
		</view> -->
	</view>
</template>
<script src="./jweixin-1.3.2.js" type="text/javascript" charset="utf-8"></script>
<script>
	import {
		wgs84togcj02,
		getErrorMsg
	} from '@/api/map/geolocation'
	import coordtransform from 'coordtransform';
	import navbar from '@/components/Navbar/index.vue'
	import landmark from '../map/landmark.vue';
	import {
		getMapList,
		getWorkList,
		getManagement,
		getTreestreet,
		getLocation
	} from '@/api/map/map.js'
	import QQMapWX from '@/utils/qqmap-wx-jssdk.js'
	import wxs from 'weixin-js-sdk'

	import {
		result
	} from 'lodash';
	export default {
		components: {
			landmark,
			navbar
		},
		data() {
			return {
				loginForm: {
					username: '15571578328',
					password: 'Hwj@15571578328',
					captcha: '',
					checkKey: ''
				},
				headtitle: '政务服务地图',
				Props: {
					imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: "", //导航高度(动态获取传参)
					bgColor: "", //导航栏背景色,不传参则默认#9CF
					capsuleTop: "", //胶囊顶部距离(动态获取传参)
					textColor: "", //导航标题字体颜色(不传默认#FFF)
					iconColor: "", //icon图标颜色(不传默认#FFF)
					blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
					backText: "", //默认字体(返回)
				},
				iconPath: '',
				defaultFilter: true,
				defaultRange: true,
				isMap: true,
				MapShow: true,
				circles: [],
				title: 'map',
				latitude: '29.985364',
				longitude: '113.938780',
				latitudeCenter: '29.985364',
				longitudeCenter: '113.938780',
				marklatitude: '',
				marklongitude: '',
				markers: [],
				// 腾讯地图实例
				qqmapsdk: '',
				// 输入框值
				changeValue: '',
				// 联想值
				suggestion: [],
				// 防抖
				time: null,
				nowProvince: 0,
				province: [{
						producingLocation: null,
						producingName: '办事服务',
						provinceCode: 37
					},
					{
						producingLocation: null,
						producingName: '自助终端机',
						provinceCode: 32
					}
				],
				reportList: [],
				workIndex: 0,
				workList: [],
				rangeIndex: 6,
				rangeList: [{
						value: '30000',
						label: '30公里内'
					},
					{
						value: '25000',
						label: '25公里内'
					},
					{
						value: '20000',
						label: '20公里内'
					},
					{
						value: '15000',
						label: '15公里内'
					},
					{
						value: '10000',
						label: '10公里内'
					},
					{
						value: '5000',
						label: '5公里内'
					},
					{
						value: '0',
						label: '离我最近'
					}
				],
				governmentWorkIndex: '',
				// radius: '0',
				governmentWork: '',
				address: '当前位置',
				landmarkShow: false,
				show: false,
				showClearIcon: false,
				filter: false,
				SelectId: [
					1
				],
				placeList: [{
						id: 0,
						title: '全部'
					}, {
						id: 1,
						title: '办事大厅'
					},
					{
						id: 2,
						title: '自助终端机'
					},
					{
						id: 3,
						title: '便民服务站'
					}, {
						id: 4,
						title: '医院'
					},
					{
						id: 5,
						title: '卫生院'
					}, {
						id: 6,
						title: '银行'
					},
					{
						id: 7,
						title: '充电桩'
					}, {
						id: 8,
						title: '停车场'
					}
				],
				typeList: [{
						id: '421221',
						name: '全部'
					},
					{
						id: '421221',
						name: '嘉鱼县'
					}
				],
				townList: [],
				teamList: [],
				currentType: '',
				nowPlace: 7,
				village: -1,
				indexAddress: '',
				townType: '',
				administrativeLevel: '',
				range: 0,
				lately: true,
				mapHeight: false,
				destLongitude: '',
				destLatitude: '',
				bubblingShow: false,
				userInfo: {},
				bubblingAdress: '',
				bubblingName: '',
				showMore: false,
				placeListMore: [],
				bubblingList: {},
				map: null,
				newmarkers: []
			}
		},
		mounted() {
			this.loadSDK(() => {
				this.initMap();
			});
		},
		onLoad(data) {
			const that = this
			if (data.nowProvince) {
				that.nowProvince = Number(data.nowProvince)
			}
			if (data.longitude) {
				that.receiveFromChild(data)
			}
			that.userInfo =
				typeof this.$store.state.user.userInfo == 'string' ?
				JSON.parse(this.$store.state.user.userInfo) :
				this.$store.state.user.userInfo
			// uni-app 授权弹出框
			// that.getLocation()
			// that.getList()
			this.placeListMore = this.placeList.slice(0, 3);
			// that.getWork()



		},
		methods: {
			loadSDK(callback) {
				const script = document.createElement('script');
				script.src = 'https://api.tianditu.gov.cn/api?v=4.0&tk=561e846f9906522baf3ad3c7ee40ccda';
				script.onload = () => {
					callback();
				};
				document.head.appendChild(script);
			},
			initMap() {
				const that = this
				// 获取地图容器元素
				const mapContainer = document.getElementById('mapContainer');
				// 初始化天地图
				that.map = new T.Map(mapContainer);
				// 设置地图中心点和缩放级别
				that.map.centerAndZoom(new T.LngLat(that.longitudeCenter, that.latitudeCenter), 14);

				that.map.addEventListener('click', that.handleMapClick);

				that.getList(that.map)
				that.getweizhi(that.map)
			},
			handleMapClick(event) {
				this.bubblingShow = false
			},
			addCustomMarkers() {
				this.markers.forEach(item => {
					item.lat = Number(item.latitude)
					item.lng = Number(item.longitude)
				});
				const positions = this.markers
				positions.forEach(pos => {
					const customIcon = new T.Icon({
						iconUrl: pos.iconPath, // 自定义图标的URL
						iconSize: new T.Point(pos.width, pos.height), // 图标尺寸
						iconAnchor: new T.Point(32, 32) // 图标锚点
					});
					const lnglat = new T.LngLat(Number(pos.longitude), Number(pos.latitude));
					const marker = new T.Marker(lnglat, {
						icon: customIcon
					});
					this.newmarkers.push(marker);
					// marker.bindPopup(`<div>${pos.name}</div>`);
					marker.addEventListener('click', () => {
						this.handleMarkerClick(pos);
					});
					this.map.addOverLay(marker);
				});
				// var marker = new T.Marker(new T.LngLat(116.411794, 39.9068));
				// map.addOverLay(marker);
			},
			zhuce(point) {

			},
			async handleMarkerClick(pos) {
				// 标注点击事件处理函数

				const that = this
				const id = pos.id
				const res = await getManagement({
					id: id,
					latitude: that.latitude,
					longitude: that.longitude
				})
				that.longitudeCenter = res.result.longitude
				that.latitudeCenter = res.result.latitude
				that.bubblingShow = true
				that.bubblingList = res?.result
				that.map.centerAndZoom(new T.LngLat(that.longitudeCenter, that.latitudeCenter), 14);

			},
			//获取地图列表
			async getList(map) {
				const that = this
				that.newmarkers.forEach(marker => {
					that.map.removeOverLay(marker);
				});
				that.markers = []
				let type = ''
				if (that.SelectId.length > 0) {
					type = that.SelectId.join(',');
					if (type == 0) {
						type = '1,2,3,4,5,6,7,8'
					}
				} else {
					type = ''
				}
				if (that.governmentWork == '全部') {
					that.governmentWork = ''
				}
				// if (that.radius == '0') {
				// 	that.radiusData = ''
				// } else {
				// 	that.radiusData = that.radius
				// }
				const res = await getMapList({
					longitude: that.longitude,
					latitude: that.latitude,
					type: type,
					// radius: that.radiusData,
					// governmentWork: that.governmentWork,
					// indexAddress: that.indexAddress,
					// administrativeLevel: that.administrativeLevel
					// name: that.changeValue
				})
				that.circles = []
				that.markers = [{ // 获取返回结果，放到mks数组中
					title: '当前定位',
					id: 999,
					iconPath: '../../static/images/map/red.png',
					latitude: that.latitude,
					longitude: that.longitude,
					width: 40,
					height: 57,
				}]
				that.circles.push({
					latitude: that.latitude,
					longitude: that.longitude,
					color: '#4ccfffff',
					fillColor: '#7cb5ec88',
					radius: 0,
					strokeWidth: 1
				})
				that.reportList = res?.result
				for (let i = 0; i < that.reportList.length; i++) {
					if (that.reportList[i].type == 1) {
						that.iconPath = '../../static/images/map/hall.png'
					} else if (that.reportList[i].type == 2) {
						that.iconPath = '../../static/images/map/selfHelp.png'
					} else if (that.reportList[i].type == 3) {
						that.iconPath = '../../static/images/map/service.png'
					} else if (that.reportList[i].type == 4) {
						that.iconPath = '../../static/images/map/hospital.png'
					} else if (that.reportList[i].type == 5) {
						that.iconPath = '../../static/images/map/Health.png'
					} else if (that.reportList[i].type == 6) {
						that.iconPath = '../../static/images/map/bank.png'
					} else if (that.reportList[i].type == 7) {
						that.iconPath = '../../static/images/map/cdz.png'
					} else if (that.reportList[i].type == 8) {
						that.iconPath = '../../static/images/map/tcc.png'
					}
					var a = {
						id: that.reportList[i].id,
						iconPath: that.iconPath,
						latitude: Number(that.reportList[i].latitude),
						longitude: Number(that.reportList[i].longitude),
						width: 28,
						height: 37
					}
					that.markers.push(a);
				}
				that.addCustomMarkers(map)
			},
			getCurrent() {
				// 作用域问题
				const that = this
				wx.getLocation({
					isHighAccuracy: true,
					type: 'gcj02', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
					success: function(res) {
						// alert(res.latitude)
						// alert(res.longitude)
						// 原始坐标
						// let originalLat = 29.985341;
						// let originalLng = 113.93879;
						// // 目标坐标
						// let targetLat = 29.98776;
						// let targetLng = 113.93328;
						// let newdiffLat =  diffLat.toFixed(6)
						// let newdiffLng =  diffLng.toFixed(6)
						// console.log(newdiffLat, '1111')
						// console.log(newdiffLng, '1111')
						let diffLat = 0.002419;
						let diffLng = -0.00551;
						that.latitude = Number(res.latitude) + diffLat
						that.longitude = Number(res.longitude) + diffLng
						that.latitude = that.latitude.toString()
						that.longitude = that.longitude.toString()
						that.longitudeCenter = that.longitude
						that.latitudeCenter = that.latitude
						map.setCenter(new T.LngLat(this.longitudeCenter, this.latitudeCenter), 14);
						that.getList(that.map)
					}
				});
			},
			getweizhi(map) {
				const that = this
				wx.getLocation({
					isHighAccuracy: true,
					type: 'gcj02', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
					success: function(res) {
						// let originalLat = 29.985415;
						// let originalLng = 113.93881;
						// // 目标坐标
						// let targetLat = 29.988096;
						// let targetLng = 113.933495;
						// 计算差值
						let diffLat = 0.002419;
						let diffLng = -0.00551;
						that.latitude = Number(res.latitude) + diffLat
						that.longitude = Number(res.longitude) + diffLng
						that.latitude = that.latitude.toString()
						that.longitude = that.longitude.toString()
						that.longitudeCenter = that.longitude
						that.latitudeCenter = that.latitude
						map.panTo(new T.LngLat(that.longitudeCenter, that.latitudeCenter), 14);
						that.getList(that.map)
					}
				});
			},
			more() {
				if (this.showMore) {
					this.showMore = false
					this.placeListMore = this.placeList.slice(0, 3);
				} else {
					this.showMore = true
					this.placeListMore = this.placeList
				}
			},
			changeHeight() {
				if (this.mapHeight) {
					this.mapHeight = false
				} else {
					this.mapHeight = true
				}
			},
			changLately() {
				if (this.lately == false) {
					this.lately = true
				}
				// this.radius = '0'
				this.range = 0
			},
			valChange(e) {
				// this.radius = e.value
				this.lately = false
			},

			async gofilter() {
				if (this.userInfo.mobilePhone) {
					uni.navigateTo({
						url: '/pages/map/order?name=' + '嘉鱼政务服务中心'
					});
				} else {
					uni.showToast({
						title: '请先完成实名认证',
						icon: 'none'
					})
				}
			},

			reset() {
				this.SelectId = [1]
				this.village = ''
				this.currentType = ''
				this.townType = ''
				this.indexAddress = ''
				this.teamList = []
				this.townList = []
				this.administrativeLevel = ''
				// this.radius = '0'
				this.range = 0
				this.lately = true
			},
			define() {
				this.getList()
				this.filter = false
			},
			selectFun(id) {
				if (id == 0) {
					this.SelectId = [0]
					let index = this.SelectId.indexOf(id) // 求出当前id的所在位置
				} else {
					if (!this.SelectId.includes(id)) {
						this.SelectId.push(id) // 判断已选列表中是否存在该id，不是则追加进去
					} else {
						let index = this.SelectId.indexOf(id) // 求出当前id的所在位置
						this.SelectId.splice(index, 1) // 否则则删除
					}
					const requiredNumbers = [1, 2, 3, 4, 5, 6, 7, 8];
					if (requiredNumbers.every(num => this.SelectId.includes(num))) {
						this.SelectId = [0]
					} else {
						this.SelectId = this.SelectId.filter(element => element !== 0);
					}
				}
				this.getList()
			},
			//获取政务办事
			async getTreeList() {
				const res = await getTreestreet({
					parentId: '421221',
					type: 6,
				})
				this.townList = res?.result
				this.townList.unshift({
					id: '421221',
					name: '全部'
				})
			},
			changeVillage(index, id, parentId) {
				this.village = index
				this.indexAddress = id
				this.administrativeLevel = 3
			},
			async changeTown(index, id) {
				if (index == 0) {
					this.teamList = []
				} else {
					const res = await getTreestreet({
						parentId: id,
						type: 6,
					})
					this.teamList = res?.result
					this.teamList.unshift({
						id: id,
						parentId: id,
						name: '全部'
					})
				}
				this.indexAddress = id
				this.administrativeLevel = 2
				this.townType = index
				this.village = ''
			},
			async changeType(index, id) {
				if (index == 1) {
					this.getTreeList()
					this.indexAddress = id
					this.administrativeLevel = 1
				} else if (index == 0) {
					this.townList = []
					this.teamList = []
					this.indexAddress = ''
					this.administrativeLevel = ''
				}
				this.currentType = index
				this.townType = ''
				this.village = ''
			},
			advanced() {
				this.filter = true
			},
			clearIcon: function() {
				this.changeValue = '';
				this.showClearIcon = false;
			},
			clearInput: function(event) {
				this.changeValue = event.detail.value;
				if (event.detail.value.length > 0) {
					this.showClearIcon = true;
				} else {
					this.showClearIcon = false;
				}
			},
			filterClose() {
				this.filter = false
			},
			close() {
				this.show = false
			},
			async onMarkerTap(e) {
				const that = this
				const {
					markerId
				} = e.detail;
				const id = this.reportList[markerId].id

				const res = await getManagement({
					id: id,
					latitude: that.latitude,
					longitude: that.longitude
				})
				that.longitudeCenter = res.result.longitude
				that.latitudeCenter = res.result.latitude
				that.bubblingShow = true
				that.bubblingList = res?.result
				// this.getDetails(id)
				// 在这里编写你需要执行的代码
			},
			receiveFromChild(msg) {
				const that = this
				that.longitude = msg.longitude
				that.latitude = msg.latitude
				that.address = msg.title
				that.landmarkShow = false
				that.getList()
			},
			async landmarklist() {
				uni.navigateTo({
					url: '/pages/map/landmark?longitude=' + this.longitude + '&latitude=' + this.latitude
				});
			},
			goSearch() {
				uni.navigateTo({
					url: '/pages/map/Search?latitude=' + this.latitude + '&longitude=' + this
						.longitude
				});
			},
			async goDetails(id) {
				uni.navigateTo({
					url: '/pages/map/newDetails?id=' + id + '&latitude=' + this.latitude + '&longitude=' + this
						.longitude
				});
			},
			wgs84ToGcj02(latitude, longitude) {
				// 使用 coordtransform 库将 WGS84 坐标转换为 GCJ02 坐标
				const gcj02Coord = coordtransform.wgs84togcj02(longitude, latitude);
				return {
					latitude: gcj02Coord[1],
					longitude: gcj02Coord[0]
				};
			},
			// 导航
			getDetails(data) {
				const that = this
				that.destLongitude = that.wgs84ToGcj02(data.latitude, data.longitude).longitude
				that.destLatitude = that.wgs84ToGcj02(data.latitude, data.longitude).latitude
				that.destLongitude = that.destLongitude.toFixed(5)
				that.destLatitude = that.destLatitude.toFixed(5)
				const url = `/pages-sub/location/index?lat=` + Number(that.destLatitude) + `&lon=` + Number(that
						.destLongitude) +
					`&name=目的地&address=目的地`
				wxs.miniProgram.navigateTo({
					url: url
				})

				// uni.showActionSheet({
				// 	itemList: ['高德地图', '百度地图', '腾讯地图'],
				// 	success: function(res) {
				// 		that.guide(res.tapIndex)
				// 	},
				// 	fail: function(res) {
				// 		console.log(res.errMsg);
				// 	}
				// });

			},
			// 选择地图
			guide(signMap) {
				uni.showLoading({
					title: '跳转中'
				});
				let position = {
					latitude: this.destLatitude,
					longitude: this.destLongitude,
					name: '目标点'
				}
				if (position.name && position.name != '') {
					//地点位置position.name 地点经纬度lng lat
					var lng = position.longitude; //经度39.9
					var lat = position.latitude; //纬度116.4
					if (signMap == 0) {
						// 高德地图
						uni.getSystemInfo({
							success: (res) => {
								if (res.platform == "android") {
									window.location.href =
										"androidamap://viewMap?sourceApplication=appname&poiname=" + position
										.name + "&lat=" + lat + "&lon=" + lng + "&dev=0";
									//判断是否跳转
									setTimeout(function() {
										let hidden = window.document.hidden || window.document
											.mozHidden || window.document.msHidden || window.document
											.webkitHidden
										if (typeof hidden == "undefined" || hidden == false) {
											//调用高德地图
											window.location.href =
												"https://uri.amap.com/marker?position=" + lng + "," +
												lat + "&name=" + position.name;
										}
									}, 2000);
								} else {
									window.location.href =
										"iosamap://viewMap?sourceApplication=appname&poiname=" + position
										.name + "&lat=" + lat + "&lon=" + lng + "&dev=0";
									//判断是否跳转
									setTimeout(function() {
										let hidden = window.document.hidden || window.document
											.mozHidden || window.document.msHidden || window.document
											.webkitHidden
										if (typeof hidden == "undefined" || hidden == false) {
											//调用高德地图
											window.location.href =
												"https://uri.amap.com/marker?position=" + lng + "," +
												lat + "&name=" + position.name;
										}
									}, 2000);
								}
							}
						})
					} else if (signMap == 1) {
						// // 百度地图
						wxs.miniProgram.navigateTo({
							url: `/pages-sub/location/index?lat=39.961488&lon=116.786174&name=东方夏威夷&address=武汉市蔡甸区大沌特路1号`
						})
						// uni.getSystemInfo({
						// 	success: (res) => {
						// 		if (res.platform == "android") {
						// 			let d = new Date();
						// 			let t0 = d.getTime();
						// 			window.location.href =
						// 				"androidamap://viewMap?sourceApplication=appname&poiname=" + position
						// 				.name + "&lat=" + lat + "&lon=" + lng + "&dev=0";
						// 			//由于打开需要1～2秒，利用这个时间差来处理－－打开app后，返回h5页面会出现页面变成app下载页面，影响用户体验
						// 			var delay = setInterval(function() {
						// 				var d = new Date();
						// 				var t1 = d.getTime();
						// 				if (t1 - t0 < 3000 && t1 - t0 > 2000) {
						// 					window.location.href =
						// 						"http://api.map.baidu.com/marker?location=" + lat +
						// 						"," + lng + "&title=" + position.name +
						// 						"&content=地点&output=html&src=webapp.baidu.openAPIdemo";
						// 				}
						// 				if (t1 - t0 >= 3000) {
						// 					clearInterval(delay);
						// 				}
						// 			}, 1000);
						// 		} else {
						// 			let d = new Date();
						// 			let t0 = d.getTime();
						// 			window.location.href =
						// 				"iosamap://viewMap?sourceApplication=appname&poiname=" + position
						// 				.name + "&lat=" + lat + "&lon=" + lng + "&dev=0";
						// 			//由于打开需要1～2秒，利用这个时间差来处理－－打开app后，返回h5页面会出现页面变成app下载页面，影响用户体验
						// 			let delay = setInterval(function() {
						// 				var d = new Date();
						// 				var t1 = d.getTime();
						// 				if (t1 - t0 < 3000 && t1 - t0 > 2000) {
						// 					window.location.href =
						// 						"http://api.map.baidu.com/marker?location=" + lat +
						// 						"," + lng + "&title=" + position.name +
						// 						"&content=地点&output=html&src=webapp.baidu.openAPIdemo";
						// 				}
						// 				if (t1 - t0 >= 3000) {
						// 					clearInterval(delay);
						// 				}
						// 			}, 1000);
						// 		}
						// 	}
						// })
					} else {
						wx.openLocation({
							latitude: Number(this.destLatitude), // 纬度，浮点数，范围为90 ~ -90
							longitude: Number(this.destLongitude), // 经度，浮点数，范围为180 ~ -180。
							name: '', // 位置名
							address: '', // 地址详情说明
							scale: 16, // 地图缩放级别,整型值,范围从1~28。默认为最大
							infoUrl: '', // 在查看位置界面底部显示的超链接,可点击跳转
							success: function(res) {
								console.log(res, '1111')
							}
						})
						// // 腾讯地图
						// uni.getSystemInfo({
						// 	success: (res) => {
						// 		if (res.platform == "android") {
						// 			window.location.href =
						// 				"androidamap://viewMap?sourceApplication=appname&poiname=" + position
						// 				.name + "&lat=" + lat + "&lon=" + lng + "&dev=0";
						// 			//判断是否跳转
						// 			setTimeout(function() {
						// 				let hidden = window.document.hidden || window.document
						// 					.mozHidden || window.document.msHidden || window.document
						// 					.webkitHidden
						// 				if (typeof hidden == "undefined" || hidden == false) {
						// 					//调用腾讯地图
						// 					window.location.href =
						// 						`https://apis.map.qq.com/uri/v1/marker?marker=coord:(${lat},${lng})&addr:${position.name}`
						// 				}
						// 			}, 2000);
						// 		} else {
						// 			window.location.href =
						// 				"iosamap://viewMap?sourceApplication=appname&poiname=" + position
						// 				.name + "&lat=" + lat + "&lon=" + lng + "&dev=0";
						// 			//判断是否跳转
						// 			setTimeout(function() {
						// 				let hidden = window.document.hidden || window.document
						// 					.mozHidden || window.document.msHidden || window.document
						// 					.webkitHidden
						// 				if (typeof hidden == "undefined" || hidden == false) {
						// 					//调用高德地图
						// 					window.location.href =
						// 						`https://apis.map.qq.com/uri/v1/marker?marker=coord:(${lat},${lng})&addr:${position.name}`
						// 				}
						// 			}, 2000);
						// 		}
						// 	}
						// })
					}
				} else {
					uni.showToast({
						title: '暂不知道该地点位置',
						icon: 'none',
						duration: 2000,
					});
				}
			},

			// rangePickerChange(e) {
			// 	const that = this
			// 	that.radius = that.rangeList[e.detail.value].value
			// 	that.rangeIndex = e.detail.value
			// 	that.defaultRange = false
			// 	// that.getLocation()
			// 	setTimeout(function() {
			// 		that.getList()
			// 	}, 1000);
			// },

			workPickerChange(e) {
				this.defaultFilter = false
				this.governmentWorkIndex = e.detail.value
				this.governmentWork = this.workList[e.detail.value]
				this.getList()
			},

			//获取政务办事
			async getWork() {
				const res = await getWorkList({
					longitude: this.longitude,
					latitude: this.latitude,
					// radius: 300000
				})
				this.workList = res?.result
				let a = '全部'
				this.workList.unshift(a)
			},
			ListShow() {
				this.MapShow = false
				this.getList()
			},
			// 地图/列表切换
			PlatShow() {
				this.MapShow = true
			},
			//改变类目
			CategoryChange(e) {
				this.regionindex = 0;
				this.region = [{
					areaName: '请选择',
					value: '-1'
				}];
				this.Categoryindex = e.target.value;
				this.getarea(this.Category[e.target.value].value)

			},

			getLocation() {
				const that = this
				if (navigator.geolocation) {
					navigator.geolocation.getCurrentPosition(
						position => {
							that.latitude = position.coords.latitude; // 纬度
							that.longitude = position.coords.longitude; // 经度
							uni.showModal({
								content: position.coords.latitude + position.coords.longitude,
								showCancel: false
							});
							// 原始坐标
							let originalLat = 36.665519;
							let originalLng = 117.124742;
							// 目标坐标
							let targetLat = 36.661561;
							let targetLng = 117.127529;
							// 计算差值
							let diffLat = targetLat - originalLat;
							let diffLng = targetLng - originalLng;
							that.latitude = Number(that.latitude) + diffLat
							that.longitude = Number(that.longitude) + diffLng
							that.latitude = that.latitude.toString()
							that.longitude = that.longitude.toString()
							that.latitudeCenter = that.latitude
							that.longitudeCenter = that.longitude
							that.getList()
						},
						error => {
							that.getList()
						}
					);
				} else {
					that.getList()
				}
			},
			// 根据ip获取地址
			// async getLocation() {
			// 	var that = this
			// 	const url1 = 'https://apis.map.qq.com/ws/location/v1/ip' // 关键字查询
			// 	that.$jsonp(url1, {
			// 		key: 'G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV',
			// 		output: 'jsonp',
			// 	}).then(res => {
			// 		console.log(res, '1111')
			// 		// that.longitude = res.result.location.lng
			// 		// that.latitude = res.result.location.lat
			// 		let latitude = res.result.location.lat
			// 		let longitude = res.result.location.lng
			// 		const KEY = "G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV";
			// 		let url = "https://apis.map.qq.com/ws/coord/v1/translate"
			// 		this.$jsonp(url, {
			// 			key: KEY,
			// 			output: "jsonp",
			// 			locations: latitude + ',' + longitude,
			// 			type: 3
			// 		}).then(res => {
			// 			console.log(res, '22222')
			// 			that.longitude = res.locations[0].lng
			// 			that.latitude = res.locations[0].lat
			// 			that.getList()
			// 			that.getWork()
			// 		}).catch(err => {
			// 			console.log(err)
			// 		})

			// 	}).catch(err => {
			// 		console.log(err)
			// 	})
			// },
			// 事件触发，调用接口
			async nearby_search() {
				const that = this
				if (!that.changeValue) {
					that.suggestion = []
					uni.showToast({
						title: '请输入值！',
						icon: 'error',
						duration: 2000
					});
					return
				} else {
					// 调用接口
					// 输入框防抖
					clearTimeout(that.time)
					that.time = setTimeout(() => {
						that.getsuggestion()
					}, 500)
				}
			},
			// 事件触发，调用接口
			async getsuggestion() {
				const that = this
				const res = await getMapList({
					longitude: that.longitude,
					latitude: that.latitude,
					// radius: that.radiusData,
					// governmentWork: that.governmentWork,
					name: that.changeValue,
				})

				that.suggestion = res.result
				that.show = true

			},
			regionchange() {
				this.bubblingShow = false
			},
			// 模块切换
			changeProvince(e) {
				this.nowProvince = e;
				this.getList()
			},
			changePlace(e) {
				this.nowPlace = e;
			},
		},
		computed: {
			fontSizeSuperSmall() {
				return this.$store.state.fontSizeSuperSmall; // 从Vuex获取小号字体大小
			},
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		}
	}
</script>
<style lang="scss">
	.container {
		background-color: transparent;
		/* 完全透明背景 */

		.header {
			background-color: #ffffff;
			box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.4);
			border-radius: 0rpx 0rpx 20rpx 20rpx;
			z-index: 10090;
			padding-top: 53rpx;


			.sieving {
				display: flex;
				position: relative;
				align-items: flex-start;
				padding-bottom: 40rpx;

				.Province {
					text-align: center;
					font-family: -apple-system;
					background-color: #ffffff;
					display: flex;
					flex-wrap: wrap;
					background-color: white;
					width: 100vw;
					padding: 40rpx 40rpx 20rpx 40rpx;
					border-bottom-left-radius: 100rpx;
					border-bottom-right-radius: 100rpx;

					.ProvinceBox {
						flex: 0 0 auto;
						margin: 0 12px;
						margin-bottom: 22rpx;

						.text {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 34rpx;
							color: #565D6E;
							line-height: 30rpx;
							font-style: normal;
						}

						.line-bottom {
							// font-weight: 600;
							font-size: 34rpx;
							color: #141414;
							line-height: 30rpx;
							font-style: normal;
						}

						.line {
							width: 55rpx;
							height: 10rpx;
						}
					}

				}

				.more {
					position: absolute;
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 30rpx;
					color: #5CACFF;
					line-height: 42rpx;
					text-align: left;
					font-style: normal;
					margin-top: 2vh;
					right: 10vw;

					.put {
						margin-left: 10rpx;
						width: 27rpx;
						height: 23rpx;
					}
				}


			}



			.tip {
				width: 710rpx;
				height: 60rpx;
				margin: 0 20rpx;
				display: flex;
				align-items: center;
				background-color: #fff;
				border-radius: 3px;
				z-index: 200;
				border-radius: 30rpx;
				border: 2rpx solid #DDDDDD;

				.uni-input {
					flex: 1;
					height: 20rpx;
					padding-left: 40rpx;
				}

				span {
					width: 40px;
					color: #8b8c8f;
				}

				.search {
					width: 32rpx;
					height: 32rpx;

					z-index: 99999;
				}
			}

			.screen {
				display: flex;
				align-items: center;
				justify-content: space-around;
				background-color: #FFF;
				padding: 30rpx 0;
				// box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0,0,0,0.15);
				border-radius: 0rpx 0rpx 20rpx 20rpx;

				.orient {
					display: flex;
					align-items: center;
				}

				.uni-list {
					display: flex;
					align-items: center;
					line-height: 30rpx;
				}
			}

		}


		.conter {
			width: 100vw;
			background-color: #ffffff;
			margin-top: 10rpx;
			position: fixed;
			top: 192rpx;
			z-index: 10000;
			border-bottom-left-radius: 40rpx;
			border-bottom-right-radius: 40rpx;
			min-height: 190rpx;
			max-height: 490rpx;
			overflow-y: scroll;

			.result_list {
				position: relative;
				background-color: #FFF;
				display: flex;
				flex-direction: column;
				width: 100%;
				padding: 34rpx 30rpx;
				border-bottom: 2rpx solid #E7F0FF;
				z-index: 10000;

				.information {
					position: absolute;
					top: 64rpx;
					left: 66rpx;

					.title {
						font-family: PingFangSC, PingFang SC;
						font-size: 38rpx;
						color: #141414;
						line-height: 42rpx;
						font-style: normal;
						margin-bottom: 20rpx;
					}

					.content {
						display: flex;
						align-items: center;
						margin-bottom: 20rpx;

						.work {
							margin-left: 20rpx;
							height: 36rpx;
							background: #FFF9F0;
							border-radius: 8rpx;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 20rpx;
							color: #F3AB6E;
							line-height: 36rpx;
							font-style: normal;
						}

						.governmentWork {
							margin-left: 20rpx;
							height: 36rpx;
							background: #FFF9F0;
							border-radius: 8rpx;
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 20rpx;
							color: #F3AB6E;
							line-height: 36rpx;
							font-style: normal;
						}

						.distance {
							margin-left: 10px;
						}
					}

					.localization {
						display: flex;
						align-items: center;

						.list_locate {
							width: 20rpx;
							height: 24rpx;
							margin-right: 10rpx;
						}

						.distance {
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 26rpx;
							color: #057FFE;
							line-height: 33rpx;
							font-style: normal;
							margin-right: 20rpx;
						}

						.text {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 26rpx;
							color: #333;
							line-height: 33rpx;
							font-style: normal;
						}
					}
				}

				.tips {
					position: absolute;
					top: 69rpx;
					right: 42rpx;
					text-align: center;

					.Gohere {
						width: 60rpx;
						height: 60rpx;
					}

					.date {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #057FFE;
						line-height: 33rpx;
						font-style: normal;
					}
				}

				.rect {
					width: 424rpx;
					height: 163rpx;
				}
			}

			.nomore {
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				line-height: 33rpx;
				text-align: center;
				font-style: normal;
				margin: 30rpx 0;
			}
		}
	}

	.associate {
		height: 40px;
		border-bottom: 1px solid #ccc;
		padding: 0 8px;
		overflow: hidden;
		display: flex;
		justify-content: space-between;
	}

	.filter_icon {
		text-align: center;
		line-height: 60rpx;
		top: 42vh;
		right: 32rpx;
		position: fixed;
		z-index: 99999;

		.background {
			width: 80rpx;
			height: 80rpx;
		}
	}

	.Status {
		text-align: center;
		line-height: 60rpx;
		bottom: 120rpx;
		right: 20rpx;
		position: fixed;
		z-index: 99999;

		.background {
			width: 108rpx;
			height: 108rpx;
		}

		.text {
			position: absolute;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			font-size: 20rpx;
			color: #FFFFFF;
			line-height: 28rpx;
			text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
			text-align: left;
			font-style: normal;
			bottom: 38rpx;
			left: 34rpx;
		}
	}

	.current {
		text-align: center;
		line-height: 60px;
		top: 35vh;
		right: 32rpx;
		position: fixed;
		width: 80rpx;
		/* 设置宽度 */
		height: 80rpx;
		/* 设置高度与宽度相等以形成圆形 */
		z-index: 99999;
	}

	.reportboxs {
		margin-top: 4rpx;
		// padding: 0rpx 30rpx;
		background-color: #fff;
		position: relative;
		border-top-left-radius: 100rpx;
		border-top-left-radius: 100rpx;

		.reportbox {
			padding: 0rpx 30rpx;
			padding-top: 40rpx;

			.map_list {
				position: relative;
				background-color: #FFF;
				display: flex;
				flex-direction: column;
				width: 100%;
				// padding: 34rpx 0;
				border-bottom: 2rpx solid #E7F0FF;
				margin-bottom: 31rpx;
				padding-bottom: 34rpx;

				.information {
					position: absolute;
					top: 64rpx;
					left: 66rpx;

					.title {
						font-family: PingFangSC, PingFang SC;
						font-size: 38rpx;
						color: #141414;
						line-height: 42rpx;
						font-style: normal;
						margin-bottom: 16rpx;


					}

					.content {
						display: flex;
						align-items: center;
						margin-bottom: 20rpx;

						.work {
							min-width: 100rpx;
							margin-left: 20rpx;
							height: 36rpx;
							background: #FFF9F0;
							border-radius: 8rpx;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 20rpx;
							color: #F3AB6E;
							line-height: 36rpx;
							font-style: normal;
						}

						.governmentWork {
							max-width: 340rpx;
							margin-left: 20rpx;
							height: 36rpx;
							background: #FFF9F0;
							border-radius: 8rpx;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 20rpx;
							color: #F3AB6E;
							line-height: 36rpx;
							font-style: normal;
						}

						.distance {
							margin-left: 10px;
						}
					}

					.localization {
						display: flex;
						align-items: center;

						.list_locate {
							width: 20rpx;
							height: 24rpx;
							margin-right: 10rpx;
						}

						.distance {
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 26rpx;
							color: #057FFE;
							line-height: 33rpx;
							font-style: normal;
							margin-right: 20rpx;
						}

						.text {
							width: 400rpx;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 26rpx;
							color: #333;
							line-height: 33rpx;
							font-style: normal;
						}
					}
				}

				.tips {
					position: absolute;
					top: 49rpx;
					right: 42rpx;
					text-align: center;

					.Gohere {
						width: 60rpx;
						height: 60rpx;
					}

					.date {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #057FFE;
						line-height: 33rpx;
						font-style: normal;
					}
				}

				.rect {
					width: 424rpx;
					height: 163rpx;
				}



			}
		}

		.listbg {
			width: 750rpx;
			height: 118rpx;
			position: absolute;
			top: -20rpx;
			left: 0;
			z-index: 500;
		}

	}

	/* 假设.custom-popup-mask是蒙层的class */
	.custom-popup-mask {
		background-color: #fff;
		/* 这里设置你想要的颜色和透明度 */
	}

	.triangle {

		width: 14rpx;
		height: 8rpx;
		margin-left: 9rpx;
	}

	.filter {

		width: 16rpx;
		height: 20rpx;
		margin-left: 8rpx
	}

	.locate {
		width: 21rpx;
		height: 25rpx;
		margin-right: 8rpx;
	}

	.f24 {
		font-weight: 400;
		font-size: 24rpx;
		color: #666666;
		line-height: 33rpx;
		font-style: normal;
		max-width: 240rpx;
		/* 定义容器宽度 */
		white-space: nowrap;
		/* 保持文本在一行内显示 */
		overflow: hidden;
		/* 超出容器部分隐藏 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
	}

	.uni-popup-mask {
		display: none !important;
	}

	::v-deep.picker-class .uni-picker-panel__content-text {
		font-size: 20px;
	}

	/* 修改蒙层颜色为红色 */
	.custom-popup .uni-popup-mask {
		background-color: red;
	}

	.cacel_button {
		width: 50rpx;
		height: 50rpx;
		margin-right: 10rpx;
		display: flex;
		align-items: center;
		/* 图片在垂直方向上居中 */
		justify-content: center;
		/* 图片在水平方向上居中 */
	}

	.search_button {
		width: 50rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		/* 图片在垂直方向上居中 */
		justify-content: center;
		/* 图片在水平方向上居中 */
		margin-right: 32rpx;
	}

	.cacel {
		width: 32rpx;
		height: 32rpx;

	}

	.input {

		margin-left: 20rpx;
	}

	.facility {
		.places {
			display: flex;
			flex-wrap: wrap;
			width: 100%;

			.placesitem {
				flex: 0 0 auto;
				/* 让盒子按内容宽度宽度 */
				margin: 5px;
				/* 添加间距 */
				text-align: center;
				font-weight: 400;
				font-size: 24rpx;
				color: #666666;
				line-height: 51rpx;
				font-style: normal;
				width: 163rpx;
				height: 51rpx;
				background: #FFFFFF;
				border-radius: 34rpx;
				border: 1rpx solid #999999;
			}
		}
	}

	.filter_boxs {

		.filterTop {
			width: 80vw;
			position: absolute;
			height: 300rpx;
		}

		.place_box {
			width: 100vw;
			background-color: #ffffff;
			margin-top: 10rpx;
			z-index: 10000;
			border-bottom-left-radius: 40rpx;
			border-bottom-right-radius: 40rpx;
			min-height: 190rpx;
			overflow-y: scroll;
			padding: 0 25rpx;
			padding-bottom: 50rpx;

			.places {
				display: flex;
				flex-wrap: wrap;
				width: 100%;

				.placesitem {
					flex: 0 0 auto;
					/* 让盒子按内容宽度宽度 */
					margin: 5px;
					/* 添加间距 */
					text-align: center;
					font-weight: 400;
					font-size: 24rpx;
					color: #666666;
					line-height: 51rpx;
					font-style: normal;

					width: 173rpx;
					height: 51rpx;
					background: #FFFFFF;
					border-radius: 34rpx;
					border: 1rpx solid #999999;
				}
			}

		}
	}



	.main-box {
		min-height: 20vh;
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;

		.type-list {
			width: 172rpx;
			height: 294rpx;
			background: #F5F7FB;
			border-radius: 20rpx;
			text-align: center;

			.list {
				margin: 0 auto;
				margin-top: 20rpx;
				width: 125rpx;
				height: 46rpx;
				background: #F5F7FB;
				border-radius: 34rpx;
				border: 1rpx solid #999999;
				text-align: center;
				line-height: 40rpx;
				font-weight: 400;
				font-size: 25rpx;
				color: rgba(51, 51, 51, 0.9);
				line-height: 46rpx;
				font-style: normal;
				margin-bottom: 10rpx;

				.bg {
					position: absolute;
					width: 100%;
					height: 100%;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
				}

				.icon {
					width: 8rpx;
					height: 40rpx;
					position: absolute;
					left: 2%;
					top: 50%;
					transform: translateY(-50%);
				}

				.title {
					position: relative;
					z-index: 1;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
				}
			}

			.active {
				width: 125rpx;
				height: 46rpx;
				background: #057FFE;
				border-radius: 34rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: #fff;
				line-height: 46rpx;
				font-style: normal;
				border: none;
			}

		}

		.team-list {
			width: 65%;
			background: #fff;
			display: flex;
			flex-wrap: wrap;

			.placesitem {
				flex: 0 0 auto;
				/* 让盒子按内容宽度宽度 */
				margin: 5px;
				/* 添加间距 */
				text-align: center;
				width: 180rpx;
				height: 50rpx;
				border-radius: 8rpx;
				border: 1rpx solid #DADADA;
				font-weight: 400;
				font-size: 18rpx;
				color: #666666;
				line-height: 50rpx;
				font-style: normal;
			}

			.list {
				width: 460rpx;
				height: 180rpx;
				margin: 0 auto;
				position: relative;
				display: flex;
				align-items: center;
				margin-bottom: 30rpx;
				padding: 0 20rpx;
				border-bottom: 2rpx solid #e7f0ff;

				.bg {
					position: absolute;
					width: 460rpx;
					height: 100%;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					// z-index: -1;
				}

				.avatar {
					width: 122rpx;
					height: 122rpx;

					image {
						width: 100%;
						height: 100%;
						border-radius: 16rpx;
					}
				}

				.info {
					z-index: 1;
					flex: 1;
					padding-left: 20rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.name {
						font-size: 32rpx;
						font-weight: bold;
					}

					.type {
						width: 130rpx;
						height: 36rpx;
						line-height: 36rpx;
						text-align: center;
						background: #fff9f0;
						border-radius: 8rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 20rpx;
						color: #f3ab6e;
						margin: 10rpx 0;
					}

					.desc {
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 22rpx;
						color: #666666;
					}
				}
			}
		}
	}

	.buttons {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 40rpx;
		margin-bottom: 30rpx;
		z-index: 9999999;

		.reset {
			width: 253rpx;
			height: 80rpx;
			border-radius: 8rpx;
			border: 2rpx solid #057FFE;
			font-weight: 500;
			font-size: 34rpx;
			color: #057FFE;
			line-height: 80rpx;
			letter-spacing: 2px;
			text-align: center;
			font-style: normal;
			z-index: 9999999;
		}

		.define {
			width: 253rpx;
			height: 80rpx;
			background: #057FFE;
			border-radius: 8rpx;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 80rpx;
			letter-spacing: 2px;
			text-align: center;
			font-style: normal;
		}
	}

	.scroll-Y {
		width: 200rpx;
		height: 300rpx;
	}

	.f12 {
		margin-bottom: 10rpx;
		font-weight: 500;
		font-size: 28rpx;
		color: rgba(0, 0, 0, 0.9);
		line-height: 40rpx;
		text-align: left;
		font-style: normal;
	}

	.region {
		position: absolute;
		top: 70rpx;
		width: 90%;
	}

	.rangeboxs {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.rangeitems {
		display: flex;
		align-items: center;
	}

	.government {
		margin-bottom: 40rpx;
		padding-top: 230rpx;
	}

	.territory {
		padding-top: 20rpx;
		margin-top: 20rpx;
	}

	.palceChange {
		background-color: #057FFE;
		color: #fff !important;
		border-radius: 34rpx;

	}

	.lately {
		color: #057FFE
	}

	.changeHeight {
		position: absolute;
		top: -12rpx;
		left: 330rpx;
		z-index: 9999;
		height: 20rpx;
		width: 108rpx;
		border-radius: 19rpx;
		background: #DBDBDB;

	}

	.bubbling {
		width: 60vw;
		height: 150rpx;
		// padding: 20rpx;
		// background-color: #fff;
		// color: #000;
		// position: absolute;
		// left: 40vw;
		// font-size: 19rpx;
		position: absolute;
		background: #fff;
		// border: 1px solid #999;
		box-shadow: 0px 0px 5px 0 #101D47;
		padding: 10px;
		margin-top: 10px;
		left: 14vw;
		border-radius: 30rpx;
		z-index: 99999;
		top: 4vh;
	}


	.bubbling:after {
		content: '';
		position: absolute;
		top: 100%;
		left: 30vw;
		border-width: 5px;
		border-style: solid;
		border-color: #fff transparent transparent transparent;
	}

	.bubbling:before {
		content: '';
		position: absolute;
		top: 100%;
		left: 30vw;
		border-width: 6px;
		border-style: solid;
		border-color: #999 transparent transparent transparent;
	}

	.bubblingText {
		font-weight: 400;
		font-size: 24rpx;
		color: #141414;
		line-height: 33rpx;
		text-align: left;
		font-style: normal;
	}

	.bubblingDialog {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.nav_icon {
		width: 26rpx;
		height: 26rpx;
	}

	.nav_boxs {
		display: flex;
		align-items: center;

		.text {
			font-weight: 400;
			font-size: 24rpx;
			color: #057FFE;
			line-height: 33rpx;
			text-align: left;
			font-style: normal;
			margin-right: 10rpx;
		}
	}
</style>