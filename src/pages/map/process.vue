<template>
  <view>
    <!-- <navbar :Props="Props" :title="headtitle" @black="black"></navbar> -->
    <view :style="{ fontSize: fontSizeMedium }" class="main-box">
      <image class="bg" src="/static/images/assistant/xq_bg.png"></image>
      <view class="tips">
        <image src="/static/images/assistant/msg.png"></image>
        <text>已预约</text>
        <text>请等待预约结果</text>
      </view>
      <view class="btns">
        <view class="btn" @click="$tab.navigateTo(`/pages/mine/orderRecord`)">
          查看预约
        </view>
        <view class="btn2" @click="goHome">返回</view>
      </view>
    </view>
  </view>
</template>

<script>
import global from "@/utils/global.js";
import { getRecordDetail } from "@/api/assistant/index.js";
import navbar from "@/components/Navbar/index.vue";
export default {
  components: {
    navbar,
  },
  data() {
    return {
      record: {},
      detail: {},
      isCurrentItem: true,
      isNextItem: true,
      headtitle: "办理详情",
      Props: {
        imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: "", //导航高度(动态获取传参)
        bgColor: "", //导航栏背景色,不传参则默认#9CF
        capsuleTop: "", //胶囊顶部距离(动态获取传参)
        textColor: "", //导航标题字体颜色(不传默认#FFF)
        iconColor: "", //icon图标颜色(不传默认#FFF)
        blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
        backText: "", //默认字体(返回)
      },
    };
  },
  async onLoad(options) {
    this.Props.statusBarHeight = getApp().globalData.statusBarHeight;
    this.Props.capsuleTop = getApp().globalData.capsuleTop;
    this.record = JSON.parse(options.record);
    this.isCurrentItem = this.record.isCurrentItem;
    // 当前办理事项的 index
    let currentItemIndex = this.record.currentItemIndex;
    if (this.isCurrentItem) {
      const res = await getRecordDetail({
        recordId: this.record.itemList[currentItemIndex].recordId,
        itemId: this.record.itemList[currentItemIndex].itemId,
      });
      // 下个办理事项的详情
      this.detail = res.result;
      // 只有下个办理事项 状态是dtj 才可以下一步
      if (this.detail.currentItem.itemStatus === "dtj") {
        this.isNextItem = true;
      } else {
        this.isNextItem = false;
      }
    }
  },
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
    },
  },
  methods: {
    async goHome() {
      this.$tab.navigateTo(`/pages/map/index`);
    },
  },
};
</script>

<style>
page {
  background: #f4f8fc;
}
</style>
<style lang="scss" scoped>
.main-box {
  width: 686rpx;
  margin: 60rpx auto;
  padding: 60rpx 20rpx 150rpx 20rpx;
  position: relative;
  background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
  box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
  border-radius: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  // justify-content: space-between;
  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 547rpx;
  }

  .tips {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 60rpx;
    margin-top: 50rpx;

    text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 44rpx;
      color: #333333;
    }

    image {
      width: 199rpx;
      height: 237rpx;
      margin-bottom: 60rpx;
    }
  }

  .btns {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 200rpx;

    .btn {
      width: 590rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: #057ffe;
      border-radius: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #ffffff;
      margin-bottom: 50rpx;
    }

    .btn2 {
      width: 590rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: #ffffff;
      border-radius: 40rpx;
      border: 2rpx solid #057ffe;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #057ffe;
    }
  }
}
</style>