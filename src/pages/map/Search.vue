<template>
	<view class="boxs">
		<view class="tipsbox">
			<view class="backboxs">
				<image class="back" @click="back()" src="@/static/images/map/back.png" mode=""></image>
			</view>
			<view class="tip">
				<input class="uni-input" v-model="changeValue" focus @input="clearInput" placeholder="请输入关键字" />
				<!-- <text class="uni-icon" v-if="showClearIcon" @click="clearIcon">&#xe434;</text> -->
				<view class="cacel_button" v-if="showClearIcon" @click="clearIcon">
					<image class="cacel" src="@/static/images/map/cancel.png" mode=""></image>
				</view>
				<view class="search_button" @click="nearby_search">
					<image style="z-index: 9999;" class="search" src="@/static/images/map/search.png" mode="">
					</image>
				</view>
			</view>
		</view>

		<view class="">
			<view class="map_list" v-for="(report, index) in reportList" :key="index">
				<!-- <image class="rect" src="@/static/images/map/rect.png" mode=""></image> -->
				<view class="rect">

				</view>
				<view class="information" @click="goDetails(report.id)">
					<view class="title">
						{{report.name}}
					</view>
					<view class="localization">
						<image class="list_locate" src="@/static/images/map/list_locate.png" mode="">
						</image>
						<view class="distance">{{report.distance}}km
						</view>
						<view class="text">{{report.address}}</view>
					</view>
				</view>
				<!-- 				<view class="tips" @click="getDetails(report)">
					<image class="Gohere" src="@/static/images/map/here.png" mode=""></image>
					<view :style="{ fontSize: fontSizeSmall }" class="date">导航</view>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getMapList
	} from '@/api/map/map.js'
	export default {
		data() {
			return {
				changeValue: '',
				longitude: '',
				latitude: '',
				showClearIcon: false,
				reportList: []
			}
		},
		onLoad(data) {
			console.log(data, '2222')
			this.longitude = data.longitude
			this.latitude = data.latitude
		},
		methods: {
			clearIcon: function() {
				this.changeValue = '';
				this.showClearIcon = false;
			},
			clearInput: function(event) {
				this.changeValue = event.detail.value;
				this.nearby_search()
				if (event.detail.value.length > 0) {
					this.showClearIcon = true;
				} else {
					this.showClearIcon = false;
				}
			},
			async nearby_search() {
				const that = this
				if (!that.changeValue) {
					that.suggestion = []
					uni.showToast({
						title: '请输入值！',
						icon: 'error',
						duration: 2000
					});
					return
				} else {
					// 调用接口
					// 输入框防抖
					clearTimeout(that.time)
					that.time = setTimeout(() => {
						that.getsuggestion()
					}, 500)
				}
			},
			// 事件触发，调用接口
			async getsuggestion() {
				const that = this
				const res = await getMapList({
					longitude: that.longitude,
					latitude: that.latitude,
					// radius: that.radiusData,
					// governmentWork: that.governmentWork,
					name: that.changeValue,
				})

				that.reportList = res.result
				that.show = true
			},
			async goDetails(id) {
				uni.navigateTo({
					url: '/pages/map/newDetails?id=' + id + '&latitude=' + this.latitude + '&longitude=' + this
						.longitude
				});
			},
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
		}
	}
</script>

<style lang="scss">
	.boxs {
		background-color: #fff;
		padding-top: 20rpx;
		min-height: 100vh;
	}

	.tip {
		width: 710rpx;
		height: 60rpx;
		margin: 0 20rpx;
		display: flex;
		align-items: center;
		background-color: #fff;
		border-radius: 3px;
		z-index: 200;
		border-radius: 30rpx;
		border: 2rpx solid #DDDDDD;

		.uni-input {
			flex: 1;
			height: 20rpx;
			padding-left: 40rpx;
		}

		span {
			width: 40px;
			color: #8b8c8f;
		}

		.search {
			width: 32rpx;
			height: 32rpx;

			z-index: 99999;
		}

		.cacel_button {
			width: 50rpx;
			height: 50rpx;
			margin-right: 10rpx;
			display: flex;
			align-items: center;
			/* 图片在垂直方向上居中 */
			justify-content: center;

			/* 图片在水平方向上居中 */
			.cacel {
				width: 32rpx;
				height: 32rpx;

			}
		}

		.search_button {
			width: 50rpx;
			height: 50rpx;
			display: flex;
			align-items: center;
			/* 图片在垂直方向上居中 */
			justify-content: center;
			/* 图片在水平方向上居中 */
			margin-right: 32rpx;

			.search {
				width: 32rpx;
				height: 32rpx;

				z-index: 99999;
			}
		}

	}

	.map_list {
		position: relative;
		background-color: #FFF;
		display: flex;
		flex-direction: column;
		width: 100%;
		padding: 34rpx 0;
		border-bottom: 2rpx solid #E7F0FF;

		.information {
			position: absolute;
			top: 64rpx;
			left: 66rpx;

			.title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 30rpx;
				color: #141414;
				line-height: 42rpx;
				font-style: normal;
				margin-bottom: 12rpx;
			}

			.content {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				.work {
					min-width: 100rpx;
					margin-left: 20rpx;
					height: 36rpx;
					background: #FFF9F0;
					border-radius: 8rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 20rpx;
					color: #F3AB6E;
					line-height: 36rpx;
					font-style: normal;
				}

				.governmentWork {
					max-width: 340rpx;
					margin-left: 20rpx;
					height: 36rpx;
					background: #FFF9F0;
					border-radius: 8rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 20rpx;
					color: #F3AB6E;
					line-height: 36rpx;
					font-style: normal;
				}

				.distance {
					margin-left: 10px;
				}
			}

			.localization {
				display: flex;
				align-items: center;

				.list_locate {
					width: 20rpx;
					height: 24rpx;
					margin-right: 10rpx;
				}

				.distance {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #057FFE;
					line-height: 33rpx;
					font-style: normal;
					margin-right: 20rpx;
				}

				.text {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #666666;
					line-height: 33rpx;
					font-style: normal;
				}
			}
		}

		.tips {
			position: absolute;
			top: 49rpx;
			right: 42rpx;
			text-align: center;

			.Gohere {
				width: 60rpx;
				height: 60rpx;
			}

			.date {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #057FFE;
				line-height: 33rpx;
				font-style: normal;
			}
		}

		.rect {
			width: 424rpx;
			height: 163rpx;
		}
	}

	.backboxs {

		background-color: #fff;
		padding: 10rpx 10rpx;
	}

	.back {
		width: 40rpx;
		height: 40rpx;
	}
	.tipsbox {
		display: flex;
		align-items: center;
	}
</style>