<template>
  <view class="box">
    <!-- <navbar :Props="Props" :title='headtitle' @black="black"></navbar> -->
    <view class="backboxs">
      <image
        class="back"
        @click="back()"
        src="@/static/images/map/back.png"
        mode=""
      ></image>
    </view>
    <view class="boxs">
      <view class="content">
        <!-- <image class="g_background" src="@/static/images/interaction/g_background.png" mode=""></image> -->
        <view class="report">
          <view class="title">
            <view class="round"></view>
            <view class="text">业务信息</view>
          </view>
          <view class="cell phone">
            <view class="f30">业务流水号</view>
            <view class="">
              <u--input
                v-model="FormData.businessId"
                style="width: 400rpx"
                class="input"
                placeholder="请输入业务流水号"
                clearable
                disabled
              ></u--input>
            </view>
          </view>
          <view class="line"></view>
          <view class="cell">
            <view class="f30">姓名</view>
            <view class="">
              <u--input
                v-model="FormData.appointmentName"
                style="width: 400rpx"
                class="input"
                placeholder="请输入姓名"
                clearable
              ></u--input>
            </view>
          </view>
          <view class="line"></view>
          <view class="cell">
            <view class="f30">联系方式</view>
            <view class="input_list">
              <u--input
                v-show="!openShow"
                v-model="appointmentTel"
                style="width: 300rpx; border: none"
                class="input"
                placeholder="请输入联系方式"
                clearable
              ></u--input>
              <u--input
                v-show="openShow"
                disabled
                v-model="disAppointmentTel"
                style="width: 300rpx; border: none"
                class="input"
                placeholder="请输入联系方式"
                clearable
              ></u--input>
              <u-icon
                v-show="!openShow"
                @click="open()"
                class="eye"
                name="eye-fill"
              ></u-icon>
              <u-icon
                v-show="openShow"
                @click="open()"
                class="eye"
                name="eye-off"
              ></u-icon>
            </view>
          </view>
          <view class="line"></view>
          <view class="cell">
            <view class="f30">办理事项</view>
            <view class="">
              <u--input
                v-model="FormData.itemName"
                style="width: 400rpx;background-color: #fff;"
                class="input"
                placeholder="请输入办理事项"
                clearable
              ></u--input>
            </view>
          </view>
          <view class="line"></view>
          <view class="cell">
            <view class="f30">预约网点</view>
            <qiaoSelect
              @data-from-child="receiveDataFromChild"
              :keyId="2"
              :dataList="mapList"
              phText="请选择/输入预约网点"
              showField="name"
              searchKey="name"
              :showObj="objShow1"
              :showBorder="false"
              @change="selectChange1"
              @input="inputChange1"
            >
            </qiaoSelect>
          </view>
          <!-- <view class="line"></view> -->
          <!-- 					<view class="cell">
						<view class="f30">事项级别</view>
						<picker class="picker-class" @change="rangePickerChange" :value="index" :range-key="'title'"
							:range="levelList">
							<view v-show="defaultRange" class="f24">请选择事项级别
							</view>
							<view v-show="!defaultRange" class="f24">{{LevelName}}
							</view>
						</picker>
					</view> -->

          <!-- <div class="cell" @click="showItemType">
						<div class="label label2">事项级别:</div>
						<div class="value2" style="width: 356rpx">
							<span>{{ LevelName ? LevelName : '请选择' }}</span>
							<img src="/static/images/assistant/down.png" />
						</div>
					</div>

					<view class="line"></view>
					<div class="cell" @click="showItemList">
						<div class="label label2">事项类型:</div>
						<div class="value2" style="width: 356rpx">
							<span>{{ typeName ? typeName : '请选择' }}</span>
							<img src="/static/images/assistant/down.png" />
						</div>
					</div> -->
          <!-- <view class="line"></view> -->
          <!-- <view class="cell">
						<view class="f30">办理事项</view>
						<qiaoSelects ref="child" @data-from-child="receiveDataFromChild" :keyId="1" :dataList="orgArray"
							phText="请选择/输入办理事项" phColor="#999999" showField="itemName" searchKey="itemName"
							:showObj="showObj" :showBorder="false" :typeName="typeName"
							@call-father="fatherMethodHandle" :LevelName="LevelName" @change="selectChange"
							@input='inputChange'>
						</qiaoSelects>
					</view> -->
        </view>
      </view>
      <view class="order">
        <scroll-view class="text" scroll-x="true" style="width: 100%">
          <view class="date_boxs">
            <view
              class="items"
              v-for="(report, index) in dateList"
              :key="index"
              @click="changeDate(index, report)"
            >
              <view
                class="weeklist"
                :class="dateChange == index ? 'line-bottom' : ''"
              >
                <view :class="dateChange == index ? 'blue' : ''" class="week"
                  >{{ report.weekday }}
                </view>
                <view :class="dateChange == index ? 'blue' : ''" class="text">{{
                  report.date
                }}</view>
              </view>
              <image
                v-show="dateChange == index"
                class="line"
                src="@/static/images/map/line.png"
                mode=""
              >
              </image>
            </view>
          </view>
        </scroll-view>
        <view class="government">
          <view class="places">
            <view
              :class="timeChange == index ? 'timeChange' : ''"
              class="placesitem"
              v-for="(item, index) in timeList"
              :key="index"
              @click="changeTime(index, item)"
            >
              <view>{{ item.timePeriod }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="btn-box">
      <view class="feedback" @click="validateInput()"> 确认预约</view>
    </view>
    <uni-popup ref="message" type="message">
      <uni-popup-message
        :type="msgType"
        :message="messageText"
        :duration="2000"
      ></uni-popup-message>
    </uni-popup>
  </view>
</template>

<script>
import { getDictItem } from "@/api/assistant/index.js";
import qiaoSelect from "@/uni_modules/qiao-select/components/qiao-select/qiaoSelect.vue";
import qiaoSelects from "@/uni_modules/qiao-select/components/qiao-select/qiaoSelects.vue";

import { appointmentAdd } from "@/api/map/map.js";
import navbar from "@/components/Navbar/index.vue";
import {
  getAppointmentTimeList,
  getCurrentUserInfo,
  getItemList,
  getMapList,
  getYyBusinessNum,
} from "@/api/map/map.js";
import { getItemType } from "@/api/assistant/index.js";
import { result } from "lodash";
export default {
  components: {
    navbar,
    qiaoSelect,
    qiaoSelects,
  },
  onLoad: function (report) {
    this.Props.statusBarHeight = getApp().globalData.statusBarHeight;
    this.Props.capsuleTop = getApp().globalData.capsuleTop;
    this.reportItem = report;
    console.log(report, "111111111111111111111111");
    if (this.reportItem.updateTime == "null") {
      this.reportItem.updateTime = "";
    }
    if (this.reportItem.feedbackContent == "null") {
      this.reportItem.feedbackContent = "";
    }
    if (this.reportItem.isFeedback_dictText == "已反馈") {
      this.disabled = true;
    }
    this.FormData.appointmentPoint = report.name;
    this.objShow1.id = 1;
    this.objShow1.name = report.name;
    this.getList();
    this.getCurrentUserInfo();
    this.getlevelList();
    this.getItemType();
    this.getlocation();
	this.getBusiness()
  },
  data() {
    return {
      disabled: false,
      reportItem: {},
      feedback: "",
      headtitle: "预约",
      msgType: "",
      messageText: "",
      Props: {
        imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: "", //导航高度(动态获取传参)
        bgColor: "", //导航栏背景色,不传参则默认#9CF
        capsuleTop: "", //胶囊顶部距离(动态获取传参)
        textColor: "", //导航标题字体颜色(不传默认#FFF)
        iconColor: "", //icon图标颜色(不传默认#FFF)
        blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
        backText: "", //默认字体(返回)
      },
      orgArray: [],
      showObj: null,
      dateList: [],
      timeList: [],
      dateChange: 0,
      timeChange: 0,
      FormData: {
        businessId: "",
        appointmentName: "",
        itemName: "",
        // itemLevel: "",
        appointmentTel: "",
        appointmentTime: "",
        appointmentTimePeriod: "",
      },
      levelList: [],
      defaultRange: true,
      typeShow: true,
      //   itemLevel: "",
      LevelName: "",
      typeName: "",
      objShow: true,
      longitude: "",
      latitude: "",
      mapList: [],
      objShow1: {
        id: 1,
        name: "",
      },
      typeList: [],
      openShow: true,
      openDisabled: true,
      disAppointmentTel: "",
      itemNameText: "",
    };
  },
  methods: {
    // 事项类型选择
    showItemType() {
      const that = this;
      uni.showActionSheet({
        itemList: that.levelList.map((i) => i.title),
        success: (res) => {
          that.itemLevel = that.levelList[res.tapIndex].value;
          that.LevelName = that.levelList[res.tapIndex].title;
          that.FormData.itemLevel = that.itemLevel;
          that.defaultRange = false;
          that.$refs.child.clickClear();
          that.FormData.itemName = "";
          that.getObjList();
        },
      });
    },
    // 事项选择
    showItemList() {
      const that = this;
      if (!this.LevelName) {
        uni.showToast({
          icon: "none",
          title: "请先选择事项级别",
        });
        return;
      }
      // uni.showActionSheet({
      //   itemList: this.itemTypeList.map((i) => i),
      //   success: (res) => {
      //     this.itemNameText = ''
      //     this.query.itemName = ''
      //     this.itemTypeName = this.itemTypeList[res.tapIndex]
      //     this.query.itemType = this.itemTypeList[res.tapIndex]
      //     this.getItemListNewList()
      //   }
      // })

      uni.showActionSheet({
        itemList: this.typeList.map((i) => i),
        success: (res) => {
          // this.query.itemName = this.typeList[res.tapIndex].itemName
          // this.itemNameText = this.typeList[res.tapIndex].itemName
          // this.query.itemId = this.typeList[res.tapIndex].itemId
          that.itemType = that.typeList[res.tapIndex];
          that.typeName = that.typeList[res.tapIndex];
          that.FormData.itemType = that.itemType;
          that.typeShow = false;
          that.objShow = false;
          that.$refs.child.clickClear();
          that.FormData.itemName = "";
          that.getObjList();
        },
      });
    },
    open() {
      if (this.openShow) {
        this.openShow = false;
        this.openDisabled = false;
        this.disAppointmentTel =
          this.appointmentTel.slice(0, 3) +
          "****" +
          this.appointmentTel.slice(7);
      } else {
        this.openShow = true;
        this.openDisabled = true;
        this.disAppointmentTel =
          this.appointmentTel.slice(0, 3) +
          "****" +
          this.appointmentTel.slice(7);
      }
    },
    // 事项选择
    fatherMethodHandle() {
      console.log(111);
      if (!this.LevelName) {
        uni.showToast({
          icon: "none",
          title: "请先选择事项级别",
        });
      } else if (!this.typeName) {
        uni.showToast({
          icon: "none",
          title: "请先选择事项类型",
        });
      }
    },
    async getlocation() {
      const that = this;
      const url = "https://apis.map.qq.com/ws/location/v1/ip"; // 关键字查询
      that
        .$jsonp(url, {
          key: "G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV",
          output: "jsonp",
        })
        .then((res) => {
          that.longitude = res.result.location.lng;
          that.latitude = res.result.location.lat;
          that.getMapList();
        })
        .catch((err) => {
          console.log(err);
        });
    },
    async getMapList() {
      const date = await getMapList({
        longitude: this.longitude,
        latitude: this.latitude,
      });
      this.mapList = date.result;
    },
    async getBusiness() {
      const date = await getYyBusinessNum({});
	  console.log(date, '99999');
	  this.FormData.businessId = date.message
    },
    rangePickerChange(e) {
      const that = this;
      that.itemLevel = that.levelList[e.detail.value].value;
      that.LevelName = that.levelList[e.detail.value].title;
      that.FormData.itemLevel = that.itemLevel;
      that.defaultRange = false;
      that.$refs.child.clickClear();
      that.FormData.itemName = "";
      that.getObjList();
    },
    changeType(e) {
      const that = this;
      that.itemType = that.typeList[e.detail.value].value;
      that.typeName = that.typeList[e.detail.value].title;
      that.FormData.itemType = that.itemType;
      that.typeShow = false;
      that.objShow = false;
      that.$refs.child.clickClear();
      that.FormData.itemName = "";
      that.getObjList();
    },
    async getObjList() {
      const query = {
        divisionLevel: this.itemLevel,
        itemType: this.itemType,
      };
      const res = await getItemList(query);
      this.orgArray = res?.result;
      console.log(res, "22222");
    },
    async getAppointmentTimeList() {
      const query = {
        appointmentTime: this.FormData.appointmentTime,
      };
      const res = await getAppointmentTimeList(query);
      this.timeList = res.result;
      console.log(this.timeList, "000000");
      this.FormData.appointmentTimePeriod = this.timeList[0].timePeriod;
      this.orderNum = this.timeList[0].appointmentNumber;
    },
    async getCurrentUserInfo() {
      const res = await getCurrentUserInfo();
      console.log(res, "res-------");
      this.FormData.appointmentName = res?.result?.nickName;
      this.appointmentTel = res?.result?.mobilePhone;
      this.disAppointmentTel =
        this.appointmentTel.slice(0, 3) + "****" + this.appointmentTel.slice(7);
    },
    selectChange(e) {
      //返回选择的对象，如果输入框清空，返回null
      console.log(1111);
      if (e) {
        this.mechId = e.id;
        console.log(11113333333333333);
        this.FormData.itemName = e.itemName;
      } else {
        this.mechId = "";
      }
    },
    selectChange1(e) {
      //返回选择的对象，如果输入框清空，返回null
      console.log(e, "222222222222222");
      if (e) {
        this.mechId = e.id;
        this.FormData.appointmentPoint = e.name;
      } else {
        this.mechId = "";
      }
    },
    async getlevelList() {
      const res = await getDictItem({
        dictCode: "jy_administrative_level",
      });
      console.log(res, "--------------1----------------");
      this.levelList = res.result;
    },
    async getItemType() {
      const res = await getItemType();
      console.log(res, "----------------2--------------");
      this.typeList = res.result;
    },
    inputChange(e) {
      //返回搜索结果集合,一般用不到
      if (typeof e === "string") {
        this.FormData.itemName = e;
      }
    },

    inputChange1(e) {
      //返回搜索结果集合,一般用不到
      console.log(e, "11111111111111111111111");
      if (typeof e === "string") {
        this.FormData.appointmentPoint = e;
      } else {
        this.FormData.appointmentPoint = e[0].name;
      }
      console.log(this.FormData.appointmentPoint);
    },
    getList() {
      // 获取当前日期
      const today = new Date();

      // 创建一个数组来存储日期和对应的星期
      const dateList = [];

      // 循环30次，每次增加一天
      for (let i = 0; i < 30; i++) {
        // 复制当前日期以避免修改原始日期对象，并增加i天
        const nextDate = new Date(
          today.getFullYear(),
          today.getMonth(),
          today.getDate() + i
        );

        // 获取年份、月份和日期
        const year = nextDate.getFullYear();
        const month = nextDate.getMonth() + 1; // 月份从0开始，所以需要加1
        const day = nextDate.getDate();

        // 获取星期几（0代表周日，1代表周一，以此类推）
        const weekday = nextDate.getDay();
        const weekdays = [
          "周日",
          "周一",
          "周二",
          "周三",
          "周四",
          "周五",
          "周六",
        ];
        const weekdayName = weekdays[weekday];

        // 将日期、星期几添加到数组中
        dateList.push({
          date: `${month < 10 ? "0" + month : month}.${
            day < 10 ? "0" + day : day
          }`,
          weekday: weekdayName,
          time: `${year}-${month < 10 ? "0" + month : month}-${
            day < 10 ? "0" + day : day
          }`,
        });
      }
      this.dateList = dateList;
      this.FormData.appointmentTime = this.dateList[0].time;
      this.getAppointmentTimeList();
      // 输出日期列表和对应的星期几
      console.log(dateList, "22222");
    },
    back() {
      uni.navigateBack();
    },
    receiveDataFromChild(data) {
      console.log(data, "22222");
    },
    changeDate(index, data) {
      this.dateChange = index;
      this.FormData.appointmentTime = data.time;
    },
    changeTime(index, data) {
      console.log(data, "2222");
      this.timeChange = index;
      this.FormData.appointmentTimePeriod = data.timePeriod;
      this.orderNum = data.appointmentNumber;
      console.log(this.orderNum, "0999999");
    },
    validateInput() {
      const reg = /^1[3-9]\d{9}$/;
      if (reg.test(this.appointmentTel)) {
        this.message = "手机号格式正确";
      } else {
        this.msgType = "warn";
        this.messageText = "手机号格式不正确";
        this.$refs.message.open();
        return;
      }
      this.FormData.appointmentTel = this.appointmentTel;
      if (!this.FormData.appointmentName) {
        this.msgType = "warn";
        this.messageText = "请输入姓名！";
      } else if (!this.FormData.appointmentTel) {
        this.msgType = "warn";
        this.messageText = "请输入联系方式！";
      } else if (!this.FormData.itemName) {
        this.msgType = "warn";
        this.messageText = "请输入或选择办理事项！";
      } else if (!this.FormData.appointmentPoint) {
        this.msgType = "warn";
        this.messageText = "请输入或选择预约网点！";
      } else if (this.orderNum <= 0) {
        this.msgType = "warn";
        this.messageText = "当前时段预约人数已满！";
      } else {
        this.msgType = "warn";
        this.messageText = "";
        const param = this.FormData;
        appointmentAdd(param).then(() => {
          this.msgType = "succuss";
          this.messageText = "您的预约已成功提交！";
          setTimeout(() => {
            uni.navigateTo({
              url: "/pages/map/process",
            });
          }, 1000);
        });
      }
      this.$refs.message.open();
    },
  },
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
    },
    fontSizeSuperLarge() {
      return this.$store.state.fontSizeSuperLarge; // 从Vuex获取大号字体大小
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: #f5f6f7;
}

.box {
  width: 100vw;
  height: 100vh;
  background-color: #f4f8fc;
}

.boxs {
  margin: 32rpx;
}

.content {
  padding: 30rpx 40rpx;
  height: 100%;
  width: 100%;
  display: flex;
  gap: 15rpx;
  flex-direction: column;
  align-items: center;
  z-index: 1;
  background: linear-gradient(
    171deg,
    #d1efff 0%,
    rgba(252, 245, 255, 0.32) 48%,
    #feffff 100%
  );
  box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.06);
  border-radius: 32rpx;
  opacity: 0.79;
  position: relative;

  .g_background {
    width: 686rpx;
    height: 180rpx;
    z-index: 1;
  }

  .cell {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;

    .selector {
      .selector-item {
        display: flex;
        padding: 12rpx 0 0 31rpx;
        width: 310rpx;
        height: 60rpx;
        background: #ffffff;
        border-radius: 30rpx;
        border: 2rpx solid #dddddd;
        font-weight: 500;
        font-size: 24rpx;
        color: #444444;
        line-height: 33rpx;
        font-style: normal;

        &:hover {
          color: #aaa;
          border: solid 1rpx rgba(200, 200, 200, 0.7);
        }
      }
    }
  }

  .cells {
    margin-bottom: 20rpx;

    .text {
      margin-bottom: 20rpx;
    }
  }

  .report {
    width: 100%;
    display: flex;
    flex-direction: column;

    .title {
      display: flex;
      align-items: center;

      .round {
        width: 18rpx;
        height: 18rpx;
        background: #057ffe;
      }

      .text {
        font-weight: 600;
        font-size: 36rpx;
        color: #057ffe;
        line-height: 50rpx;
        font-style: normal;
        margin-left: 14rpx;
      }
    }

    .type {
      width: 610rpx;
      z-index: 1;
    }

    .phone {
      padding-top: 50rpx;
    }

    .text {
      width: 100%;
      color: #333;
    }
  }
}

.btn-box {
  width: 750rpx;
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  margin-bottom: 40rpx;
  padding-bottom: 40rpx;

  ::v-deep button {
    width: 150rpx;
  }
}

.u-border {
  width: 610rpx;
  height: 207rpx;
  background: #f5f7fb;
  border-radius: 20rpx;
  padding: 20rpx;
}

.feedback {
  width: 590rpx;
  height: 80rpx;
  background: #057ffe;
  border-radius: 40rpx;
  background: #057ffe;
  font-weight: 500;
  font-size: 32rpx;
  color: #ffffff;
  line-height: 80rpx;
  letter-spacing: 2px;
  text-align: center;
  font-style: normal;
}

.return {
  width: 282rpx;
  height: 80rpx;
  border-radius: 8rpx;
  border: 2rpx solid #057ffe;
  font-weight: 500;
  font-size: 34rpx;
  color: #057ffe;
  line-height: 80rpx;
  letter-spacing: 2px;
  text-align: center;
  font-style: normal;
}

.order {
  margin-top: 34rpx;

  .date_boxs {
    display: flex;

    .items {
      margin-bottom: 40rpx;
      margin-right: 20rpx;

      .weeklist {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0 20rpx;

        .image {
          width: 560rpx;
          // height: 238rpx;
        }

        .week {
          font-weight: 600;
          font-size: 40rpx;
          color: #858585;
          line-height: 56rpx;
          text-align: center;
          font-style: normal;
          margin-bottom: 10rpx;
        }

        .text {
          font-weight: 400;
          font-size: 34rpx;
          color: #858585;
          line-height: 48rpx;
          text-align: center;
          font-style: normal;
        }
      }

      .line {
        width: 67rpx;
        height: 9rpx;
        margin-left: 20rpx;
      }
    }
  }
}

.places {
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  .placesitem {
    flex: 0 0 auto;
    /* 让盒子按内容宽度宽度 */
    margin: 5px;
    /* 添加间距 */
    text-align: center;
    font-weight: 400;
    font-size: 30rpx;
    color: #666666;
    line-height: 50rpx;
    font-style: normal;
    line-height: 101rpx;
    width: 209rpx;
    height: 101rpx;
    background: #ffffff;
    border-radius: 8rpx;
  }

  .timeChange {
    width: 209rpx;
    height: 101rpx;
    background: #057ffe;
    border-radius: 8rpx;
    font-weight: 600;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 101rpx;
    text-align: center;
    font-style: normal;
  }
}

.f30 {
  font-weight: 500;
  font-size: 30rpx;
  color: #333333;
  line-height: 42rpx;
  text-align: center;
  font-style: normal;
  position: relative;

  &::after {
    content: "*";
    position: absolute;
    left: -20rpx;
    top: 5rpx;
    color: #f24f4b;
  }
}

.line {
  width: 634rpx;
  height: 1rpx;
  border: 2rpx solid #e7f0ff;
  margin-bottom: 16rpx;
}

.line-bottom {
  // border-bottom: #057FFE solid 4rpx;
}

.blue {
  color: #057ffe !important;
}

.input {
  // width: 200rpx;
}

.u-border {
  height: 70rpx;
  border-radius: 0;
}

.backboxs {
  background-color: #f4f8fc;
  padding: 10rpx 30rpx;
}

.back {
  width: 40rpx;
  height: 40rpx;
}

.picker-class {
  width: 388rpx;
  height: 60rpx;
  background: #ffffff;
  border-radius: 6rpx;
  border: 2rpx solid #dddddd;
  line-height: 60rpx;
  padding-left: 12rpx;
}

.ceshi {
  width: 100vw;
  height: 500rpx;
  position: absolute;
  top: 900rpx;
  background-color: #000;
  z-index: 99;
}

.input_list {
  width: 400rpx;
  display: flex;
  border: 1rpx solid #dadbed;
}

.eye {
  margin-right: 30rpx;
}

.value2 {
  min-height: 60rpx;
  // line-height: 60rpx;
  // flex: 1;
  background: #ffffff;
  border-radius: 6rpx;
  border: 2rpx solid #dddddd;
  padding: 0 20rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 30rpx;
  color: #444444;
  font-style: normal;
  display: flex;
  align-items: center;
  justify-content: space-between;

  img {
    width: 28rpx;
    height: 14rpx;
  }
}

.label2 {
  position: relative;

  &::after {
    content: "*";
    position: absolute;
    left: -20rpx;
    top: 5rpx;
    color: #f24f4b;
  }
}
</style>