<template>
	<view style="background-color: #fff;min-height: 100vh;">
		<view class="backboxs">
			<image class="back" @click="back()" src="@/static/images/map/back.png" mode=""></image>
		</view>
		<view class="boxs">
			<view class="tip">
				<!-- <input class="uni-input" v-model="keyword" focus placeholder="请输入关键字" /> -->
				<u--input v-model="keyword" style="border: none;" placeholder="请输入关键字" clearable></u--input>
				<view class="" @click="nearby_search">
					<image style="z-index: 99999;" class="search" src="@/static/images/map/search.png" mode="">
					</image>
				</view>
			</view>
		</view>
		<map id="myMap" :enable-satellite="isMap" :circles="circles" :markers="markers" style="width:100%;height:40vh;"
			:longitude="longitude" :latitude="latitude" scale='16'>
		</map>
		<view class="landmark">
			<view class="landmarks" v-for="(report, index) in maks" :key="index" @click="getDetails(report)">
				<image class="landmark_rect" src="@/static/images/map/landmark_rect.png" mode=""></image>
				<view class="content">
					<view :style="{ fontSize: fontSizeLarge }" class="type">{{report.title}}</view>
					<view :style="{ fontSize: fontSizeSmall }" class="text">{{report.address}}</view>
				</view>
				<image class="unchange" src="@/static/images/map/unchange.png" mode=""></image>
			</view>
			<view>
				没有更多数据了
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getManagement
	} from '@/api/map/map.js'
	import QQMapWX from '@/utils/qqmap-wx-jssdk.js'
	export default {
		data() {
			return {
				MapShow: true,
				circles: [],
				title: 'map',
				// 腾讯地图实例
				qqmapsdk: '',
				// 输入框值
				changeValue: '',
				// 联想值
				suggestion: [],
				// 防抖
				time: null,
				nowProvince: 0,
				id: '',
				details: {},
				markers: [],
				maks: [],
				isMap: true,
				keyword: ''
			}
		},
		onLoad(props) {
			console.log(props, '2222')
			const that = this
			that.latitude = props.latitude
			that.longitude = props.longitude
			that.getLocation()
			// uni-app 授权弹出框

		},
		methods: {
			async nearby_search() {
				// qqmapsdk.search({
				// 	keyword: that.keyword, //搜索关键词
				// 	location: that.latitude + ',' + that
				// 		.longitude, //①String格式：lat<纬度>,lng<经度>（例：location: ‘39.984060,116.307520’）
				// 	success: function(res) { //搜索成功后的回调
				// 		for (var i = 0; i < res.data.length; i++) {
				// 			that.maks.push({ // 获取返回结果，放到mks数组中
				// 				title: res.data[i].title,
				// 				id: res.data[i].id,
				// 				latitude: res.data[i].location.lat,
				// 				longitude: res.data[i].location.lng,
				// 				address: res.data[i].address,
				// 				width: 20,
				// 				height: 20
				// 			})
				// 		}
				// 		console.log(that.maks, 'maksss')
				// 	},
				// 	fail: function(res) {
				// 		console.log(res, 'res999');
				// 	},
				// 	complete: function(res) {
				// 		console.log(res, 'res9888');
				// 	},
				// });


				const that = this
				that.markers.push({ // 获取返回结果，放到mks数组中
					title: '当前定位',
					id: 9999,
					iconPath: '../../static/images/map/red.png',
					latitude: that.latitude,
					longitude: that.longitude,
					width: 40,
					height: 57
				})
				const KEY = "G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV";
				let url = "https://apis.map.qq.com/ws/place/v1/search"
				const boundary = 'nearby' + '(' + that.latitude + ',' + that.longitude + ',' + '1000' + '1' + ')'
				// const boundary = 'nearby(28.681114,115.918377,1000,1)'
				console.log(boundary, '999999')
				console.log(that.keyword, '0999999')
				this.$jsonp(url, {
					key: KEY,
					keyword: that.keyword,
					output: "jsonp",
					boundary: boundary,
					location: that.latitude + ',' + that.longitude
				}).then(res => {
					that.maks = []
					for (var i = 0; i < res.data.length; i++) {
						that.maks.push({ // 获取返回结果，放到mks数组中
							title: res.data[i].title,
							id: res.data[i].id,
							latitude: res.data[i].location.lat,
							longitude: res.data[i].location.lng,
							address: res.data[i].address,
							width: 20,
							height: 20
						})
					}
					console.log(that.maks, 'maksss')
				}).catch(err => {
					console.log(err)
				})


			},
			getonload() {
				const that = this
				console.log(that.latitude, 'latitude')
				that.getLocation()
			},
			getDetails(data) {
				console.log(data, '222')
				this.$emit('custom-event', data);
				uni.navigateTo({
					url: '/pages/map/index?longitude=' + data.longitude + '&latitude=' + data.latitude +
						'&title=' + data.title
				});
			},
			back() {
				uni.navigateBack();
			},
			// 获取定位 生成地图实例
			async getLocation() {
				// 作用域问题
				const that = this
				that.markers.push({ // 获取返回结果，放到mks数组中
					title: '当前定位',
					id: 9999,
					iconPath: '../../static/images/map/red.png',
					latitude: that.latitude,
					longitude: that.longitude,
					width: 40,
					height: 57
				})
				const KEY = "G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV";
				let url = "https://apis.map.qq.com/ws/place/v1/search"
				const boundary = 'nearby' + '(' + that.latitude + ',' + that.longitude + ',' + '1000' + '1' + ')'
				// const boundary = 'nearby(28.681114,115.918377,1000,1)'
				console.log(boundary, '999999')
				this.$jsonp(url, {
					key: KEY,
					keyword: '嘉鱼政务服务中心',
					output: "jsonp",
					boundary: boundary,
					location: that.latitude + ',' + that.longitude
				}).then(res => {
					console.log(res, '1111')
					for (var i = 0; i < res.data.length; i++) {
						that.maks.push({ // 获取返回结果，放到mks数组中
							title: res.data[i].title,
							id: res.data[i].id,
							latitude: res.data[i].location.lat,
							longitude: res.data[i].location.lng,
							address: res.data[i].address,
							width: 20,
							height: 20
						})
					}
					console.log(that.maks, 'maksss')
				}).catch(err => {
					console.log(err)
				})

				// 腾讯地图Api
				// const qqmapsdk = new QQMapWX({
				// 	key: 'G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV' //这里填写自己申请的key
				// });
				// console.log(that.latitude, '1111')
				// console.log(that.longitude, '2222')
				// console.log(qqmapsdk.search, '000')
				// qqmapsdk.search({
				// 	keyword: 'null', //搜索关键词
				// 	location: that.latitude + ',' + that
				// 		.longitude, //①String格式：lat<纬度>,lng<经度>（例：location: ‘39.984060,116.307520’）
				// 	success: function(res) { //搜索成功后的回调
				// 		for (var i = 0; i < res.data.length; i++) {
				// 			that.maks.push({ // 获取返回结果，放到mks数组中
				// 				title: res.data[i].title,
				// 				id: res.data[i].id,
				// 				latitude: res.data[i].location.lat,
				// 				longitude: res.data[i].location.lng,
				// 				address: res.data[i].address,
				// 				width: 20,
				// 				height: 20
				// 			})
				// 		}
				// 		console.log(that.maks, 'maksss')
				// 	},
				// 	fail: function(res) {
				// 		console.log(res, 'res999');
				// 	},
				// 	complete: function(res) {
				// 		console.log(res, 'res9888');
				// 	},
				// });
			}
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		}
	}
</script>
<style lang="scss">
	.boxs {
		width: 750rpx;
		height: 131rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.15);
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		padding-top: 30rpx;

		.tip {
			width: 710rpx;
			height: 60rpx;
			margin: 0 20rpx;
			display: flex;
			align-items: center;
			background-color: #fff;
			border-radius: 3px;
			z-index: 200;
			border-radius: 30rpx;
			border: 2rpx solid #DDDDDD;

			.uni-input {
				flex: 1;
				height: 20rpx;
				padding-left: 40rpx;
			}

			span {
				width: 40px;
				color: #8b8c8f;
			}

			.search {
				width: 32rpx;
				height: 32rpx;
				margin-right: 32rpx;
				z-index: 99999;
			}
		}
	}


	.landmark {
		min-height: 500rpx;
		padding: 60rpx 30rpx 60rpx 30rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.15);
		border-radius: 40rpx;

		.landmarks {
			position: relative;
			display: flex;
			flex-direction: column;
			width: 100%;
			margin-bottom: 19rpx;

			.content {
				width: 590rpx;
				position: absolute;
				top: 34rpx;
				left: 34rpx;

				.type {
					font-family: PingFangSC, PingFang SC;
					font-weight: 600;
					font-size: 30rpx;
					color: #141414;
					line-height: 42rpx;
					font-style: normal;

				}

				.text {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #ACACAC;
					line-height: 33rpx;
					font-style: normal;
				}
			}

			.unchange {
				width: 24rpx;
				height: 24rpx;
				position: absolute;
				top: 69rpx;
				right: 34rpx;
			}

			.landmark_rect {
				width: 686rpx;
				height: 156rpx;
			}

			.date {
				color: #999;
			}


			.tips {
				position: absolute;
				right: 20rpx;
			}

		}
	}

	.Gohere {
		width: 60rpx;
		height: 60rpx;
	}

	.backboxs {

		background-color: #fff;
		padding: 10rpx 30rpx;
	}

	.back {
		width: 40rpx;
		height: 40rpx;
	}
</style>