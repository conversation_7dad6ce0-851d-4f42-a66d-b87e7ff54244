<template>
	<view class="content" :style="{
      paddingTop:  Props.statusBarHeight + 'px'
    }">
		<view class="backboxs">
			<image class="back" @click="back()" src="@/static/images/map/back.png" mode=""></image>
		</view>
		<!-- <navbar :Props="Props" :title='headtitle' @black="black"></navbar> -->
		<!-- 		<map :enable-satellite="isMap" id="myMap" :circles="circles" :markers="markers" style="width:100%"
			@markertap="onMarkerTap" :style="{height:mapHeight?'20vh':'60vh'}" :longitude="longitudeCenter"
			:latitude="latitudeCenter" scale='16'>
		</map> -->
		<view id="mapContainer" style="width:100%;" :style="{height:mapHeight?'20vh':'60vh'}"></view>
		<view class="boxs">
			<view class="changeHeight" @click="changeHeight()"></view>
			<image class="detail_background" src="@/static/images/map/detail_background.png" mode=""></image>
			<view class="header">
				<view :style="{ fontSize: fontSizeSuperLarge }" class="title">{{details.name}}</view>
				<image class="img" :src="y" mode="" v-for="(y, i) in uploadPicture" :key="y"></image>
			</view>
			<view class="address">
				<!-- <view class="detail">统一标准地址 {{details.uniAddressCode}}</view> -->
				<view :style="{ fontSize: fontSizeSmall }" class="detail">{{details.address}}</view>
				<view class="distance">
					<image class="locate" src="@/static/images/map/detail_locate.png" mode=""></image>
					<view :style="{ fontSize: fontSize26 }">距你直线{{details.distance}}公里</view>
				</view>

				<view class="copy" @click="copyChange(details.address)">
					<image class="copy_img" src="@/static/images/map/copy.png"></image>
					<view class="text">复制</view>
				</view>
			</view>
			<view class="affairs">
				<view class="tips">
					<image class="bubble" src="@/static/images/map/bubble.png" mode=""></image>
					<view :style="{ fontSize: fontSizeLarge }" class="title">政务办事</view>
				</view>
				<view class="grid-body">
					<!-- 					<scroll-view scroll-y="true" style="height: 174rpx;">
						<text :style="{ fontSize: fontSizeSmall }" class="text">{{details.governmentWork}}</text>
					</scroll-view> -->
					<scroll-view scroll-y="true" style="height: 204rpx;">
						<view v-for="(item, index) in governmentWork" :key="index">
							<view class="list">
								<view>
									{{index + 1 }}
								</view>
								<view class="">
									、
								</view>
								<view class="">
									{{item }}
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
			<view class="btns">
				<view class="order" @click="order">
					<view class="text">
						立即预约
					</view>
				</view>
				<view class="nav" @click="navigateToLocation()">
					<image class="img_nav" src="@/static/images/map/navigation.png" mode=""></image>
					<view class="text">
						导航
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getManagement,
		getMapList
	} from '@/api/map/map.js'
	import config from '@/config'
	import coordtransform from 'coordtransform';
	// import QQMapWX from '@/utils/qqmap-wx-jssdk.js'
	// import wx from 'weixin-js-sdk';
	import navbar from '@/components/Navbar/index.vue'
	import wxs from 'weixin-js-sdk'
	export default {
		components: {
			navbar
		},
		data() {
			return {
				headtitle: '办事大厅',
				Props: {
					imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: "", //导航高度(动态获取传参)
					bgColor: "", //导航栏背景色,不传参则默认#9CF
					capsuleTop: "", //胶囊顶部距离(动态获取传参)
					textColor: "", //导航标题字体颜色(不传默认#FFF)
					iconColor: "", //icon图标颜色(不传默认#FFF)
					blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
					backText: "", //默认字体(返回)
				},
				isMap: true,
				MapShow: true,
				circles: [],
				title: 'map',
				latitude: '39.9085',
				longitude: '117.39747',
				latitudeCenter: '39.9085',
				longitudeCenter: '117.39747',
				// 腾讯地图实例
				qqmapsdk: '',
				// 输入框值
				changeValue: '',
				// 联想值
				suggestion: [],
				// 防抖
				time: null,
				nowProvince: 0,
				id: '',
				details: {},
				markers: [],
				uploadPicture: [],
				governmentWork: [],
				nowlatitude: '',
				nowlongitude: '',
				iconPath: '',
				userInfo: {},
				mapHeight: true,
				reportList: [],
				map: null,
				newmarkers: [],
				destLongitude: '',
				destLatitude: '',
				imgBaseUrl: config.baseUrl + '/boot/',
			}
		},
		mounted() {
			this.loadSDK(() => {
				this.initMap();
			});
		},
		onLoad(props) {
			const that = this
			that.Props.statusBarHeight = getApp().globalData.statusBarHeight
			that.Props.capsuleTop = getApp().globalData.capsuleTop
			that.id = props.id
			that.nowlatitude = props.latitude
			that.nowlongitude = props.longitude
			that.userInfo =
				typeof this.$store.state.user.userInfo == 'string' ?
				JSON.parse(this.$store.state.user.userInfo) :
				this.$store.state.user.userInfo
			that.getLocation()
		},
		methods: {
			loadSDK(callback) {
				const script = document.createElement('script');
				script.src = 'https://api.tianditu.gov.cn/api?v=4.0&tk=561e846f9906522baf3ad3c7ee40ccda';
				script.onload = () => {
					callback();
				};
				document.head.appendChild(script);
			},
			initMap() {
				const that = this
				// 获取地图容器元素
				const mapContainer = document.getElementById('mapContainer');
				// 初始化天地图
				that.map = new T.Map(mapContainer);
				// 设置地图中心点和缩放级别
				that.map.centerAndZoom(new T.LngLat(that.longitudeCenter, that.latitudeCenter), 14);
				that.map.addEventListener('click', that.handleMapClick);
				that.getList(that.map)
			},
			async handleMarkerClick(pos) {
				// 标注点击事件处理函数

				console.log(pos, '87777')
				const that = this
				const id = pos.id
				const res = await getManagement({
					id: id,
					latitude: that.latitude,
					longitude: that.longitude
				})
				console.log(res, '9----------')
				that.longitudeCenter = res.result.longitude
				that.latitudeCenter = res.result.latitude
				that.map.centerAndZoom(new T.LngLat(that.longitudeCenter, that.latitudeCenter), 14);
				that.longitude = res.result.longitude
				that.latitude = res.result.latitude
				that.id = pos.id
				const staticDomainURL = config.staticDomainURL
				that.details = res.result
				let kilometers = that.details.distance / 1000;
				that.details.distance = kilometers.toFixed(1);
				if (that.details.governmentWorkShowType == 1) {
					that.governmentWork = that.details.governmentWorkStandard
				} else {
					that.governmentWork = that.details.governmentWork
				}
				if (that.governmentWork.includes(';')) {
					that.governmentWork = that.governmentWork.split(';')
				} else {
					that.governmentWork = [
						that.governmentWork
					]
				}
				if (that.details.uploadPicture) {
					if (that.details.uploadPicture.includes(',')) {
						that.uploadPicture = that.details.uploadPicture.split(',')
					} else {
						that.uploadPicture = [
							that.details.uploadPicture
						]
					}
				}
				that.latitude = that.details.latitude
				that.longitude = that.details.longitude
				that.latitudeCenter = that.details.latitude
				that.longitudeCenter = that.details.longitude
				that.governmentWork = that.governmentWork.filter((item) => item !== "")
				for (var i = 0; i < that.uploadPicture.length; i++) {
					that.uploadPicture[i] = that.imgBaseUrl + that.uploadPicture[i]
				}
				if (that.details.type == 1) {
					that.iconPath = '../../static/images/map/hall.png'
				} else if (that.details.type == 2) {
					that.iconPath = '../../static/images/map/selfHelp.png'
				} else if (that.details.type == 3) {
					that.iconPath = '../../static/images/map/service.png'
				} else if (that.details.type == 4) {
					that.iconPath = '../../static/images/map/hospital.png'
				} else if (that.details.type == 5) {
					that.iconPath = '../../static/images/map/Health.png'
				} else if (that.details.type == 6) {
					that.iconPath = '../../static/images/map/bank.png'
				} else if (that.reportList[i].type == 7) {
					that.iconPath = '../../static/images/map/cdz.png'
				} else if (that.reportList[i].type == 8) {
					that.iconPath = '../../static/images/map/tcc.png'
				}
				that.map.centerAndZoom(new T.LngLat(this.longitude, this.latitude), 14)
			},

			addCustomMarkers() {
				this.markers.forEach(item => {
					item.lat = Number(item.latitude)
					item.lng = Number(item.longitude)
				});
				const positions = this.markers
				positions.forEach(pos => {
					const customIcon = new T.Icon({
						iconUrl: pos.iconPath, // 自定义图标的URL
						iconSize: new T.Point(pos.width, pos.height), // 图标尺寸
						iconAnchor: new T.Point(32, 32) // 图标锚点
					});
					const lnglat = new T.LngLat(Number(pos.longitude), Number(pos.latitude));
					const marker = new T.Marker(lnglat, {
						icon: customIcon
					});
					this.newmarkers.push(marker);
					// marker.bindPopup(`<div>${pos.name}</div>`);
					marker.addEventListener('click', () => {
						this.handleMarkerClick(pos);
					});
					this.map.addOverLay(marker);
				});
				// var marker = new T.Marker(new T.LngLat(116.411794, 39.9068));
				// map.addOverLay(marker);
			},
			//获取地图列表
			async getList(map) {
				const that = this
				const res = await getMapList({
					longitude: that.nowlongitude,
					latitude: that.nowlatitude,
					type: '',
				})
				that.circles = []
				that.markers = []
				that.circles.push({
					latitude: that.latitude,
					longitude: that.longitude,
					color: '#4ccfffff',
					fillColor: '#7cb5ec88',
					radius: 0,
					strokeWidth: 1
				})
				that.reportList = res?.result
				for (let i = 0; i < that.reportList.length; i++) {
					if (that.reportList[i].type == 1) {
						that.iconPath = '../../static/images/map/hall.png'
					} else if (that.reportList[i].type == 2) {
						that.iconPath = '../../static/images/map/selfHelp.png'
					} else if (that.reportList[i].type == 3) {
						that.iconPath = '../../static/images/map/service.png'
					} else if (that.reportList[i].type == 4) {
						that.iconPath = '../../static/images/map/hospital.png'
					} else if (that.reportList[i].type == 5) {
						that.iconPath = '../../static/images/map/Health.png'
					} else if (that.reportList[i].type == 6) {
						that.iconPath = '../../static/images/map/bank.png'
					} else if (that.reportList[i].type == 7) {
						that.iconPath = '../../static/images/map/cdz.png'
					} else if (that.reportList[i].type == 8) {
						that.iconPath = '../../static/images/map/tcc.png'
					}
					var a = {
						id: that.reportList[i].id,
						iconPath: that.iconPath,
						latitude: Number(that.reportList[i].latitude),
						longitude: Number(that.reportList[i].longitude),
						width: 28,
						height: 37
					}
					that.markers.push(a);
				}
				that.addCustomMarkers(map)
			},
			async onMarkerTap(e) {
				const that = this
				const {
					markerId
				} = e.detail;
				const id = that.reportList[markerId].id
				const res = await getManagement({
					id: id,
					latitude: that.nowlatitude,
					longitude: that.nowlongitude
				})
				that.longitudeCenter = res.result.longitude
				that.latitudeCenter = res.result.latitude
				that.longitude = res.result.longitude
				that.latitude = res.result.latitude
				that.id = that.reportList[markerId].id
				// that.getLocation()
			},
			changeHeight() {
				if (this.mapHeight) {
					this.mapHeight = false
				} else {
					this.mapHeight = true
				}
			},
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			async order() {
				if (this.userInfo.mobilePhone) {
					uni.navigateTo({
						url: '/pages/map/order?name=' + this.details.name
					});
				} else {
					uni.showToast({
						title: '请先完成实名认证',
						icon: 'none'
					})
				}
			},
			async copyChange(data) {
				uni.setClipboardData({
					data: data,
					success: function(res) {
						console.log('success', res);
						uni.getClipboardData({
							success: function(res) {
								console.log('粘贴', res);
							}
						});
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						});
					},
					fail: (error) => {
						console.log('失败', error);
						uni.showToast({
							title: '复制失败',
							icon: 'none'
						});
					}
				});
			},
			wgs84ToGcj02(latitude, longitude) {
				// 使用 coordtransform 库将 WGS84 坐标转换为 GCJ02 坐标
				const gcj02Coord = coordtransform.wgs84togcj02(longitude, latitude);
				return {
					latitude: gcj02Coord[1],
					longitude: gcj02Coord[0]
				};
			},
			// 导航
			navigateToLocation() {
				const that = this
				that.destLongitude = that.wgs84ToGcj02(that.latitude, that.longitude).longitude
				that.destLatitude = that.wgs84ToGcj02(that.latitude, that.longitude).latitude
				that.destLongitude = that.destLongitude.toFixed(5)
				that.destLatitude = that.destLatitude.toFixed(5)
				const url = `/pages-sub/location/index?lat=` + Number(that.destLatitude) + `&lon=` + Number(that
						.destLongitude) +
					`&name=目的地&address=目的地`
				console.log(url, '99999')
				wxs.miniProgram.navigateTo({
					url: url
				})
			},
			async getDetails(id) {
				console.log(id, '888888')
				uni.navigateTo({
					url: '/pages/map/details?id=' + id
				});

			},

			// 获取定位 生成地图实例
			async getLocation() {
				// 作用域问题
				const that = this
				const staticDomainURL = config.staticDomainURL
				const res = await getManagement({
					id: that.id,
					latitude: that.nowlatitude,
					longitude: that.nowlongitude
				})
				that.details = res.result
				let kilometers = that.details.distance / 1000;
				that.details.distance = kilometers.toFixed(1);
				if (that.details.governmentWorkShowType == 1) {
					that.governmentWork = that.details.governmentWorkStandard
				} else {
					that.governmentWork = that.details.governmentWork
				}
				if (that.governmentWork.includes(';')) {
					that.governmentWork = that.governmentWork.split(';')
				} else {
					that.governmentWork = [
						that.governmentWork
					]
				}
				if (that.details.uploadPicture) {
					if (that.details.uploadPicture.includes(',')) {
						that.uploadPicture = that.details.uploadPicture.split(',')
					} else {
						that.uploadPicture = [
							that.details.uploadPicture
						]
					}
				}
				that.latitude = that.details.latitude
				that.longitude = that.details.longitude
				that.latitudeCenter = that.details.latitude
				that.longitudeCenter = that.details.longitude
				that.governmentWork = that.governmentWork.filter((item) => item !== "")
				for (var i = 0; i < that.uploadPicture.length; i++) {
					that.uploadPicture[i] = that.imgBaseUrl + that.uploadPicture[i]
				}
				console.log(that.uploadPicture, '99999')
				if (that.details.type == 1) {
					that.iconPath = '../../static/images/map/hall.png'
				} else if (that.details.type == 2) {
					that.iconPath = '../../static/images/map/selfHelp.png'
				} else if (that.details.type == 3) {
					that.iconPath = '../../static/images/map/service.png'
				} else if (that.details.type == 4) {
					that.iconPath = '../../static/images/map/hospital.png'
				} else if (that.details.type == 5) {
					that.iconPath = '../../static/images/map/Health.png'
				} else if (that.details.type == 6) {
					that.iconPath = '../../static/images/map/bank.png'
				} else if (that.details.type == 7) {
					that.iconPath = '../../static/images/map/cdz.png'
				} else if (that.details.type == 8) {
					that.iconPath = '../../static/images/map/tcc.png'
				}
				// that.map.centerAndZoom(new T.LngLat(this.longitude, this.latitude), 14)
			}
		},
		computed: {
			fontSize26() {
				return this.$store.state.fontSize26; // 从Vuex获取小号字体大小
			},
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			},
			fontSizeSuperLarge() {
				return this.$store.state.fontSizeSuperLarge; // 从Vuex获取大号字体大小
			}
		}
	}
</script>
<style lang="scss">
	.content {
		.boxs {
			min-height: 80vh;
			background-color: #fff;
			padding: 0 4%;
			position: relative;
			padding-top: 70rpx;
			border-top-left-radius: 40rpx;
			border-top-right-radius: 40rpx;
			padding-bottom: 20rpx;

			.detail_background {
				width: 750rpx;
				height: 118rpx;
				position: absolute;
				top: 0;
				left: 0;
			}

			.header {
				width: 686rpx;
				height: 295rpx;
				position: relative;
				z-index: 1;
				margin-bottom: 20rpx;
				padding: 30rpx 0 0 34rpx;

				.title {
					font-weight: 500;
					font-size: 36rpx;
					color: #141414;
					line-height: 50rpx;
					font-style: normal;
					margin-bottom: 20rpx;
				}

				.type {
					min-width: 40rpx;
					margin-left: 20rpx;
					height: 36rpx;
					background: #FFF9F0;
					border-radius: 8rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 20rpx;
					color: #F3AB6E;
					line-height: 36rpx;
					font-style: normal;
				}
			}

			.header:before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(87deg, #E6F4FF 0%, #FFFFFF 100%);
				border-radius: 20rpx;
				opacity: 0.48;
				z-index: -1;
			}

			.address {
				position: relative;
				z-index: 1;
				width: 686rpx;
				height: 193rpx;
				margin-bottom: 20rpx;
				padding: 30rpx 0 0 34rpx;

				.detail {
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;
					line-height: 33rpx;
					font-style: normal;
					margin-bottom: 15rpx;
				}

				.distance {
					display: flex;
					align-items: center;
					font-weight: 500;
					font-size: 26rpx;
					color: #057FFE;
					line-height: 37rpx;
					font-style: normal;
				}
			}

			.address:before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(87deg, #E6F4FF 0%, #FFFFFF 100%);
				border-radius: 20rpx;
				opacity: 0.48;
			}

			.affairs {
				width: 686rpx;
				// height: 300rpx;
				position: relative;
				z-index: 1;

				.tips {
					position: relative;
					padding: 30rpx 0 0 34rpx;
					margin-bottom: 19rpx;

					.bubble {
						width: 33rpx;
						height: 34rpx;
						position: absolute;
						z-index: 99;
						top: 28rpx;
						left: 19rpx;
					}

					.title {
						font-weight: 500;
						font-size: 30rpx;
						color: #141414;
						line-height: 42rpx;
						font-style: normal;
						z-index: 999;

					}
				}

				.grid-body {
					padding: 0 34rpx;
					padding-bottom: 30rpx;

					.list {
						display: flex;
						margin-bottom: 10rpx;
						font-weight: 400;
						font-size: 24rpx;
						color: #333333;
						line-height: 44rpx;
						font-style: normal;
					}

					.text {
						font-weight: 400;
						font-size: 24rpx;
						color: #333333;
						line-height: 44rpx;
						text-align: left;
						font-style: normal;
					}

					.grid-item-box {
						width: 120rpx;
						// height: 40rpx;
						min-height: 64rpx;
						border-radius: 8rpx;
						border: 1rpx solid #DADADA;
						text-align: left;

						.text {
							font-weight: 400;
							font-size: 24rpx;
							color: #666666;
							line-height: 33rpx;
							font-style: normal;
						}
					}
				}

			}

			.affairs:before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(87deg, #E6F4FF 0%, #FFFFFF 100%);
				border-radius: 20rpx;
				opacity: 0.48;
			}
		}


		.nav {
			width: 160rpx;
			height: 64rpx;
			background: #057FFE;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			padding: 0 24rpx;
			line-height: 64rpx;

			.img_nav {
				width: 28rpx;
				height: 28rpx;
				margin-right: 31rpx;
			}

			.text {
				font-weight: 500;
				font-size: 26rpx;
				color: #FFFFFF;
				line-height: 64rpx;
				font-style: normal;
			}
		}

		.copy {
			position: absolute;
			bottom: 36rpx;
			right: 30rpx;
			width: 120rpx;
			height: 48rpx;
			border-radius: 8rpx;
			border: 1rpx solid #D0D0D0;
			display: flex;
			align-items: center;
			padding-left: 17rpx;

			.copy_img {
				width: 24rpx;
				height: 26rpx;
			}

			.text {
				font-weight: 400;
				font-size: 24rpx;
				color: #666666;
				line-height: 33rpx;
				font-style: normal;
				margin-left: 15rpx;
			}
		}

		.img {
			width: 240rpx;
			height: 165rpx;
			margin-right: 20rpx;
		}

		.locate {
			width: 21rpx;
			height: 27rpx;
			margin-right: 9rpx;
		}
	}

	.content::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-top-left-radius: 40rpx;
		border-top-right-radius: 40rpx;
		opacity: 0.1;
	}

	.order {
		width: 160rpx;
		height: 64rpx;
		border-radius: 8rpx;
		border: 1rpx solid #D0D0D0;
		display: flex;
		align-items: center;
		padding: 0 24rpx;
		line-height: 64rpx;
		margin-right: 20rpx;

		.img_share {
			width: 28rpx;
			height: 28rpx;
			margin-right: 31rpx;
		}

		.text {
			font-weight: 500;
			font-size: 26rpx;
			color: #333333;
			font-style: normal;
		}
	}

	.btns {
		display: flex;
		align-items: center;
		// float: right;
		margin-left: 50%;
		margin-top: 80rpx;
		margin-bottom: 40rpx;
	}

	.backboxs {

		background-color: #fff;
		padding: 10rpx 30rpx;
	}

	.back {
		width: 40rpx;
		height: 40rpx;
	}

	.changeHeight {
		position: absolute;
		top: -40rpx;
		left: 330rpx;
		z-index: 11999999;
		height: 20rpx;
		width: 100rpx;
		border-radius: 10rpx;
		background-color: #999;

	}
</style>