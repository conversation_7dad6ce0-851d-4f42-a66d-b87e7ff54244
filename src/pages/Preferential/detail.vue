<template>
	<view :style="{
      paddingTop:  Props.statusBarHeight + 'px'
    }">
		<!-- <navbar :Props="Props" :title='headtitle' @black="black"></navbar> -->
		<view class="content">
			<view class="title">{{detail.title}}</view>
			<view class="brief">
				<view class="createTime">{{detail.releaseSource}}</view>
				<view class="createTime">{{detail.createTime}}</view>
			</view>
			<!-- <rich-text :nodes="detail.content"></rich-text> -->

			<u-parse :content="detail.content"></u-parse>

<!-- 			<view v-if="handleResult.length>0" class="reports">
				<view v-for="(report, index) in handleResult" :key="index" @click="downFile(report.wordUrl)"
					class="look">查看文件
				</view>
			</view> -->
		</view>
	</view>
</template>

<script src="./jweixin-1.3.2.js" type="text/javascript" charset="utf-8"></script>
<script>
	import {
		getPreferentialDetail
	} from "@/api/preferential/preferential.js"
	import config from '@/config'
	import {
		getAccessToken
	} from '@/utils/auth'
	import navbar from '@/components/Navbar/index.vue'
	export default {
		components: {
			navbar
		},
		data() {
			return {
				headtitle: '惠企政策',
				Props: {
					imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: "", //导航高度(动态获取传参)
					bgColor: "", //导航栏背景色,不传参则默认#9CF
					capsuleTop: "", //胶囊顶部距离(动态获取传参)
					textColor: "", //导航标题字体颜色(不传默认#FFF)
					iconColor: "", //icon图标颜色(不传默认#FFF)
					blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
					backText: "", //默认字体(返回)
				},
				detail: {},
				handleResult: [],
				id: '',
				imgBaseUrl: config.baseUrl + '/boot/',
				baseUrl: config.baseUrl,
			}
		},
		onLoad(props) {
			const that = this
			console.log(props, '999999')
			that.Props.statusBarHeight = getApp().globalData.statusBarHeight
			that.Props.capsuleTop = getApp().globalData.capsuleTop
			that.id = props.id
			that.getList()
		},
		methods: {
			async getList() {
				const that = this
				const param = {
					id: that.id
				}
				console.log(param)
				const res = await getPreferentialDetail(param)
				console.log(res, '29999')
				that.detail = res.result
				// that.file()
			},

			downFile(fileUrl) {
				let fileFormat = fileUrl.substr(fileUrl.lastIndexOf('.') + 1)
				console.log(fileFormat, '11111')
				if (fileFormat === 'png' || fileFormat === 'jpg') {
					uni.previewImage({
						urls: [this.baseUrl + '/boot/' + fileUrl]
					})
					return
				}
				uni.showLoading({
					title: '正在打开...'
				})
				uni.downloadFile({
					url: this.baseUrl + '/boot/' + fileUrl,
					header: {
						Authorization: getAccessToken()
					},
					// filePath: `${wx.env.USER_DATA_PATH}/${'文件'}.${fileFormat}`,
					success: (res) => {
						if (res.statusCode === 200) {
							uni.openDocument({
								filePath: res.tempFilePath,
								fileType: fileFormat,
								showMenu: true,
								success: () => {
									uni.hideLoading()
								},
								fail: () => {
									uni.$u.toast('打开文档失败,请检查格式是否正确')
								}
							})
						}
					}
				})
			},



			// downFile(fileUrl) {
			// 	let fileFormat = fileUrl.substr(fileUrl.lastIndexOf('.') + 1)
			// 	console.log(fileFormat, '11111')
			// 	if (fileFormat === 'png' || fileFormat === 'jpg') {
			// 		uni.previewImage({
			// 			urls: [this.baseUrl + '/boot/' + fileUrl]
			// 		})
			// 		return
			// 	}
			// 	uni.showLoading({
			// 		title: '正在打开...'
			// 	})
			// 	uni.downloadFile({
			// 		url: this.baseUrl + '/boot/' + fileUrl,
			// 		header: {
			// 			Authorization: getAccessToken()
			// 		},
			// 		// filePath: `${wx.env.USER_DATA_PATH}/${'文件'}.${fileFormat}`,
			// 		success: (res) => {
			// 			if (res.statusCode === 200) {
			// 				uni.setClipboardData({
			// 					data: res.tempFilePath,
			// 					success: function() {
			// 						console.log('success'); //调用方法成功
			// 						uni.hideLoading()
			// 					}
			// 				})
			// 				console.log(res.tempFilePath);
			// 				var oA = document.createElement("a");
			// 				oA.download = ''; // 设置下载的文件名，默认是'下载'
			// 				oA.target = "_blank"
			// 				oA.href = res.tempFilePath; //临时路径再保存到本地
			// 				document.body.appendChild(oA);
			// 				oA.click();
			// 				oA.remove(); // 下载之后把创建的元素删除
			// 				uni.hideLoading()
			// 				// uni.openDocument({
			// 				// 	filePath: res.tempFilePath,
			// 				// 	fileType: fileFormat,
			// 				// 	showMenu: true,
			// 				// 	success: () => {
			// 				// 		uni.hideLoading()
			// 				// 	},
			// 				// 	fail: () => {
			// 				// 		uni.$u.toast('打开文档失败,请检查格式是否正确')
			// 				// 	}
			// 				// })
			// 				// wx.openDocument({
			// 				// 	filePath: res.tempFilePath,
			// 				// 	fileType: fileFormat,
			// 				// 	showMenu: true,
			// 				// 	success: function(res) {
			// 				// 		console.log('打开文档成功')
			// 				// 		uni.hideLoading()
			// 				// 	}
			// 				// })
			// 			}
			// 		}
			// 	})
			// },

			open(report) {
				console.log(report, '01111')
				// uni.downloadFile({
				// 	url: report.path,
				// 	success: function(res) {
				// 		var filePath = res.tempFilePath;
				// 		uni.openDocument({
				// 			filePath: filePath,
				// 			showMenu: true,
				// 			success: function(res) {
				// 				console.log('打开文档成功');
				// 			}
				// 		});
				// 	}
				// });
			},
			file() {
				const that = this
				console.log(that.detail.wordUrl, '00000')
				const staticDomainURL = config.staticDomainURL
				if (that.detail.wordUrl != null) {
					console.log(111)
					that.handleResult = that.detail.wordUrl.split(',')
					that.handleResult = Object.values(that.handleResult).map(path => {
						const fileName = path.split('/').pop(); // 获取文件名
						const name = fileName.split('_')[0]; // 提取名称
						return {
							name: name,
							path: staticDomainURL + path,
							wordUrl: that.detail.wordUrl
						};
					});
				}
				console.log(that.handleResult, 'hand------')
			}
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		}
	}
</script>
<style lang="scss">
	.content {
		background: #F4F8FC;
		min-height: 100vh;
		background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 100%);
		border-radius: 32px;
		margin: 32rpx;
		padding: 34rpx;

		.title {
			font-weight: 500;
			font-size: 36rpx;
			color: #444444;
			line-height: 50rpx;
			text-align: center;
			font-style: normal;
		}

		.brief {
			// display: flex;
			// align-items: center;
			margin-bottom: 28rpx;

			.createTime {
				font-weight: 400;
				font-size: 24rpx;
				color: #ACACAC;
				line-height: 33rpx;
				text-align: left;
				font-style: normal;
				// margin-right: 20px;
				margin-bottom: 20rpx;
			}
		}

		.reports {
			margin-top: 200rpx;
			flex-wrap: wrap;

			.look {
				flex: 0 0 auto;
				/* 让盒子按内容宽度宽度 */
				margin: 5px;
				width: 282rpx;
				height: 80rpx;
				background: #057FFE;
				border-radius: 8rpx;
				text-align: center;
				font-weight: 500;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: 80rpx;
				letter-spacing: 2px;
				text-align: center;
				font-style: normal;
			}
		}

	}
</style>