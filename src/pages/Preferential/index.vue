<template>
	<view class="content" :style="{
      paddingTop:  Props.statusBarHeight + 'px'
    }">
		<!-- <navbar :Props="Props" :title='headtitle' @black="black"></navbar> -->
		<view class="top">
			<view class="tip">
				<input class="uni-input" v-model="searchInput" focus @input="clearInput" placeholder="请输入关键字" />
				<view class="cacel_button" v-if="showClearIcon" @click="clearIcon">
					<image class="cacel" src="@/static/images/map/cancel.png" mode=""></image>
				</view>
				<view class="search_boxs" @click="nearby_search">
					<image style="z-index: 99999;" class="search" src="@/static/images/map/search.png" mode="">
					</image>
				</view>
			</view>
		</view>
		<view class="" style="padding-top: 180rpx;">
			
		</view>
		<view class="boxs">
			<view class="reports" v-for="(report, index) in reportList" :key="index" @click="detail(report)">
				<image class="record_bg" src="@/static/images/interaction/record_bg.png" mode=""></image>
				<view class="date">{{report.updateTime}}</view>
				<view class="text">{{report.title}}</view>
				<view class="substance">{{report.content}}</view>
			</view>
			<uni-load-more iconType="circle" :status="loadMoreStatus" />
		</view>
	</view>
</template>

<script>
	import {
		getPreferentialPage,
		getPreferentialDetail
	} from "@/api/preferential/preferential.js"
	import navbar from '@/components/Navbar/index.vue'
	export default {
		components: {
			navbar
		},
		data() {
			return {
				searchInput: '',
				reportList: [],
				searchList: [],
				pageNo: 1,
				pageSize: 10,
				filterList: [],
				showButtons: [],
				msgType: '',
				messageText: '',
				loadMoreStatus: 'more',
				headtitle: '惠企政策',
				Props: {
					imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: "", //导航高度(动态获取传参)
					bgColor: "", //导航栏背景色,不传参则默认#9CF
					capsuleTop: "", //胶囊顶部距离(动态获取传参)
					textColor: "", //导航标题字体颜色(不传默认#FFF)
					iconColor: "", //icon图标颜色(不传默认#FFF)
					blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
					backText: "", //默认字体(返回)
				},
				showClearIcon: false
			}
		},
		onLoad: function() {
			this.Props.statusBarHeight = getApp().globalData.statusBarHeight
			this.Props.capsuleTop = getApp().globalData.capsuleTop
		},
		methods: {
			detail(report) {
				console.log(report, '09999')
				uni.navigateTo({
					url: '/pages/Preferential/detail?id=' + report.id
				});

			},
			async nearby_search() {
				const that = this
				that.pageNo = 1
				const param = {
					pageNo: that.pageNo,
					pageSize: that.pageSize,
					// type: retype,
					title: that.searchInput,
					// 	telNumber: this.searchInput,
				}
				console.log(param)
				const res = await getPreferentialPage(param)
				console.log(res, '29999')
				// that.reportList = res?.result?.records
				that.reportList = res.result.records
				if (res.result.records.length < this.pageSize) {
					this.loadMoreStatus = 'noMore'
				} else {
					this.loadMoreStatus = 'more'
				}
				that.reportList.forEach((item, index) => {
					item.content = item.content.replace(/<[^>]*>/g, '');
					item.content = item.content.replace(/&nbsp;/g, ' ');
					// 替换其他特殊字符
					item.content = item.content.replace(/&ldquo;/g, '"').replace(/&rdquo;/g, '"');
					// 去除多余的空格
					item.content = item.content.replace(/\s+/g, ' ').trim();
				})

				console.log(that.reportList, '2222')
			},
			clearIcon: function() {
				this.searchInput = '';
				this.showClearIcon = false;
			},
			clearInput: function(event) {
				console.log(event, '11111')
				this.searchInput = event.detail.value;
				this.nearby_search()
				if (event.detail.value.length > 0) {
					this.showClearIcon = true;
				} else {
					this.showClearIcon = false;
				}
			},
			onReachBottom() {
				if (this.loadMoreStatus !== 'more') return
				this.loadMoreStatus = 'loading'
				this.pageNo++
				this.getComplaintList()
			},
			emptyButton(event) {
				this.showButtons = []
			},
			toggleButtons(url) {
				uni.downloadFile({
					url: url,
					success: function(res) {
						var filePath = res.tempFilePath;
						uni.openDocument({
							filePath: filePath,
							showMenu: true,
							success: function(res) {
								console.log('打开文档成功');
							}
						});
					}
				});
			},
			textFilter(text) {
				if (text.length > 36) {
					return text.substring(0, 37) + '...'
				}
				return text
			},
			async getComplaintList() {
				const that = this
				const param = {
					pageNo: that.pageNo,
					pageSize: that.pageSize,
					// type: retype,
					title: that.searchInput,
					// 	telNumber: this.searchInput,
				}
				console.log(param)
				const res = await getPreferentialPage(param)
				console.log(res, '29999')
				// that.reportList = res?.result?.records
				that.reportList = that.reportList.concat(res.result.records)
				if (res.result.records.length < this.pageSize) {
					this.loadMoreStatus = 'noMore'
				} else {
					this.loadMoreStatus = 'more'
				}
				that.reportList.forEach((item, index) => {
					item.content = item.content.replace(/<[^>]*>/g, '');
					item.content = item.content.replace(/&nbsp;/g, ' ');
					// 替换其他特殊字符
					item.content = item.content.replace(/&ldquo;/g, '"').replace(/&rdquo;/g, '"');
					// 去除多余的空格
					item.content = item.content.replace(/\s+/g, ' ').trim();
				})
				// that.reportList.forEach((item, index) => {
				// 	item.content = item.content.replace(/<[^>]*>/g, '');
				// })

				console.log(that.reportList, '2222')
			},
		},
		mounted() {
			this.getComplaintList()
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			},
			fontSizeSuperLarge() {
				return this.$store.state.fontSizeSuperLarge; // 从Vuex获取大号字体大小
			}
		}
	}
</script>

<style lang="scss">
	.content {
		// margin: 30rpx;
		background: #F4F8FC;
		min-height: 100vh;

		.top {
			background-color: #ffffff;
			box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.15);
			border-radius: 0rpx 0rpx 20rpx 20rpx;
			padding: 40rpx 0;
			margin-bottom: 32rpx;
			position: fixed;
			z-index: 9999;
			.Province {
				text-align: center;
				font-family: -apple-system;
				background-color: #ffffff;
				padding-top: 36rpx;
				display: flex;
				justify-content: space-around;
				background-color: white;

				.text {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 30rpx;
					color: rgba(0, 0, 0, 0.9);
					line-height: 30rpx;
					font-style: normal;
				}

				.line-bottom {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 30rpx;
					color: rgba(20, 20, 20, 0.9);
					line-height: 30rpx;
					font-style: normal;
				}

				.line {
					width: 55rpx;
					height: 10rpx;
				}
			}


			.tip {
				width: 710rpx;
				height: 60rpx;
				margin: 0 20rpx;
				display: flex;
				align-items: center;
				background-color: #fff;
				border-radius: 3px;
				z-index: 200;
				border-radius: 30rpx;
				border: 2rpx solid #DDDDDD;

				.uni-input {
					flex: 1;
					height: 20rpx;
					padding-left: 40rpx;
				}

				span {
					width: 40px;
					color: #8b8c8f;
				}

				.search_boxs {
					width: 50rpx;
					height: 50rpx;
					display: flex;
					align-items: center;
					/* 图片在垂直方向上居中 */
					justify-content: center;
					/* 图片在水平方向上居中 */
					margin-right: 32rpx;
				}

				.search {
					width: 32rpx;
					height: 32rpx;
					z-index: 99999;
				}
			}

			.screen {
				display: flex;
				align-items: center;
				justify-content: space-around;
				background-color: #FFF;
				padding: 30rpx 0;
				// box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0,0,0,0.15);
				border-radius: 0rpx 0rpx 20rpx 20rpx;

				.orient {
					display: flex;
					align-items: center;
				}

				.uni-list {
					display: flex;
					align-items: center;
					line-height: 30rpx;
				}
			}

		}

		.boxs {
			background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 100%);
			box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
			border-radius: 32rpx;
			// padding: 32rpx;
			margin: 0 32rpx;
			height: 100%;
			width: 686rpx;
			display: flex;
			gap: 25rpx;
			flex-direction: column;
			align-items: center;
			padding: 40rpx 20rpx;
			.reports {
				background-color: #FFF;
				display: flex;
				flex-direction: column;
				width: 100%;
				height: 188rpx;
				gap: 30rpx;
				position: relative;
				border-bottom: 2rpx solid #E7F0FF;

				.record_bg {
					width: 424rpx;
					height: 180rpx;
					position: absolute;
				}

				.type {
					position: absolute;
					font-weight: 500;
					font-size: 32rpx;
					color: #181818;
					line-height: 45rpx;
					font-style: normal;
					top: 16rpx;
					left: 77rpx;
				}

				.date {
					position: absolute;
					font-weight: 400;
					font-size: 24rpx;
					color: #ACACAC;
					line-height: 33rpx;
					font-style: normal;
					top: 111rpx;
					left: 14rpx;
				}

				.text {
					width: 600rpx;
					position: absolute;
					font-weight: 400;
					font-size: 28rpx;
					color: #666666;
					line-height: 40rpx;
					font-style: normal;
					left: 14rpx;
					top: 16rpx;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.substance {
					width: 600rpx;
					position: absolute;
					font-weight: 400;
					font-size: 28rpx;
					color: #666666;
					line-height: 40rpx;
					font-style: normal;
					left: 14rpx;
					top: 60rpx;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 1;
					overflow: hidden;
					text-overflow: ellipsis;
				}



				.icon {
					width: 48rpx;
					height: 48rpx;
					position: absolute;
					top: 14rpx;
					left: 14rpx;
				}
			}
		}
	}

	.cacel_button {
		width: 50rpx;
		height: 50rpx;
		margin-right: 10rpx;
		display: flex;
		align-items: center;
		/* 图片在垂直方向上居中 */
		justify-content: center;
		/* 图片在水平方向上居中 */
	}


	.cacel {
		width: 32rpx;
		height: 32rpx;

	}
</style>