<template>
  <view
    :style="{
      paddingTop: Props.statusBarHeight + 'px'
    }"
  >
    <navbar :Props="Props" :title="headtitle" @black="black"></navbar>
    <view :style="{ fontSize: fontSizeMedium }" class="main-box" v-if="type == 0">
      <p class="title">欢迎使用“嘉有小乔”惠企服务用户协议</p>
      <view class="date">
        <text>更新日期：2024/10/23</text>
        <text>生效日期：2024/10/23</text>
      </view>
      <p>重要须知：</p>
      <p class="content">
        在注册、登录或使用“嘉有小乔”惠企服务（以下简称“本小程序”）提供的任何服务之前，请您务必仔细阅读并充分理解本用户协议（以下简称“协议”）。本协议由您与本小程序的运营者共同缔结，具有合同效力。一旦您开始使用本小程序，即表示您已接受本协议的全部条款，并同意成为本协议的一方当事人，受本协议的约束。
      </p>
      <p>一、服务内容</p>
      <p class="content">
        服务范围：本小程序提供的服务包括但不限于信息展示、功能应用、互动交流等。具体服务内容以本小程序实际提供为准。
        服务变更：我们有权根据业务发展和技术升级的需要，随时变更、中断或终止部分或全部服务，并有权对收费标准、方式等进行调整。我们将通过本小程序内公告或其他适当方式提前通知您相关变更。
      </p>
      <p>二、用户账号</p>
      <p class="content">
        账号注册：您需按照本小程序的注册要求，填写真实、准确、完整的注册信息，完成注册流程。您应妥善保管您的账号及密码，因您保管不善导致账号被冒用或信息泄露的，您将自行承担相应责任。
        账号使用：您应遵守法律法规、社会公德及本协议的规定，合法、合规地使用您的账号。您不得利用本小程序进行任何违法、违规、侵犯他人权益或损害本小程序利益的行为。
      </p>
      <p>三、知识产权</p>
      <p class="content">
        内容归属：本小程序内的所有信息、数据、文字、图形、图像、音频、视频、软件、程序、源代码、界面设计、版面框架、有关数据或电子文档等均为本小程序或其权利人的财产，受著作权、商标权、专利权等知识产权和其他相关法律的保护。
        授权使用：未经本小程序或其权利人的明确书面许可，您不得以任何形式复制、修改、传播、出售、转让、汇编或以其他方式使用本小程序内的任何内容。
      </p>
      <p>四、责任限制</p>
      <p class="content">
        服务中断：因不可抗力、系统故障、第三方原因等导致本小程序服务中断或无法正常使用的，我们不承担任何责任。但我们将尽快恢复服务，并提前通过适当方式通知您。
        内容准确性：本小程序中的信息可能因各种因素存在时间差、误解或错误。我们对信息的准确性、完整性、合法性或真实性不作任何保证，也不承担因信息不准确、不完整、不合法或虚假而产生的任何责任。
      </p>
      <p>五、违约责任</p>
      <p class="content">
        如您违反本协议中的任何条款，我们有权采取包括但不限于限制账号使用、删除账号、终止服务、追究法律责任等措施。
      </p>
      <p>六、协议变更与终止</p>
      <p class="content">
        协议变更：我们有权根据业务发展和法律法规的变化适时修订本协议。修订后的协议一经公布即生效，并在公布时通过本小程序内公告或其他适当方式通知您。如您不同意修订后的协议，应立即停止使用本小程序。
        协议终止：您或我们均有权随时终止本协议。您可以通过删除账号或停止使用本小程序来终止本协议；我们也可以在法律法规允许的范围内，基于合理理由单方面终止本协议。
      </p>
      <p>七、法律适用与争议解决</p>
      <p class="content">
        本协议的订立、执行、解释及争议的解决均应适用中华人民共和国法律。因本协议引起的或与本协议有关的任何争议，双方应首先通过友好协商解决；协商不成的，任何一方均有权向本小程序运营者所在地有管辖权的人民法院提起诉讼。
      </p>
    </view>
    <view :style="{ fontSize: fontSizeMedium }" class="main-box" v-if="type == 1">
      <p class="title">欢迎使用“嘉有小乔”惠企服务隐私协议</p>
      <view class="date">
        <text>更新日期：2024/10/23</text>
        <text>生效日期：2024/10/23</text>
      </view>
      <p>重要提示：</p>
      <p class="content">
        在使用“嘉有小乔”惠企服务（以下简称“本小程序”）提供的服务之前，请您务必仔细阅读并理解本隐私协议。本协议阐述了我们对用户隐私信息的收集、使用、保护及管理的政策和原则。一旦您开始使用本小程序，即表示您已同意我们按照本协议收集、使用、保护和管理您的个人信息。
      </p>
      <p>一、信息收集</p>
      <p class="content">
        用户主动提供：当您注册、登录、使用本小程序的特定功能或参与我们组织的活动时，您可能需要提供个人信息，包括但不限于姓名、性别、年龄、联系方式、电子邮箱地址、微信账号等。
      </p>
      <p class="content">
        自动收集：为了提升用户体验和服务质量，本小程序可能会自动收集您的设备信息（如设备型号、操作系统版本、IP地址、MAC地址等）、访问记录（如浏览页面、停留时间、点击行为等）以及使用习惯等非个人身份信息。
      </p>
      <p>二、信息使用</p>
      <p class="content">
        提供服务：我们使用收集到的信息来为您提供更个性化、更便捷的服务体验，包括但不限于身份验证、消息推送、内容推荐等。
        统计分析：我们会对收集到的信息进行统计分析，以优化我们的产品和服务，提升用户体验。
        合法合规：在法律法规允许的范围内，我们可能会将您的信息共享给合作伙伴或第三方服务提供商，以完成服务提供或实现其他合法目的，但我们将确保该等共享行为符合相关法律法规的要求，并会采取合理措施保障信息安全。
      </p>
      <p>三、信息安全</p>
      <p class="content">
        加密存储：我们采用加密技术存储您的个人信息，以防止信息泄露和未经授权的访问。
        访问控制：我们严格控制对个人信息的访问权限，只有经过授权的人员才能访问您的个人信息。
        安全措施：我们实施了一系列安全措施，包括但不限于防火墙、入侵检测系统、数据加密等，以保障您的信息安全。
      </p>
      <p>四、用户权利</p>
      <p class="content">
        知情权：您有权了解我们收集、使用您的个人信息的情况。
        选择权：您有权选择是否向我们提供个人信息，以及在提供后随时更正或删除您的个人信息（但请注意，某些信息的删除可能会影响您正常使用本小程序的功能）。
        投诉与举报：如果您对我们的隐私政策或个人信息处理方式有任何疑问或不满，您可以通过本小程序内的客服渠道或我们的官方网站上的联系方式与我们联系，我们将尽快处理您的反馈。
      </p>
      <p>五、变更与通知</p>
      <p class="content">
        我们有权根据业务发展和法律法规的变化适时修订本协议。修订后的协议一经公布即生效，并在公布时通过本小程序内公告或其他适当方式通知您。请您定期查阅本隐私协议，以了解最新的内容。
      </p>
      <p>六、法律适用与争议解决</p>
      <p class="content">
        本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。因本协议引起的或与本协议有关的任何争议，双方应首先通过友好协商解决；协商不成的，任何一方均有权向本小程序运营者所在地有管辖权的人民法院提起诉讼。
      </p>
    </view>
  </view>
</template>

<script>
import navbar from '@/components/Navbar/index.vue'
export default {
  components: {
    navbar
  },
  data() {
    return {
      headtitle: '隐私政策',
      Props: {
        imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: '', //导航高度(动态获取传参)
        bgColor: '', //导航栏背景色,不传参则默认#9CF
        capsuleTop: '', //胶囊顶部距离(动态获取传参)
        textColor: '', //导航标题字体颜色(不传默认#FFF)
        iconColor: '', //icon图标颜色(不传默认#FFF)
        blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
        backText: '' //默认字体(返回)
      },
      type: 0
    }
  },
  onLoad(options) {
    this.Props.statusBarHeight = getApp().globalData.statusBarHeight
    this.Props.capsuleTop = getApp().globalData.capsuleTop
    this.type = options.type
    this.headtitle = options.type == 1 ? '隐私政策' : '用户协议'
  },
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall // 从Vuex获取小号字体大小
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium // 从Vuex获取中号字体大小
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge // 从Vuex获取大号字体大小
    }
  },
  methods: {
    handleToCare() {
      uni.showActionSheet({
        itemList: ['大号字体', '默认大小'],
        success: (res) => {
          if (res.tapIndex == 0) {
            this.$store.dispatch('updateLargeFontSize')
          }
          if (res.tapIndex == 1) {
            this.$store.dispatch('updateNormalFontSize')
          }
          uni.showToast({
            title: '设置成功',
            icon: 'none'
          })
        },
        fail: function (res) {}
      })
    },
    handleToEditPwd() {
      uni.navigateTo({
        url: '/pages/mine/editPwd'
      })
    },
    handleToEditInfo() {
      uni.navigateTo({
        url: '/pages/mine/editInfo'
      })
    },
    handleLogout() {
      this.$modal.confirm('确定注销并退出系统吗？').then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$tab.reLaunch('/pages/index')
        })
        global.tabBarCurrent = 0
      })
    }
  }
}
</script>
<style>
page {
  background-color: #f4f8fc;
}
</style>
<style lang="scss" scoped>
.main-box {
  padding: 60rpx 20rpx;
  width: 686rpx;
  margin: 60rpx auto;
  background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
  border-radius: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #444444;
  line-height: 48rpx;
  font-style: normal;
  .title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 36rpx;
    color: #444444;
    line-height: 50rpx;
    text-align: center;
    font-style: normal;
  }
  .date {
    display: flex;
    justify-content: space-around;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #acacac;
    line-height: 33rpx;
    text-align: left;
    font-style: normal;
    margin: 30rpx 0;
  }
  .content {
    text-indent: 2em;
  }
}
</style>
