<template>
  <view>
    <u-notice-bar
      bgColor="#ECEFFF"
      color="#000000"
      icon="volume"
      :text="text1"
      direction="column"
      @click="noticeClick"
    ></u-notice-bar>
    <view>
      <view class="search-box">
        <!--  @click="openPicker" -->
        <view class="search-input">
          <!-- <text v-if="!inputValueText">关键字搜索</text>
          <text v-else>{{ inputValueText }}</text> -->
          <input
            :style="{ fontSize: fontSizeLarge }"
            type="text"
            placeholder="关键字搜索"
            v-model="keyword"
          />
        </view>
        <view class="filter">
          <view @click="openPicker">筛选</view>
          <image class="filter_icon" src="@/static/images/map/screen.png" mode=""></image>
        </view>
      </view>
    </view>
    <view class="main-box">
      <view class="tab-box">
        <view
          v-for="(item, index) in tabList"
          :key="index"
          :class="tabIndex == index ? 'tab-item active' : 'tab-item'"
          @click="tabClick(index)"
        >
          {{ item.name }}
        </view>
      </view>
      <view class="btns">
        <view class="btn" hover-class="hover_class" @click="getPhones"></view>
        <!-- <view class="btn btn2" hover-class="hover_class" @click="go"> </view> -->
      </view>
      <view class="team-list">
        <view v-if="teamList.length > 0">
          <waves v-for="(item, index) in teamList" :key="index">
            <view class="list" @click="goDetail(item)">
              <image
                class="bg"
                src="@/static/images/assistant/card-bg.png"
                mode="aspectFill"
              ></image>
              <view class="avatar">
                <image :src="imgBaseUrl + '/boot/' + item.photoUrl" mode="aspectFill"></image>
              </view>
              <view class="info">
                <view class="name" :style="{ fontSize: fontSizeLarge }">{{
                  item.assistantName
                }}</view>
                <!-- <view class="type" :style="{ fontSize: fontSizeSmall }">
                  <text>
                    {{
                      item.isAllItem == 1 ? '全流程落地' : item.singleMore_dictText.split(',')[0]
                    }}
                  </text>
                </view> -->
                <view class="desc" :style="{ fontSize: fontSizeSmall }"
                  >服务位置:{{ item.serviceLocal }}</view
                >
              </view>
            </view>
          </waves>
          <uni-load-more iconType="circle" :status="loadMoreStatus" />
        </view>
        <u-empty
          :textSize="fontSizeSmall"
          iconSize="130"
          v-else
          mode="list"
          icon="http://cdn.uviewui.com/uview/empty/list.png"
        >
        </u-empty>
      </view>
    </view>

    <!-- <u-picker
      v-if="show2"
      :show="show"
      ref="uPicker"
      :columns="columns"
      @confirm="confirmHandler"
      @change="changeHandler"
      @cancel="cancel"
    ></u-picker> -->

    <uni-popup showClose ref="popup" background-color="#fff">
      <view class="popup-content">
        <view class="search-input">
          <input
            :style="{ fontSize: fontSizeLarge }"
            type="text"
            placeholder="关键字搜索"
            v-model="keyword2"
          />
        </view>

        <!-- 地区筛选 -->
        <view class="title">地区筛选</view>
        <view class="area">
          <!-- 省 -->
          <view class="box">
            <view
              :class="provinceIndex == index ? 'active' : ''"
              v-for="(item, index) in columns[0]"
              :key="index"
              @click="selectProvince(item, index)"
              >{{ item.name }}</view
            >
          </view>
          <!-- 市-->
          <view class="box" v-if="columns[1].length > 0">
            <view
              :class="cityIndex == index ? 'active' : ''"
              v-for="(item, index) in columns[1]"
              :key="index"
              @click="selectCity(item, index)"
              >{{ item.name }}</view
            >
          </view>
          <!-- 区 -->
          <view class="box" v-if="columns[2].length > 0">
            <view
              :class="areaIndex == index ? 'active' : ''"
              v-for="(item, index) in columns[2]"
              :key="index"
              @click="selectArea(item, index)"
              >{{ item.name }}</view
            >
          </view>
        </view>

        <view class="btns">
          <view class="b1" @click="closePopup">重置</view>
          <view class="b2" @click="queryPopup">确定</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import {
  getItemList,
  getAssistantByItem,
  administrativeDivisionRootList,
  getFrequentItem,
  getPageAssistant,
  getAssistantByPoint
} from '@/api/assistant/index.js'
import config from '@/config'
// import navbar from '@/components/Navbar/index.vue'
import global from '@/utils/global.js'
import TabBar from '@/components/TabBar/index.vue'
import { debounce } from 'lodash'
import waves from '@/components/waves/waves.vue'
import { newSetToken, removeToken } from '@/utils/auth'

// 登录导入
import { checkToken, loginByPasswd } from '@/api/login'
export default {
  components: {
    TabBar,
    waves
  },
  data() {
    return {
      tabBerLists: [],
      headtitle: '小乔帮您办',
      loginType: 1,
      searchValue: '',
      teamList: [],
      typeList: [],
      loadMoreStatus: 'more',
      params: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      currentType: 0,
      imgBaseUrl: config.baseUrl,
      query: {
        businessType: 1,
        keyword: '',
        // divisionLevel: 1
        // 区域 id
        // divisionId: '',
        // itemType: '0'
      },
      keyword: '',
      keyword2: '',
      // phones: 'tel:***********',
      phones: '',
      loginForm: {
        username: '***********',
        password: 'Hwj@***********',
        captcha: '',
        checkKey: ''
      },
      text1: [],
      tabList: [{ name: '政务服务' }, { name: '生活服务' }],
      tabIndex: 0,
      show: false, // 控制 picker 显示与隐藏
      columns: [[], [], []], // 省、市、县的数据列
      fullData: [], // 存储所有省市县数据的完整列表
      show2: false,
      inputValueText: '',
      userInfo: {},
      pageNo: 1,
      pageSize: 10,
      total: 0,
      provinceIndex: null,
      cityIndex: null,
      areaIndex: null
    }
  },
  watch: {
    keyword: {
      handler: debounce(function () {
        this.query['keyword'] = this.keyword
        this.pageNo = 1
        this.teamList = []
        this.loadMoreStatus = 'more'
        this.getTeamList()
      }, 300),
      immediate: false
    }
  },

  async onLoad() {
    // 用户信息
    this.userInfo =
      typeof this.$store.state.user.userInfo == 'string'
        ? JSON.parse(this.$store.state.user.userInfo)
        : this.$store.state.user.userInfo
    this.loginType = uni.getStorageSync('loginType')
    this.pageNo = 1
    this.teamList = []
    this.getTeamList()
    this.tabBerLists = uni.getStorageSync('tabBarList')

    // this.getQueryVariable('code')
  },
  async onShow() {
    uni.getLocation({
      // type: 'wgs84',
      success: async (res) => {
        const res2 = await getAssistantByPoint({
          lng: res.longitude,
          lat: res.latitude
        })
        this.phones = `tel:${res2.result.assistantTel}`
        console.log('this.phones', this.phones)
      }
    })

    this.fetchAllRegions()
    const res = await getFrequentItem()
    res.result.map((item) => {
      this.text1.push(item.itemName)
    })

    let code = this.getCleanedCode()
    if (code) {
      uni.request({
        url: config.baseUrl + '/boot/miniapp/auth/loginByCode',
        method: 'POST',
        data: {
          code: code
        },
        success: (res) => {
          this.wxLoginSuccess(res.data.result.token)
        },
        fail: (err) => {
          console.error(err)
        }
      })
    }
  },
  computed() {},
  methods: {
    queryPopup() {
      this.keyword = this.keyword2
      this.query['keyword'] = this.keyword
      this.pageNo = 1
      this.teamList = []
      this.loadMoreStatus = 'more'
      this.getTeamList()
      this.$refs.popup.close()
    },
    closePopup() {
      this.keyword2 = ''
      this.provinceIndex = null
      this.cityIndex = null
      this.areaIndex = null
      this.$set(this.columns, 1, [])
      this.$set(this.columns, 2, [])
      this.query['keyword'] = ''
      // this.query.divisionLevel = 1
      // divisionId
      delete this.query.divisionId
      delete this.query.divisionLevel
      this.pageNo = 1
      this.teamList = []
      this.loadMoreStatus = 'more'
      this.getTeamList()

      this.$refs.popup.close()
    },
    // 触底
    onReachBottom() {
      if (this.loadMoreStatus !== 'more') return
      this.loadMoreStatus = 'loading'
      this.pageNo++
      this.getTeamList()
    },
    go() {
      if (this.userInfo.mobilePhone) {
        this.$tab.navigateTo('/pages/mine/handlingRecord?type=0')
      } else {
        uni.showToast({
          title: '请先完成实名认证',
          icon: 'none'
        })
        removeToken()
        window.location.reload()
      }
    },
    getPhones() {
      if (this.userInfo.mobilePhone) {
        if (!this.phones) {
          this.phones = `tel:${this.teamList[0].assistantTel}`
        }
        const a = document.createElement('a')
        a.setAttribute('href', this.phones)
        a.click()
      } else {
        uni.showToast({
          title: '请先完成实名认证',
          icon: 'none'
        })
      }
    },
    getQueryVariable(variable) {
      let query = ''
      // 检查传统查询参数 (window.location.search) 是否存在
      if (window.location.search) {
        query = window.location.search.substring(1)
      }
      // 检查是否有 hash 模式中的查询参数
      else if (window.location.hash.includes('?')) {
        query = window.location.hash.split('?')[1]
      }
      if (!query) {
        // alert('没有找到查询字符串')
        this.keyword = '没有找到查询字符串'
        return false
      }
      const vars = query.split('&')
      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=')
        if (pair[0] === variable) {
          // alert('code: ' + pair[1])
          this.keyword = 'code: ' + pair[1]
          return pair[1]
        }
      }
      // alert('没有找到 ' + variable)
      this.keyword = '没有找到 ' + variable
      return false
    },
    // 通知点击
    noticeClick() {
      // 跳转人员详情 模拟
      let item = this.teamList[0]
      uni.navigateTo({
        url: `/pages/assistant/team-info?record=${JSON.stringify(item)}&tabType=${this.tabIndex}`
      })
    },
    tabClick(index) {
      this.tabIndex = index
      // this.query.itemType = index
      this.query.businessType = index == 0 ? 1 : 2
      this.pageNo = 1
      this.teamList = []
      this.getTeamList()
    },
    cancel() {
      this.show = false
    },
    openPicker() {
      this.$refs.popup.open('top')
    },
    // 省点击
    selectProvince(item, index) {
      this.provinceIndex = index
      this.$set(this.columns, 1, [])
      if (item.name === '全部') {
        this.$set(this.columns, 2, [])
        this.query.divisionLevel = 1
        delete this.query.divisionId
      } else {
        this.query.divisionId = item.id
        delete this.query.divisionLevel
      }
      this.columns[1].push(
        {
          name: '全部',
          id: null
        },
        ...item.children
      )
    },
    // 市点击
    selectCity(item, index) {
      this.cityIndex = index
      this.$set(this.columns, 2, [])
      if (item.name === '全部') {
        this.query.divisionLevel = 2
        delete this.query.divisionId
      } else {
        this.query.divisionId = item.id
        delete this.query.divisionLevel
      }
      this.columns[2].push(
        {
          name: '全部',
          id: null
        },
        ...item.children
      )
    },
    // 区点击
    selectArea(item, index) {
      this.areaIndex = index
      if (item.name === '全部') {
        this.query.divisionLevel = 3
        delete this.query.divisionId
      } else {
        this.query.divisionId = item.id
        delete this.query.divisionLevel
      }
    },
    async fetchAllRegions() {
      const res = await administrativeDivisionRootList({ pid: 0 })
      // 省 数据
      this.$set(this.columns, 0, [])
      this.columns[0].push(
        {
          name: '全部',
          id: null
        },
        res.result[0]
      )
      // // 市 数据
      // this.columns[1].push(
      //   {
      //     name: '全部',
      //     id: null
      //   },
      //   ...res.result[0].children
      // )
      // if (res && res.result) {
      //   this.fullData = [
      //     {
      //       id: null,
      //       name: '全部',
      //       children: [],
      //       administrative_level: '省'
      //     },
      //     ...res.result
      //   ]
      //   // Initialize with only the first-level (province) data
      //   this.setInitialColumns()
      // }
    },
    setInitialColumns() {
      // Initialize only the first level (province data)
      this.columns[0] = this.fullData.map((province) => province.name)
      this.columns[1] = [] // Hide the city data initially
      this.columns[2] = [] // Hide the county data initially

      console.log('Data initialized', this.columns)
      this.show2 = true
    },
    changeHandler({ columnIndex, index }) {
      const picker = this.$refs.uPicker

      if (columnIndex === 0) {
        // When selecting a province
        if (index === 0) {
          this.columns[1] = []
          this.columns[2] = []
          picker.setColumnValues(1, this.columns[1])
          picker.setColumnValues(2, this.columns[2])
        } else {
          const selectedProvince = this.fullData[index]
          // Load the cities only after selecting a province
          this.columns[1] = selectedProvince.children
            ? ['全部', ...selectedProvince.children.map((city) => city.name)]
            : ['全部']
          this.columns[2] = [] // Clear counties when switching provinces
          picker.setColumnValues(1, this.columns[1])
          picker.setColumnValues(2, this.columns[2])
        }
      } else if (columnIndex === 1) {
        // When selecting a city
        const selectedProvinceValue = picker.getValues()[0]
        const selectedProvince = this.fullData.find(
          (province) => province.name === selectedProvinceValue
        )

        if (index === 0) {
          this.columns[2] = []
          picker.setColumnValues(2, this.columns[2])
        } else if (selectedProvince) {
          const selectedCity = selectedProvince.children[index - 1]
          // Load counties only after selecting a city
          this.columns[2] =
            selectedCity && selectedCity.children
              ? ['全部', ...selectedCity.children.map((county) => county.name)]
              : ['全部']
          picker.setColumnValues(2, this.columns[2])
        }
      }
    },
    confirmHandler(e) {
      // 获取选中的名称
      const selectedProvince = e.value[0]
      const selectedCity = e.value[1]
      const selectedCounty = e.value[2]

      let provinceInfo = null
      let cityInfo = null
      let countyInfo = null

      // 处理省级选择
      if (selectedProvince === '全部') {
        provinceInfo = {
          id: null,
          name: '全部',
          administrative_level: '省'
        }
      } else {
        // 查找选中的省份
        const province = this.fullData.find((p) => p.name === selectedProvince)
        if (province) {
          provinceInfo = {
            id: province.id,
            name: province.name,
            administrative_level: province.administrative_level || '省'
          }

          // 处理市级选择
          if (selectedCity === '全部') {
            cityInfo = {
              id: null,
              name: '全部',
              administrative_level: '市'
            }
          } else {
            const city = province.children?.find((c) => c.name === selectedCity)
            if (city) {
              cityInfo = {
                id: city.id,
                name: city.name,
                administrative_level: city.administrative_level || '市'
              }

              // 处理县级选择
              if (selectedCounty === '全部') {
                countyInfo = {
                  id: null,
                  name: '全部',
                  administrative_level: '县'
                }
              } else {
                const county = city.children?.find((c) => c.name === selectedCounty)
                if (county) {
                  countyInfo = {
                    id: county.id,
                    name: county.name,
                    administrative_level: county.administrative_level || '县'
                  }
                }
              }
            }
          }
        }
      }

      console.log('Selected Province:', provinceInfo)
      console.log('Selected City:', cityInfo)
      console.log('Selected County:', countyInfo)

      // 更新显示文本
      this.inputValueText = `${provinceInfo?.name || ''}/${cityInfo?.name || ''}/${
        countyInfo?.name || ''
      }`

      // // 更新 divisionId
      // const provinceId = provinceInfo?.id || ''
      // const cityId = cityInfo?.id || ''
      // const countyId = countyInfo?.id || ''

      // this.query.divisionId = `${provinceId}${cityId ? ',' + cityId : ''}${
      //   countyId ? ',' + countyId : ''
      // }`
      // Update divisionId to only use the last selected ID
      const provinceId = provinceInfo?.id || ''
      const cityId = cityInfo?.id || ''
      const countyId = countyInfo?.id || ''

      // Only use the ID from the lowest selected level
      this.query.divisionId = countyId || cityId || provinceId

      // name === 全部的时候  this.query.divisionLevel =
      // 省 1 市 2 区3
      if (selectedProvince === '全部') {
        this.query.divisionLevel = 1
      } else if (selectedCity === '全部') {
        this.query.divisionLevel = 2
      } else if (selectedCounty === '全部') {
        this.query.divisionLevel = 3
      } else {
        this.query.divisionLevel = 3
      }
      console.log(this.query.divisionId, 'this.divisionId')

      console.log('selectedCity---------', selectedCity)

      if (selectedProvince === '全部' || selectedCity === '全部' || selectedCounty === '全部') {
        delete this.query.divisionId
      }
      this.pageNo = 1
      this.teamList = []
      this.getTeamList()
      this.show = false
    },
    goDetail(item) {
      uni.navigateTo({
        url: `/pages/assistant/team-info?record=${JSON.stringify(item)}&tabType=${this.tabIndex}`
      })
    },
    // 获取团队成员
    async getTeamList() {
      this.query.pageNo = this.pageNo
      this.query.pageSize = this.pageSize
      const res = await getPageAssistant(this.query)
      if (res.result.records.length < this.pageSize) {
        this.loadMoreStatus = 'noMore'
      } else {
        this.loadMoreStatus = 'more'
      }
      // this.total = res.result.total
      this.teamList = this.teamList.concat(res.result.records)
      let index = Math.floor(Math.random() * 10)
      // this.phones = `tel:${this.teamList[index].assistantTel}`
    },
    changeType(index, id) {
      this.currentType = index
      this.teamList = []
      if (index === 1) {
        this.query['isAll'] = 1
      } else if (index === 0) {
        delete this.query['isAll']
        delete this.query['itemId']
      } else {
        {
          delete this.query['isAll']
          this.query['itemId'] = id
        }
      }
      this.getTeamList()
    },
    search(val) {
      this.query['keyword'] = val
      this.pageNo = 1
      this.teamList = []
      this.getTeamList()
    },
    clear() {
      delete this.query['keyword']
      this.getTeamList()
    },
    getCleanedCode() {
      let query = ''
      // 获取传统模式的查询参数（?code=xxx&openId=xxx）
      if (window.location.search) {
        query = window.location.search.substring(1)
      }
      // 获取 hash 模式的查询参数（#/path?code=xxx&openId=xxx）
      else if (window.location.hash.includes('?')) {
        query = window.location.hash.split('?')[1]
      }
      // 如果没有找到 query，返回 false
      if (!query) {
        return false
      }
      // 解析查询参数
      const vars = query.split('&')
      let code = ''

      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=')
        const key = decodeURIComponent(pair[0])
        const value = decodeURIComponent(pair[1])

        if (key === 'code') {
          code = value
          break // 找到后可以提前结束循环
        }
      }
      // 去除 code 中的空格和特殊字符
      if (code) {
        // 去除空格
        // let cleanedCode = code.replace(/\s*/g, '')
        // 处理特殊字符
        // cleanedCode = encodeURIComponent(cleanedCode)

        let cleanedCode = code
        return cleanedCode
      }

      return false
    },
    // 认证登录成功
    async wxLoginSuccess(token) {
      newSetToken(token)
      const res = await checkToken(token)
      const avatar = res.result.userInfo.avatar
      const nickname = res.result.userInfo.nickName
      this.$store.commit('SET_NAME', nickname)
      this.$store.commit('SET_AVATAR', avatar)
      this.$store.commit('SET_USERINFO', res.result.userInfo)
      this.$store.commit('setRoleId', 1)
      uni.setStorageSync('loginType', 1)
    }
  },
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall // 从Vuex获取小号字体大小
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium // 从Vuex获取中号字体大小
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge // 从Vuex获取大号字体大小
    }
  }
}
</script>
<style>
page {
  background-color: #f4f8fc;
}
</style>
<style lang="scss" scoped>
.tab-box {
  display: flex;
  justify-content: space-between;
  padding: 30rpx 60px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 36rpx;
  color: #666666;
  .active {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 36rpx;
    color: #057ffe;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      bottom: -5rpx;
      left: 0;
      width: 100%;
      height: 4rpx;
      background: #057ffe;
    }
  }
}
.main-box {
  min-height: 100vh;
  display: flex;
  justify-content: start;
  flex-direction: column;

  .btns {
    margin: 10rpx 0;
    display: flex;
    justify-content: space-around;
    .btn {
      width: 660rpx;
      height: 150rpx;
      background: url('/static/images/assistant/yjhj.png') no-repeat;
      background-size: 100% 100%;
      // border-radius: 8rpx;
      // line-height: 114rpx;
      // text-align: center;
      // font-family: PingFangSC, PingFang SC;
      // font-weight: 500;
      // font-size: 26rpx;
      // color: #ffffff;
    }
    .btn2 {
      background: url('/static/images/assistant/bbjl.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .team-list {
    width: 100%;
    background: #fff;
    .list {
      width: 100%;
      height: 180rpx;
      margin: 0 auto;
      position: relative;
      display: flex;
      align-items: center;
      padding: 0 20rpx;
      border-bottom: 2rpx solid #e7f0ff;
      .bg {
        position: absolute;
        width: 460rpx;
        height: 100%;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        // z-index: -1;
      }
      .avatar {
        width: 122rpx;
        height: 122rpx;
        image {
          width: 100%;
          height: 100%;
          border-radius: 16rpx;
        }
      }
      .info {
        z-index: 1;
        flex: 1;
        padding-left: 20rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .name {
          font-size: 32rpx;
          font-weight: bold;
        }
        .type {
          width: 130rpx;
          height: 36rpx;
          line-height: 36rpx;
          text-align: center;
          background: #fff9f0;
          border-radius: 8rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 20rpx;
          color: #f3ab6e;
          margin: 10rpx 0;
        }
        .desc {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 22rpx;
          color: #666666;
          margin-top: 20rpx;
        }
      }
    }
  }
  a {
    color: #fff;
    text-decoration: none;
  }
}
.search-box {
  padding: 20rpx;
  background: #ffffff;
  margin-bottom: 20rpx;
  position: relative;
  padding-bottom: 70rpx;
  .search-input {
    position: relative;
    height: 60rpx;
    line-height: 60rpx;
    background: #ffffff;
    border-radius: 30rpx;
    border: 2rpx solid #dddddd;
    padding-left: 30rpx;
    // margin-bottom: 30rpx;

    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 24rpx;
    color: #acacac;
    input {
      height: 60rpx;
    }
    image {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 32rpx;
      height: 32rpx;
    }
  }
  .filter {
    position: absolute;
    bottom: 10rpx;
    right: 10rpx;
    display: flex;
    align-items: center;
    .filter_icon {
      width: 22rpx;
      height: 22rpx;
      margin-right: 10rpx;
    }
  }
}
.hover_class {
  opacity: 0.8 !important;
}
.popup-content {
  padding: 30px 20rpx;
  // min-height: 200rpx;
  .search-input {
    position: relative;
    height: 60rpx;
    line-height: 60rpx;
    background: #ffffff;
    border-radius: 30rpx;
    border: 2rpx solid #dddddd;
    padding-left: 30rpx;
    margin-bottom: 30rpx;

    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 24rpx;
    color: #acacac;
    input {
      height: 60rpx;
    }
    image {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 32rpx;
      height: 32rpx;
    }
  }
  .btns {
    width: 90%;
    margin: 60rpx auto 0 auto;
    display: flex;
    justify-content: space-between;
    font-size: 36rpx;
    font-weight: 600;
    .b1 {
      color: #999;
    }
    .b2 {
      color: #057ffe;
    }
  }
  .title {
    padding: 10rpx 30rpx;
    font-size: 33rpx;
    font-weight: 600;
  }
  .area {
    display: flex;
    .box {
      max-width: 300rpx;
      height: 500rpx;
      overflow: auto;
      flex: 1;
      // margin: 20rpx;
      padding: 20rpx;
      border-right: 2rpx solid #f5f5f5;
      & > view {
        padding: 5rpx 0rpx;
        text-align: center;
        background: #f5f5f5;
        border-radius: 10rpx;
        margin-bottom: 25rpx;
        font-size: 33rpx;
      }
      .active {
        background: #057ffe;
        color: #fff;
      }
      &:last-child {
        border-right: none;
      }
    }
  }
}
</style>
