<template>
  <view>
    <!-- <navbar :Props="Props" :title='headtitle' @black="black"></navbar> -->
    <view class="box">
      <image class="background" src="@/static/images/oneTime/background.png" mode=""></image>
      <view class="header">
        <view class="title">
          <!-- 全流程落地 -->
          {{ newsList.itemName }}
        </view>
        <view class="left_box">
          {{ newsList.isFree_dictText }}
        </view>
        <view class="right_box"> {{ newsList.workDay }}个工作日 </view>
        <image class="method" src="@/static/images/oneTime/method.png" mode=""></image>
      </view>
      <view class="whodo_box">
        <image class="whodo_bg" src="@/static/images/oneTime/whodo.png" mode=""></image>
        <image class="whodo_header" src="@/static/images/oneTime/whodo_header.png" mode=""></image>
        <scroll-view class="text" scroll-y="true" style="height: 480rpx">
          <rich-text :nodes="newsList.whoDo"></rich-text>
        </scroll-view>
        <view class="title">谁可以办</view>
      </view>
      <view class="what_box">
        <image class="what_bg" src="@/static/images/oneTime/whatdo.png" mode=""></image>
        <image class="what_header" src="@/static/images/oneTime/whodo_header.png" mode=""></image>
        <view class="title">怎么办</view>
        <view class="content">
          <view v-if="wsb" class="content_item" @click="wsbDialog()">
            <image class="icon" src="@/static/images/oneTime/wsb.png" mode=""></image>
            <view class="text">网上办</view>
          </view>
          <view v-if="dtb" class="content_item" @click="dtbGo()">
            <image class="icon" src="@/static/images/oneTime/dtb.png" mode=""></image>
            <view class="text">大厅办</view>
          </view>
          <view v-if="zzb" class="content_item" @click="zzbGo()">
            <image class="icon" src="@/static/images/oneTime/zzb.png" mode=""></image>
            <view class="text">自助办</view>
          </view>
          <view v-if="xqbnb" class="content_item">
            <image class="icon" src="@/static/images/oneTime/xqbnb.png" mode=""></image>
            <view class="text">小乔帮您办</view>
          </view>
        </view>
      </view>
      <view class="data">
        <image class="what_bg" src="@/static/images/oneTime/data.png" mode=""></image>
        <image class="what_header" src="@/static/images/oneTime/whodo_header.png" mode=""></image>
        <view class="title">要什么材料</view>
        <view class="need">来看看您都需要准备哪些材料</view>
        <view class="query" @click="inquiry">立即查询</view>
      </view>
      <view class="result">
        <image class="what_bg" src="@/static/images/oneTime/result.png" mode=""></image>
        <image class="what_header" src="@/static/images/oneTime/whodo_header.png" mode=""></image>
        <view class="title">办理结果</view>
        <view class="boxs">
          <scroll-view scroll-y="true" style="height: 1080rpx">
            <view class="items" v-for="(report, index) in newsList.handleResult" :key="index">
              <view class="content">
                <view class="text">{{ report.name }}</view>
                <image class="image" :src="report.path" mode=""></image>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
      <view class="replenish">
        <image class="whodo_bg" src="@/static/images/oneTime/whodo.png" mode=""></image>
        <image class="whodo_header" src="@/static/images/oneTime/whodo_header.png" mode=""></image>
        <scroll-view class="text" scroll-y="true" style="height: 480rpx">
          <rich-text :nodes="newsList.supplement"></rich-text>
        </scroll-view>
        <view class="title">补充说明</view>
      </view>
      <view class="ask">
        <image class="what_bg" src="@/static/images/oneTime/ask.png" mode=""></image>
        <image class="what_header" src="@/static/images/oneTime/whodo_header.png" mode=""></image>
        <view class="title">大家都在问</view>
        <scroll-view class="boxs" scroll-y="true" style="height: 700rpx">
          <view class="xq_reports" v-for="(item, index) in newsList.questions" :key="index">
            <view class="content" @click="detail(item.answer)">
              <view class="text">{{ item.question }}</view>
              <image class="back" src="@/static/images/home/<USER>" mode=""></image>
            </view>
            <view v-show="index != length" class="line"></view>
          </view>
        </scroll-view>
      </view>
      <uni-popup class="popup" ref="pop" type="center" mask-background-color="rgba(0,0,0,0.1)">
        <view class="popup-content">
          <view class="substance">
            <scroll-view scroll-y="true" style="height: 900rpx">
              <view class="title">补充说明</view>
              <view class="boxs">
                <view class="items" v-for="(report, index) in newsList.downList" :key="index">
                  <view class="content">
                    <view class="text">{{ report.name }}</view>
                    <image class="image" :src="report.path" mode=""></image>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          <view class="query" @click="know">我知道了</view>
        </view>
      </uni-popup>
      <uni-popup
        class="answerBoxs"
        ref="answer"
        type="center"
        mask-background-color="rgba(0,0,0,0.1)"
      >
        <view class="answer-content">
          <scroll-view class="substance" scroll-y="true" style="height: 300rpx">
            {{ answer }}
          </scroll-view>
          <view class="query" @click="aware">我知道了</view>
        </view>
      </uni-popup>
      <uni-popup class="answerBoxs" ref="wsb" type="center" mask-background-color="rgba(0,0,0,0.1)">
        <view class="answer-content">
          <scroll-view class="substance" scroll-y="true" style="height: 300rpx">
            {{ site }}
          </scroll-view>
          <view class="query" @click="iknow">我知道了</view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<script>
import config from '@/config'
import navbar from '@/components/Navbar/index.vue'
import { getOneTimeNotificationDetail } from '@/api/handled/index.js'
export default {
  components: {
    navbar
  },
  data() {
    return {
      newsList: {},
      headtitle: '一次性告知',
      itemId: '',
      Props: {
        imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: '', //导航高度(动态获取传参)
        bgColor: '', //导航栏背景色,不传参则默认#9CF
        capsuleTop: '', //胶囊顶部距离(动态获取传参)
        textColor: '', //导航标题字体颜色(不传默认#FFF)
        iconColor: '', //icon图标颜色(不传默认#FFF)
        blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
        backText: '' //默认字体(返回)
      },
      reportList: [
        {
          text: '国有建设用地使用权成交确认书流程'
        },
        {
          text: '国有建设用地使用权出让合同（土地出让合同）'
        },
        {
          text: '国有建设用地使用权首次登记（土地证）'
        },
        {
          text: '企业投资项目备案'
        },
        {
          text: '企业投资项目备案'
        }
      ],
      dtb: false,
      zzb: false,
      wsb: false,
      xqbnb: false,
      answer: '',
      length: 4,
      site: ''
    }
  },
  onLoad: function (data) {
    const that = this
    console.log(data, '2222')
    that.itemId = data.item
    that.Props.statusBarHeight = getApp().globalData.statusBarHeight
    that.Props.capsuleTop = getApp().globalData.capsuleTop
  },
  mounted() {
    this.getList()
  },
  methods: {
    dtbGo() {
      uni.navigateTo({
        url: '/pages/map/index?nowProvince=' + 0
      })
    },
    zzbGo() {
      uni.navigateTo({
        url: '/pages/map/index?nowProvince=' + 1
      })
    },
    wsbDialog() {
      this.site = this.newsList.netHandle
      this.$refs.wsb.open()
    },
    iknow() {
      this.$refs.wsb.close()
    },
    detail(answer) {
      this.answer = answer
      this.$refs.answer.open()
    },
    know() {
      this.$refs.pop.close()
    },
    aware() {
      this.$refs.answer.close()
    },
    inquiry() {
      // this.$refs.pop.open();

      uni.navigateTo({
        url: '/pages/assistant/oneTimeDetails?itemId=' + this.itemId
      })
    },
    async getList() {
      const that = this
      const res = await getOneTimeNotificationDetail({
        itemId: that.itemId
      })
      const staticDomainURL = config.staticDomainURL
      console.log(res, '09999')
      that.newsList = res?.result
      // this.params.total = res?.result?.total
      if (that.newsList.whatDoName.includes('大厅办')) {
        that.dtb = true
      }
      if (that.newsList.whatDoName.includes('自助办')) {
        that.zzb = true
      }
      if (that.newsList.whatDoName.includes('网上办')) {
        that.wsb = true
      }
      if (that.newsList.whatDoName.includes('小乔帮办')) {
        that.xqbnb = true
      }
      if (that.newsList.handleResult.includes(',')) {
        that.newsList.handleResult = that.newsList.handleResult.split(',')
      } else {
        that.newsList.handleResult = [that.newsList.handleResult]
      }
      if (that.newsList.materialAttachment.includes(',')) {
        that.newsList.materialAttachment = that.newsList.materialAttachment.split(',')
      } else {
        that.newsList.materialAttachment = [that.newsList.materialAttachment]
      }
      console.log(that.newsList.materialAttachment, 'materialAttachment-----------')
      that.length = that.newsList.questions.length - 1

      that.newsList.downList = Object.values(that.newsList.materialAttachment).map((path) => {
        console.log(path, '22222')
        // 获取文件名的起始位置（即最后一个斜杠之后）
        let startIndex = path.lastIndexOf('/') + 1

        // 获取文件名的结束位置（即最后一个下划线之前）
        let endIndex = path.lastIndexOf('_')

        // 获取文件名
        let fileName = path.substring(startIndex, endIndex)

        // 获取文件扩展名
        let fileExtension = path.substring(path.lastIndexOf('.'))

        // 拼接文件名和扩展名
        let newFileName = fileName + fileExtension
        return {
          name: newFileName,
          path: staticDomainURL + path
        }
      })

      console.log(that.newsList.downList, '-1---------------')
      that.newsList.handleResult = Object.values(that.newsList.handleResult).map((path) => {
        const fileName = path.split('/').pop() // 获取文件名
        const name = fileName.split('_')[0] // 提取名称
        return {
          name: name,
          path: staticDomainURL + path
        }
      })
      console.log(that.newsList.handleResult, '0000')
    }
  },
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall // 从Vuex获取小号字体大小
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium // 从Vuex获取中号字体大小
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge // 从Vuex获取大号字体大小
    }
  }
}
</script>

<style lang="scss">
.box {
  position: relative;

  .background {
    width: 750rpx;
    height: 5121rpx;
  }

  .header {
    position: absolute;
    top: 0;
    width: 750rpx;
    height: 488rpx;

    .title {
      font-weight: 600;
      font-size: 60rpx;
      color: #0274ef;
      line-height: 70rpx;
      text-align: center;
      font-style: normal;
      margin-top: 62rpx;
    }

    .left_box {
      width: 218rpx;
      height: 59rpx;
      background: #ffffff;
      border-radius: 29rpx;
      opacity: 0.8;
      font-weight: normal;
      font-size: 30rpx;
      color: #0089ff;
      line-height: 59rpx;
      text-align: center;
      font-style: normal;
      position: absolute;
      left: 137rpx;
      top: 243rpx;
    }

    .right_box {
      width: 218rpx;
      height: 59rpx;
      background: #ffffff;
      border-radius: 29rpx;
      opacity: 0.8;

      font-weight: normal;
      font-size: 30rpx;
      color: #0089ff;
      line-height: 59rpx;
      text-align: center;
      font-style: normal;
      position: absolute;
      right: 137rpx;
      top: 243rpx;
    }

    .method {
      width: 504rpx;
      height: 54rpx;
      position: absolute;
      right: 122rpx;
      top: 374rpx;
    }
  }

  .whodo_box {
    position: absolute;
    width: 694rpx;
    height: 633rpx;
    top: 488rpx;
    left: 28rpx;

    .whodo_bg {
      width: 694rpx;
      height: 633rpx;
    }

    .whodo_header {
      width: 410rpx;
      height: 105rpx;
      position: absolute;
      top: 0;
      left: 147rpx;
    }

    .text {
      width: 606rpx;
      height: 480rpx;
      line-height: 48rpx;
      font-weight: 400;
      font-size: 30rpx;
      color: #444444;
      text-align: left;
      font-style: normal;
      position: absolute;
      top: 111rpx;
      left: 44rpx;
    }

    .title {
      font-weight: 600;
      font-size: 41rpx;
      color: #ffffff;
      line-height: 152rpx;
      text-align: left;
      font-style: normal;
      position: absolute;
      top: -26rpx;
      left: 265rpx;
    }
  }

  .what_box {
    width: 694rpx;
    height: 365rpx;
    position: absolute;
    top: 1182rpx;
    left: 28rpx;

    .content {
      display: flex;
      width: 694rpx;
      position: absolute;
      top: 144rpx;
      left: 0rpx;
      padding: 0 25rpx;

      .content_item {
        width: 25%;
        /* 四等分父容器的空间 */
        // border: 1px solid #000;
        /* 为了清晰地显示每个子元素的边界 */
        // padding: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .icon {
          width: 95rpx;
          height: 95rpx;
        }

        .text {
          font-weight: normal;
          font-size: 30rpx;
          color: #444444;
          line-height: 56rpx;
          text-align: left;
          font-style: normal;
        }
      }
    }

    .what_bg {
      width: 694rpx;
      height: 365rpx;
    }

    .what_header {
      width: 410rpx;
      height: 105rpx;
      position: absolute;
      top: 0;
      left: 147rpx;
    }

    .title {
      font-weight: 600;
      font-size: 41rpx;
      color: #ffffff;
      line-height: 152rpx;
      text-align: left;
      font-style: normal;
      position: absolute;
      top: -26rpx;
      left: 286rpx;
    }
  }

  .data {
    width: 694rpx;
    height: 395rpx;
    position: absolute;
    top: 1608rpx;
    left: 28rpx;

    .what_bg {
      width: 694rpx;
      height: 395rpx;
    }

    .what_header {
      width: 410rpx;
      height: 105rpx;
      position: absolute;
      top: 0;
      left: 147rpx;
    }

    .title {
      font-weight: 600;
      font-size: 41rpx;
      color: #ffffff;
      line-height: 152rpx;
      text-align: left;
      font-style: normal;
      position: absolute;
      top: -26rpx;
      left: 245rpx;
    }

    .need {
      font-weight: 400;
      font-size: 36rpx;
      color: #444444;
      line-height: 50rpx;
      text-align: left;
      font-style: normal;
      position: absolute;
      top: 143rpx;
      left: 113rpx;
    }

    .query {
      width: 530rpx;
      height: 86rpx;
      background: #137aff;
      border-radius: 50rpx;
      font-weight: 400;
      font-size: 38rpx;
      color: #ffffff;
      line-height: 86rpx;
      text-align: center;
      font-style: normal;
      position: absolute;
      left: 82rpx;
      bottom: 54rpx;
    }
  }

  .result {
    width: 694rpx;
    height: 1275rpx;
    position: absolute;
    top: 2064rpx;
    left: 28rpx;

    .what_bg {
      width: 694rpx;
      height: 1275rpx;
    }

    .what_header {
      width: 410rpx;
      height: 105rpx;
      position: absolute;
      top: 0;
      left: 147rpx;
    }

    .title {
      font-weight: 600;
      font-size: 41rpx;
      color: #ffffff;
      line-height: 152rpx;
      text-align: left;
      font-style: normal;
      position: absolute;
      top: -26rpx;
      left: 265rpx;
    }

    .boxs {
      position: absolute;
      top: 135rpx;
      left: 55rpx;

      .items {
        margin-bottom: 40rpx;

        .content {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          padding-right: 20rpx;

          .image {
            width: 560rpx;
            // height: 238rpx;
          }

          .text {
            font-weight: 400;
            font-size: 38rpx;
            color: #444444;
            line-height: 53rpx;
            font-style: normal;
            margin-bottom: 18rpx;
          }
        }
      }
    }
  }

  .replenish {
    position: absolute;
    width: 694rpx;
    height: 633rpx;
    top: 3400rpx;
    left: 28rpx;

    .whodo_bg {
      width: 694rpx;
      height: 633rpx;
    }

    .whodo_header {
      width: 410rpx;
      height: 105rpx;
      position: absolute;
      top: 0;
      left: 147rpx;
    }

    .text {
      width: 606rpx;
      height: 480rpx;
      line-height: 48rpx;
      font-weight: 400;
      font-size: 30rpx;
      color: #444444;
      text-align: left;
      font-style: normal;
      position: absolute;
      top: 111rpx;
      left: 44rpx;
    }

    .title {
      font-weight: 600;
      font-size: 41rpx;
      color: #ffffff;
      line-height: 152rpx;
      text-align: left;
      font-style: normal;
      position: absolute;
      top: -26rpx;
      left: 265rpx;
    }
  }

  .ask {
    width: 694rpx;
    height: 987rpx;
    position: absolute;
    top: 4094rpx;
    left: 28rpx;

    .what_bg {
      width: 694rpx;
      height: 987rpx;
    }

    .what_header {
      width: 410rpx;
      height: 105rpx;
      position: absolute;
      top: 0;
      left: 147rpx;
    }

    .title {
      font-weight: 600;
      font-size: 41rpx;
      color: #ffffff;
      line-height: 152rpx;
      text-align: left;
      font-style: normal;
      position: absolute;
      top: -26rpx;
      left: 265rpx;
    }

    .boxs {
      position: absolute;
      top: 140rpx;
      left: 33rpx;

      .xq_reports {
        padding: 0 20rpx;

        .content {
          display: flex;

          .text {
            width: 570rpx;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 28rpx;
            color: #444;
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
          }

          .back {
            margin-top: 8rpx;
            width: 14rpx;
            height: 22rpx;
          }
        }

        .line {
          width: 598rpx;
          height: 1rpx;
          background: #d9e0e7;
          margin: 51rpx 0;
        }
      }
    }
  }

  .popup {
    height: 900rpx;

    .popup-content {
      height: 1100rpx;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      background: #fff;
      border-radius: 35rpx;
      background-color: #fff;
      width: 600rpx;
      position: relative;
      padding-top: 40rpx;

      .substance {
        margin-bottom: 40rpx;
      }

      .query {
        width: 530rpx;
        height: 86rpx;
        background: #137aff;
        border-radius: 50rpx;
        font-weight: 400;
        font-size: 38rpx;
        color: #ffffff;
        line-height: 86rpx;
        text-align: center;
        font-style: normal;
      }
    }
  }

  .answerBoxs {
    height: 500rpx;

    .answer-content {
      height: 500rpx;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      background: #fff;
      border-radius: 35rpx;
      background-color: #fff;
      width: 600rpx;
      position: relative;
      padding-top: 40rpx;

      .substance {
        width: 600rpx;
        margin-bottom: 40rpx;
        padding: 0 20rpx;
      }

      .query {
        width: 530rpx;
        height: 86rpx;
        background: #137aff;
        border-radius: 50rpx;
        font-weight: 400;
        font-size: 38rpx;
        color: #ffffff;
        line-height: 86rpx;
        text-align: center;
        font-style: normal;
      }
    }
  }
}
</style>
