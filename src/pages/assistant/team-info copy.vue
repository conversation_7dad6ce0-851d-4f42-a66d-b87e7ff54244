<template>
  <view>
    <!-- <navbar :Props="Props" :title="headtitle" @black="black"></navbar> -->
    <view class="main-box">
      <view class="row row1">
        <image class="bg" src="/static/images/assistant/info-bg.png"></image>
        <view class="avatar">
          <image :src="baseUrl + '/boot/' + record.photoUrl" mode="aspectFill"></image>
          <text :style="{ fontSize: fontSizeLarge }">{{ record.assistantName }}</text>
        </view>
        <view class="list">
          <view>联系方式</view>
          <view class="placeholder">{{ formatPhone(record.assistantTel) }}</view>
        </view>
        <view class="list">
          <view>业务级别</view>
          <!-- <view class="placeholder">{{ record.businessLevel_dictText }}</view> -->
          <view class="placeholder">{{ tabType == 0 ? '政务服务' : '生活服务' }}</view>
        </view>
        <view class="list">
          <view>办理事项</view>
          <view class="placeholder">{{ record.divisionLevel_dictText }}</view>
          <!-- <view class="placeholder"></view> -->
        </view>
        <view class="list">
          <view>服务位置</view>
          <view class="placeholder" style="display: flex; align-items: center">
            <text style="margin-right: 8rpx"> {{ record.serviceLocal }}</text>
            <div class="mini-btn" @click="copy(record.serviceLocal)">复制</div>
          </view>
        </view>
        <view class="list">
          <view>窗口位置</view>
          <view class="placeholder">{{ record.windowLocal }}</view>
        </view>
      </view>
      <view class="row row2">
        <view class="list">
          <view>事项级别</view>
          <view class="selectBox" @click="showItemLevel">
            <text>{{ itemLevelName ? itemLevelName : '请选择' }}</text>
            <image src="/static/images/assistant/down.png"></image>
          </view>
        </view>
        <view class="list">
          <view>事项类型</view>
          <view class="selectBox" @click="showItemType">
            <text>{{ itemTypeName ? itemTypeName : '请选择' }}</text>
            <image src="/static/images/assistant/down.png"></image>
          </view>
        </view>

        <view class="list" style="border: none">
          <view style="display: flex; align-items: center">
            <text>办理事项</text>
            <!-- <u-icon size="20" name="question"></u-icon> -->
          </view>
          <view v-if="checkboxValue1.length < 1" class="selectBox" @click="showPopup">
            <text>请选择</text>
            <image src="/static/images/assistant/down.png"></image>
          </view>
        </view>
        <view style="display: flex; flex-wrap: wrap">
          <view style="margin-right: 8rpx" v-for="(item, index) in checkboxValue1" :key="index">
            <u-tag
              closable
              @close="deleteTag(index)"
              :text="item"
              plain
              size="medium"
              type="primary"
            ></u-tag>
          </view>
        </view>
      </view>
    </view>
    <view class="submit"
      ><button type="primary" style="background-color: #057ffe" @click="onSubmit">
        立即办理
      </button></view
    >
    <uni-popup showClose ref="popup" border-radius="10px 10px 0 0" background-color="#fff">
      <view class="popup-content">
        <view class="btns">
          <view class="b1" @click="closePopup">取消</view>
          <view class="b2" @click="closePopup">确定</view>
        </view>
        <u-checkbox-group
          v-model="checkboxValue1"
          placement="column"
          :borderBottom="true"
          @change="checkboxChange"
          iconPlacement="left"
        >
          <u-checkbox
            :customStyle="{ marginBottom: '30rpx' }"
            v-for="(item, index) in checkboxList1"
            :key="index"
            :label="item.name"
            :name="item.name"
            :labelSize="fontSizeMedium"
            :disabled="item.disabled"
          >
          </u-checkbox>
        </u-checkbox-group>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { getItemList, addNewRecord, getDictItem, getItemListByType } from '@/api/assistant/index.js'
import config from '@/config'
import navbar from '@/components/Navbar/index.vue'
import waves from '@/components/waves/waves.vue'
import { newSetToken, removeToken } from '@/utils/auth'
// 登录导入
import { checkToken, loginByPasswd } from '@/api/login'
export default {
  components: {
    navbar,
    waves
  },
  data() {
    return {
      headtitle: '帮办员信息',
      Props: {
        imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: '', //导航高度(动态获取传参)
        bgColor: '', //导航栏背景色,不传参则默认#9CF
        capsuleTop: '', //胶囊顶部距离(动态获取传参)
        textColor: '', //导航标题字体颜色(不传默认#FFF)
        iconColor: '', //icon图标颜色(不传默认#FFF)
        blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
        backText: '' //默认字体(返回)
      },
      itemList: ['全流程落地', '多流程', '单流程'],
      itemIndex: 0,
      checkboxValue1: [],
      checkboxList1: [],
      record: {},
      // 办理事项
      itemName: '',
      itemList: [],
      itemSelected: [],
      baseUrl: config.baseUrl,
      itemTypeName: '',
      itemLevelName: '',
      itemLevel: '',
      tabType: '',
      // 事项 type
      itemType: 1,
      businessType: 0,
      userInfo: {},
      itemTypeList: [],
      itemLevelList: [],
      // divisionLevel: '',
    }
  },
  // async onShow() {},
  async onLoad(option) {
    // 用户信息
    this.userInfo =
      typeof this.$store.state.user.userInfo == 'string'
        ? JSON.parse(this.$store.state.user.userInfo)
        : this.$store.state.user.userInfo
    this.loginType = uni.getStorageSync('loginType')
    this.record = JSON.parse(option.record)
    this.tabType = option.tabType
    this.businessType = this.tabType == 0 ? 1 : 2
    this.getItemList()
    if (this.record.item == 0) {
      this.itemIndex = 2
    }
    if (this.record.item == 1) {
      this.itemIndex = 1
    }
    if (this.record.item == 2) {
      this.itemIndex = 0
    }

    // 获取事项类型字典
    const res = await getDictItem({
      dictCode: 'jy_item_type'
    })
    this.itemTypeList = res.result
    // 获取事项类型字典
    const res2 = await getDictItem({
      dictCode: 'jy_administrative_level'
    })
    this.itemLevelList = res2.result
  },
  onShow() {
    let code = this.getCleanedCode()
    if (code) {
      uni.request({
        url: config.baseUrl + '/boot/miniapp/auth/loginByCode',
        method: 'POST',
        data: {
          code: code
        },
        success: (res) => {
          this.wxLoginSuccess(res.data.result.token)
        },
        fail: (err) => {
          console.error(err)
        }
      })
    }
  },
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall // 从Vuex获取小号字体大小
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium // 从Vuex获取中号字体大小
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge // 从Vuex获取大号字体大小
    },
    formatPhone() {
      return (phone) => {
        return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      }
    }
  },
  methods: {
    closePopup() {
      this.$refs.popup.close()
    },
    // 获取事项列表
    async getItemList() {
      this.checkboxList1 = []
      let query = {
        // businessType: this.businessType
        divisionLevel: this.itemLevel,
        itemType: this.itemType
      }
      // if (this.tabType == 0) {
      //   query['itemType'] = this.itemType
      // }
      const res = await getItemListByType(query)

      // res.result = res.result.sort((a, b) => {
      //   return a.itemOrder - b.itemOrder
      // })
      this.itemList = res.result
      this.itemList.forEach((item) => {
        this.checkboxList1.push({
          id: item.itemId,
          name: item.itemName,
          disabled: false
        })
      })
      // if (this.record.isAllItem == '1' && this.itemList.length > 0) {
      //   this.itemList.forEach((item) => {
      //     this.checkboxList1.push({
      //       id: item.itemId,
      //       name: item.itemName,
      //       disabled: false
      //     })
      //   })
      //   if (this.tabType == 0 && this.itemType == 1) {
      //     this.checkboxList1.unshift({
      //       id: 0,
      //       name: '全流程落地',
      //       disabled: false
      //     })
      //   }
      // } else {
      //   if (!this.record.singleMore) {
      //     this.itemList.forEach((item) => {
      //       this.checkboxList1.push({
      //         id: item.itemId,
      //         name: item.itemName,
      //         disabled: false
      //       })
      //     })
      //   } else {
      //     this.itemList.forEach((item) => {
      //       this.checkboxList1.push({
      //         id: item.itemId,
      //         name: item.itemName,
      //         disabled: this.record.singleMore.includes(item.itemId) ? false : true
      //       })
      //     })
      //   }
      // }
    },
    // 事项类型选择
    showItemType() {
      if (!this.itemLevel) {
        uni.showToast({
          icon: 'none',
          title: '请先选择事项级别'
        })
        return
      }
      uni.showActionSheet({
        itemList: this.itemTypeList.map((i) => i.text),
        success: (res) => {
          this.itemTypeName = this.itemTypeList[res.tapIndex].text
          this.itemType = this.itemTypeList[res.tapIndex].value
          this.getItemList()
        }
      })
    },
    // 事项级别选择
    showItemLevel() {
      uni.showActionSheet({
        itemList: this.itemLevelList.map((i) => i.text),
        success: (res) => {
          this.itemLevelName = this.itemLevelList[res.tapIndex].text
          this.itemLevel = this.itemLevelList[res.tapIndex].value
          this.getItemList()
        }
      })
    },
    deleteTag(index) {
      this.checkboxValue1.splice(index, 1)
      if (this.checkboxValue1.length < 1) {
        this.checkboxList1.map((i) => {
          i.disabled = false
        })
      }
      this.itemSelected.splice(index, 1)
    },
    copy(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: '复制成功'
          })
        }
      })
    },
    checkboxChange(n) {
      this.itemSelected = []
      if (this.record.isAllItem == '1' && this.tabType == 0 && this.itemType == 1) {
        console.log('jinlaile', this.checkboxList1)
        this.checkboxList1.map((i) => {
          i.disabled = false
        })
        n.map((item) => {
          if (item === '全流程落地') {
            this.checkboxList1.map((i) => {
              if (i.name !== '全流程落地') {
                i.disabled = true
                this.itemSelected.push(i.id)
              }
            })
          } else {
            this.checkboxList1[0].disabled = true
            const itemId = (this.checkboxList1[0].disabled = this.checkboxList1.find(
              (i) => i.name === item
            ).id)
            this.itemSelected.push(itemId)
          }
        })
      } else {
        n.map((item) => {
          const itemId = this.checkboxList1.find((i) => i.name === item).id
          this.itemSelected.push(itemId)
        })
      }
      console.log('itemSelected', this.itemSelected)
    },
    showPopup() {
      if (!this.itemLevel || !this.itemType) {
        uni.showToast({
          title: '请先选择事项级别和事项类型',
          icon: 'none'
        })
        return
      }
      this.$refs.popup.open('bottom')
    },
    itemChange(e) {
      this.itemIndex = e.target.value
    },
    async onSubmit() {
      if (!this.userInfo.mobilePhone) {
        uni.showToast({
          title: '请先完成实名认证',
          icon: 'none'
        })
        return
      }
      if (this.itemSelected.length < 1) {
        uni.showToast({
          title: '请选择要办理事项',
          icon: 'none'
        })
        return
      }
      const formData = {
        assistantId: this.record.assistantId,
        businessLevel: '0',
        itemList: this.itemSelected,
        // 事项类型
        recordType: this.itemType,
        itemType: this.itemType,
        divisionLevel: this.itemLevel,
        // // 个人事项
        // personalItem: this.itemSelected,
        // // 个人事项是否全选
        // isAllPersonal: this.this.itemSelected.length === this.itemList.length ? '1' : '0',

        //  isAllItem 是否全流程,1是，0不是  根据选择的事项判断
        isAllItem: this.itemSelected.length === this.itemList.length ? '1' : '0'
      }
      const res = await addNewRecord(formData)
      // team-manage
      const encodedRecord = encodeURIComponent(JSON.stringify(res.result))
      uni.navigateTo({
        url: `/pages/assistant/team-manage?record=${encodedRecord}`
      })
    },
    getCleanedCode() {
      let query = ''
      // 获取传统模式的查询参数（?code=xxx&openId=xxx）
      if (window.location.search) {
        query = window.location.search.substring(1)
      }
      // 获取 hash 模式的查询参数（#/path?code=xxx&openId=xxx）
      else if (window.location.hash.includes('?')) {
        query = window.location.hash.split('?')[1]
      }
      // 如果没有找到 query，返回 false
      if (!query) {
        return false
      }
      // 解析查询参数
      const vars = query.split('&')
      let code = ''

      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=')
        const key = decodeURIComponent(pair[0])
        const value = decodeURIComponent(pair[1])

        if (key === 'code') {
          code = value
          break // 找到后可以提前结束循环
        }
      }
      // 去除 code 中的空格和特殊字符
      if (code) {
        // 去除空格
        // let cleanedCode = code.replace(/\s*/g, '')
        // 处理特殊字符
        // cleanedCode = encodeURIComponent(cleanedCode)

        let cleanedCode = code
        return cleanedCode
      }

      return false
    },
    // 认证登录成功
    async wxLoginSuccess(token) {
      newSetToken(token)
      const res = await checkToken(token)
      const avatar = res.result.userInfo.avatar
      const nickname = res.result.userInfo.nickName
      this.$store.commit('SET_NAME', nickname)
      this.$store.commit('SET_AVATAR', avatar)
      this.$store.commit('SET_USERINFO', res.result.userInfo)
      this.$store.commit('setRoleId', 1)
      uni.setStorageSync('loginType', 1)
    }
  }
}
</script>
<style>
page {
  background-color: #f4f8fc;
}
</style>
<style lang="scss" scoped>
.main-box {
  .row {
    width: 686rpx;
    margin: 30rpx auto 60rpx auto;
    background: #ffffff;
    box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.06);
    border-radius: 32rpx;
    padding: 30rpx 20rpx;

    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #333333;
  }
  .list {
    min-height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2rpx solid #e7f0ff;
    padding: 10rpx 6rpx;
    & > view:nth-child(1) {
      min-width: 130rpx;
    }
    & > view:nth-child(2) {
      letter-spacing: 3rpx;
      font-weight: 400;
      color: #666666;
    }
  }
  .list:last-child {
    border-bottom: none;
  }
  .avatar {
    font-family: PingFangSC, PingFang SC;
    min-height: 120rpx;
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    font-weight: 500;
    color: #141414;
    border-bottom: 2rpx solid #e7f0ff;
    padding: 10rpx 6rpx;
    z-index: 1;
    image {
      width: 130rpx;
      height: 130rpx;
      margin-right: 20rpx;
      border-radius: 16rpx;
    }
  }
  .selectBox {
    display: flex;
    align-items: center;
    image {
      width: 28rpx;
      height: 28rpx;
      margin-left: 10rpx;
    }
  }
  .row1 {
    position: relative;
    .bg {
      position: absolute;
      width: 100%;
      height: 180rpx;
      top: 0;
      left: 0;
    }
  }
}
.popup-content {
  height: 700rpx;
  overflow: auto;
  padding: 100rpx 30rpx 30rpx 30rpx;
  position: relative;
  .btns {
    width: 95%;
    height: 60rpx;
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
    position: fixed;
    top: 0rpx;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    z-index: 2;
    .b1 {
      color: #999999;
    }
    .b2 {
      color: #057ffe;
    }
  }
}
.submit {
  width: 600rpx;
  margin: 20rpx auto;
}
.mini-btn {
  width: 120rpx;
  height: 48rpx;
  border-radius: 32rpx;
  border: 1rpx solid #d0d0d0;
  text-align: center;
  line-height: 48rpx;
}
</style>
<style lang="scss">
::v-deep .u-tag__close {
  z-index: 2 !important;
}
.u-tag--medium {
  min-height: 52rpx !important;
}
</style>
