<template>
  <div class="main_box">
    <div>
      <div class="title">请选择您所办事地区</div>
      <div class="title2">
        <div class="select">
          <img src="/static/images/assistant/<EMAIL>" alt="" />
          <span style="margin-left: 10rpx">{{ address }}</span>
        </div>
        <!-- <div class="select2">{{ selectText2 ? selectText2 : selectText }}</div> -->
      </div>
      <div class="line"></div>
      <div class="list">
        <!-- 省列表 -->
        <div class="city">
          <div :class="provinceIndex ? 'active' : ''">嘉鱼县</div>
        </div>
        <!-- 市区列表 -->
        <div class="city">
          <div
            :class="cityIndex == index ? 'active' : ''"
            v-for="(item, index) in columns[1]"
            :key="index"
            @click="selectCity(item, index)"
          >
            {{ item.name }}
          </div>
        </div>
        <!-- 区列表 -->
        <div class="city">
          <div
            :class="areaIndex == index ? 'active' : ''"
            v-for="(item, index) in columns[2]"
            :key="index"
            @click="selectArea(item, index)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="btns">
        <div v-if="currentIndex == 1" @click="goBack" style="margin-right: 60rpx">返回</div>
        <div class="next" @click="nextHandle">
          {{ currentIndex == 0 ? '下一步' : '确定' }}
        </div>
        <!-- <div style="margin-left: 20rpx" @click="clearCache">清理缓存</div> -->
      </div>

      <!-- <div v-if="xcxcxurl">
        <input type="text" :value="xcxcxurl" />
      </div> -->
    </div>
  </div>
</template>

<script src="./jweixin-1.3.2.js" type="text/javascript" charset="utf-8"></script>
<script>
import {
  getItemList,
  getAssistantByItem,
  administrativeDivisionRootList,
  getFrequentItem,
  getPageAssistant,
  getAssistantByPoint,
  addTelRecord,
  getHelpTelInfo
} from '@/api/assistant/index.js'
import config from '@/config'
import { newSetToken, removeToken } from '@/utils/auth'
// 登录导入
import { checkToken, loginByPasswd } from '@/api/login'
import { getAccessToken } from '@/utils/auth'
import { jsonp } from 'vue-jsonp'

import QQMapWX from '@/utils/qqmap-wx-jssdk.js'
import wxs from 'weixin-js-sdk'
import { wgs84togcj02, getErrorMsg } from '@/api/map/geolocation'
export default {
  components: {},
  data() {
    return {
      xcxcxurl: '',
      tabBerLists: [],
      headtitle: '小乔帮您办',
      loginType: 1,
      searchValue: '',
      teamList: [],
      typeList: [],
      loadMoreStatus: 'more',
      params: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      currentType: 0,
      imgBaseUrl: config.baseUrl,
      query: {
        divisionFullName: '',
        divisionId: '',
        divisionLevel: '',
        itemId: '',
        itemName: '',
        itemType: ''
      },
      keyword: '',
      keyword2: '',
      // phones: 'tel:18884888828',
      phones: '',
      loginForm: {
        username: '15571578328',
        password: 'Hwj@15571578328',
        captcha: '',
        checkKey: ''
      },
      text1: [],
      tabList: [{ name: '政务服务' }, { name: '生活服务' }],
      tabIndex: 0,
      show: false, // 控制 picker 显示与隐藏
      columns: [[], [], []], // 省、市、县的数据列
      fullData: [], // 存储所有省市县数据的完整列表
      show2: false,
      inputValueText: '',
      userInfo: {},
      pageNo: 1,
      pageSize: 10,
      total: 0,
      provinceIndex: true,
      cityIndex: null,
      areaIndex: null,
      selectText: '请选择',
      selectText2: '',
      currentIndex: 0,
      record: null,
      address: '',
      selectArr: {
        provinceid: '',
        cityid: '',
        areaid: ''
      },
      pageType: '',
      editType: '',
    }
  },
  watch: {
    // 监听路由 获取路由参数
    $route(to, from) {
      this.currentIndex = to && to.query.type ? to.query.type : 0
      if (to && to.query.record) {
        try {
          this.record = JSON.parse(to.query.record)
        } catch (error) {
          console.error('record 解析失败:', error)
          this.record = {}
        }
      }
      this.$nextTick(() => {
        console.log('-----------', this.record)
      })
    }
  },
  async onLoad(options) {
    this.xcxcxurl = window.location.href
    this.currentIndex = options && options.type ? options.type : 0
    if (options && options.record) {
      try {
        this.record = JSON.parse(options.record)
        this.pageType = options.pageType
        this.editType = options.editType
        console.log('地区选择', this.record)
      } catch (error) {
        console.error('record 解析失败:', error)
        this.record = {}
      }
    } else {
      this.record = {}
    }

    // 用户信息
    this.userInfo =
      typeof this.$store.state.user.userInfo == 'string'
        ? JSON.parse(this.$store.state.user.userInfo)
        : this.$store.state.user.userInfo
    this.loginType = uni.getStorageSync('loginType')
    this.fetchAllRegions()
    const res = await getFrequentItem()
    res.result.map((item) => {
      this.text1.push(item.itemName)
    })

    let url = 'https://apis.map.qq.com/ws/geocoder/v1/'
    let data = {
      coord_type: 5,
      get_poi: 0,
      output: 'jsonp',
      key: 'G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV',
      location: ''
    }
    // this.getCurrent()
    // console.log('jsonp',jsonp);

    wx.getLocation({
      type: 'gcj02',
      isHighAccuracy: true,
      highAccuracyExpireTime: 10000,
      success: async (res) => {
        data.location = res.latitude + ',' + res.longitude
        jsonp(url, data).then((res2) => {
          console.log('地图位置', res2)
          this.address = res2.result.formatted_addresses?.recommend || res2.result.address
        })
      }
    })
  },
  async onShow() {
    // 打印当前完整 url
    // console.log('当前完整 url:',location.href)

    let code = this.getCleanedCode()
    if (code) {
      uni.request({
        url: config.baseUrl + '/boot/miniapp/auth/loginByCode',
        method: 'POST',
        data: {
          code: code
        },
        success: (res) => {
          this.wxLoginSuccess(res.data.result.token)
        },
        fail: (err) => {
          console.error(err)
        }
      })
    }
  },
  computed() {},
  methods: {
    getCurrent() {
      let url = 'https://apis.map.qq.com/ws/geocoder/v1/'
      let data = {
        coord_type: 5,
        get_poi: 0,
        output: 'jsonp',
        key: 'G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV',
        location: ''
      }
      const that = this
      wx.getLocation({
        isHighAccuracy: true,
        type: 'gcj02',
        success: function (res) {
          let diffLat = 0.002419
          let diffLng = -0.00551
          that.latitude = Number(res.latitude) + diffLat
          that.longitude = Number(res.longitude) + diffLng
          that.latitude = that.latitude.toString()
          that.longitude = that.longitude.toString()
          that.longitudeCenter = that.longitude
          that.latitudeCenter = that.latitude

          console.log('定位', that.longitude, that.latitude)

          data.location = that.latitude + ',' + that.longitude
          jsonp(url, data).then((res2) => {
            console.log('地图位置', res2)
            that.address = res2.result.formatted_addresses?.recommend
          })
        }
      })
    },
    clearCache() {
      // 清理所有 localstorage
      uni.clearStorageSync()
    },
    goBack() {
      if (this.currentIndex == 0) {
        // uni.navigateBack()
      } else {
        uni.navigateTo({
          url: `/pages/assistant/team-info?record=${JSON.stringify(this.record)}&pageType=${this.pageType}&editType=${this.editType}`
        })
      }
    },
    nextHandle() {
      if (!this.userInfo.mobilePhone) {
        uni.showToast({
          title: '请先完成实名认证',
          icon: 'none'
        })
        return
      }
      if (!this.provinceIndex) {
        uni.showToast({
          title: '请选择您所办事地区',
          icon: 'none'
        })
        return
      }
      if (!this.currentIndex == 1) {
        this.query['selectArr'] = this.selectArr
        console.log('地区选择-------', this.query)
        uni.navigateTo({
          url: `/pages/assistant/itemSelect?record=${JSON.stringify(this.query)}&pageType=${this.pageType}&editType=${this.editType}`
        })
      } else {
        this.record.divisionFullName = this.query.divisionFullName
        this.record.divisionId = this.query.divisionId
        this.record.divisionLevel = this.query.divisionLevel
        uni.navigateTo({
          url: `/pages/assistant/team-info?record=${JSON.stringify(this.record)}&pageType=${this.pageType}&editType=${this.editType}`
        })
      }
    },
    queryPopup() {
      this.keyword = this.keyword2
      this.query['keyword'] = this.keyword
      this.pageNo = 1
      this.teamList = []
      this.loadMoreStatus = 'more'
      this.getTeamList()
      this.$refs.popup.close()
    },
    closePopup() {
      this.keyword2 = ''
      this.provinceIndex = null
      this.cityIndex = null
      this.areaIndex = null
      this.$set(this.columns, 1, [])
      this.$set(this.columns, 2, [])
      this.query['keyword'] = ''
      delete this.query.divisionId
      delete this.query.divisionLevel
      this.pageNo = 1
      this.teamList = []
      this.loadMoreStatus = 'more'
      this.getTeamList()
      this.$refs.popup.close()
    },
    go() {
      const now = new Date()
      const hours = now.getHours()
      const minutes = now.getMinutes()
      // 检查是否在上午8:30-12:00之间或下午14:30之后
      const isMorning =
        (hours === 8 && minutes >= 30) ||
        (hours > 8 && hours < 12) ||
        (hours === 12 && minutes === 0)
      const isAfternoon = hours > 14 || (hours === 14 && minutes >= 30)
      if (!isMorning && !isAfternoon) {
        uni.showToast({
          title: '不在服务时间范围内',
          icon: 'none'
        })
      } else {
        uni.navigateTo({
          url: `/pages/assistant/team-info?query=${JSON.stringify(this.query)}`
        })
      }
    },
    async getPhones() {
      let phone = ''
      const res = getHelpTelInfo({
        divisionLevel: this.query.divisionLevel
      })
      if (res.result) {
        phone = `tel:${res.result.assistantTel} || ${this.result.mobilePhone}`
      } else {
        phone = `tel:${this.userInfo.mobilePhone}`
      }
      const a = document.createElement('a')
      a.setAttribute('href', phone)
      a.click()
      addTelRecord({
        divisionFullName: this.query.divisionFullName,
        divisionId: this.query.divisionId,
        divisionLevel: this.query.divisionLevel
      })
    },
    getQueryVariable(variable) {
      let query = ''
      // 检查传统查询参数 (window.location.search) 是否存在
      if (window.location.search) {
        query = window.location.search.substring(1)
      }
      // 检查是否有 hash 模式中的查询参数
      else if (window.location.hash.includes('?')) {
        query = window.location.hash.split('?')[1]
      }
      if (!query) {
        // alert('没有找到查询字符串')
        this.keyword = '没有找到查询字符串'
        return false
      }
      const vars = query.split('&')
      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=')
        if (pair[0] === variable) {
          // alert('code: ' + pair[1])
          this.keyword = 'code: ' + pair[1]
          return pair[1]
        }
      }
      // alert('没有找到 ' + variable)
      this.keyword = '没有找到 ' + variable
      return false
    },
    // 通知点击
    noticeClick() {
      // 跳转人员详情 模拟
      let item = this.teamList[0]
      uni.navigateTo({
        url: `/pages/assistant/team-info?record=${JSON.stringify(item)}&tabType=${this.tabIndex}`
      })
    },
    tabClick(index) {
      this.tabIndex = index
      // this.query.itemType = index
      this.query.businessType = index == 0 ? 1 : 2
      this.pageNo = 1
      this.teamList = []
      this.getTeamList()
    },
    cancel() {
      this.show = false
    },
    openPicker() {
      this.$refs.popup.open('top')
    },
    // 省点击
    selectProvince() {
      this.provinceIndex = true
      this.$set(this.columns, 1, [])
      // this.columns[1].push(...this.columns[0][0].children)

      // console.log(this.selectArr);
    },
    // 市点击
    selectCity(item, index) {
      this.selectText2 = ''
      this.$set(this.columns, 2, [])
      this.areaIndex = null
      this.cityIndex = index
      this.selectText = item.name
      this.query.divisionId = item.id
      this.selectArr[1] = item.id
      this.query.divisionLevel = 2
      this.query.divisionFullName = '嘉鱼县' + item.name
      // 筛选 item.childre
      let arr = ['头墩社区', '肖家洲村', '四邑村', '红光社区', '潘湾社区', '潘家湾村']
      let arr2 = item.children.filter((item) => arr.includes(item.name))
      if (item.name === '潘家湾镇') {
        this.columns[2].push(...arr2)
      } else {
        this.columns[2].push(...item.children)
      }
    },
    // 区点击
    selectArea(item, index) {
      this.areaIndex = index
      this.selectArr[2] = item.id
      this.query.divisionId = item.id
      this.selectText2 = this.selectText + '/' + item.name
      this.query.divisionLevel = 3
      this.query.divisionFullName = '嘉鱼县' + this.selectText + item.name
      console.log(this.query.divisionFullName)
    },
    async fetchAllRegions() {
      const res = await administrativeDivisionRootList({ pid: 0 })
      this.$set(this.columns, 1, [])
      // 省数据
      this.columns[0].push(res.result[0])
      this.selectArr[0] = res.result[0].id
      this.provinceIndex = true
      console.log('省数据', this.columns)

      // 只显示潘家湾镇
      const defaultCity = res.result[0].children.find((item) => item.name === '潘家湾镇')
      if (defaultCity) {
        this.columns[1].push(defaultCity)
        this.selectArr[1] = defaultCity.id
        // 自动选中市级
        this.cityIndex = 0
        this.selectText = defaultCity.name
        this.query.divisionId = defaultCity.id
        this.query.divisionLevel = 2
        this.query.divisionFullName = '嘉鱼县' + defaultCity.name

        // 默认显示区级数据
        let arr = ['头墩社区', '肖家洲村', '四邑村', '红光社区', '潘湾社区', '潘家湾村']
        let arr2 = defaultCity.children.filter((item) => arr.includes(item.name))
        if (defaultCity.name === '潘家湾镇') {
          this.columns[2].push(...arr2)
        } else {
          this.columns[2].push(...defaultCity.children)
        }
      }
    },
    setInitialColumns() {
      // Initialize only the first level (province data)
      this.columns[0] = this.fullData.map((province) => province.name)
      this.columns[1] = [] // Hide the city data initially
      this.columns[2] = [] // Hide the county data initially

      console.log('Data initialized', this.columns)
      this.show2 = true
    },
    changeHandler({ columnIndex, index }) {
      const picker = this.$refs.uPicker

      if (columnIndex === 0) {
        // When selecting a province
        if (index === 0) {
          this.columns[1] = []
          this.columns[2] = []
          picker.setColumnValues(1, this.columns[1])
          picker.setColumnValues(2, this.columns[2])
        } else {
          const selectedProvince = this.fullData[index]
          // Load the cities only after selecting a province
          this.columns[1] = selectedProvince.children
            ? ['全部', ...selectedProvince.children.map((city) => city.name)]
            : ['全部']
          this.columns[2] = [] // Clear counties when switching provinces
          picker.setColumnValues(1, this.columns[1])
          picker.setColumnValues(2, this.columns[2])
        }
      } else if (columnIndex === 1) {
        // When selecting a city
        const selectedProvinceValue = picker.getValues()[0]
        const selectedProvince = this.fullData.find(
          (province) => province.name === selectedProvinceValue
        )

        if (index === 0) {
          this.columns[2] = []
          picker.setColumnValues(2, this.columns[2])
        } else if (selectedProvince) {
          const selectedCity = selectedProvince.children[index - 1]
          // Load counties only after selecting a city
          this.columns[2] =
            selectedCity && selectedCity.children
              ? ['全部', ...selectedCity.children.map((county) => county.name)]
              : ['全部']
          picker.setColumnValues(2, this.columns[2])
        }
      }
    },
    confirmHandler(e) {
      // 获取选中的名称
      const selectedProvince = e.value[0]
      const selectedCity = e.value[1]
      const selectedCounty = e.value[2]

      let provinceInfo = null
      let cityInfo = null
      let countyInfo = null

      // 处理省级选择
      if (selectedProvince === '全部') {
        provinceInfo = {
          id: null,
          name: '全部',
          administrative_level: '省'
        }
      } else {
        // 查找选中的省份
        const province = this.fullData.find((p) => p.name === selectedProvince)
        if (province) {
          provinceInfo = {
            id: province.id,
            name: province.name,
            administrative_level: province.administrative_level || '省'
          }

          // 处理市级选择
          if (selectedCity === '全部') {
            cityInfo = {
              id: null,
              name: '全部',
              administrative_level: '市'
            }
          } else {
            const city = province.children?.find((c) => c.name === selectedCity)
            if (city) {
              cityInfo = {
                id: city.id,
                name: city.name,
                administrative_level: city.administrative_level || '市'
              }

              // 处理县级选择
              if (selectedCounty === '全部') {
                countyInfo = {
                  id: null,
                  name: '全部',
                  administrative_level: '县'
                }
              } else {
                const county = city.children?.find((c) => c.name === selectedCounty)
                if (county) {
                  countyInfo = {
                    id: county.id,
                    name: county.name,
                    administrative_level: county.administrative_level || '县'
                  }
                }
              }
            }
          }
        }
      }

      console.log('Selected Province:', provinceInfo)
      console.log('Selected City:', cityInfo)
      console.log('Selected County:', countyInfo)

      // 更新显示文本
      this.inputValueText = `${provinceInfo?.name || ''}/${cityInfo?.name || ''}/${
        countyInfo?.name || ''
      }`

      // // 更新 divisionId
      // const provinceId = provinceInfo?.id || ''
      // const cityId = cityInfo?.id || ''
      // const countyId = countyInfo?.id || ''

      // this.query.divisionId = `${provinceId}${cityId ? ',' + cityId : ''}${
      //   countyId ? ',' + countyId : ''
      // }`
      // Update divisionId to only use the last selected ID
      const provinceId = provinceInfo?.id || ''
      const cityId = cityInfo?.id || ''
      const countyId = countyInfo?.id || ''

      // Only use the ID from the lowest selected level
      this.query.divisionId = countyId || cityId || provinceId

      // name === 全部的时候  this.query.divisionLevel =
      // 省 1 市 2 区3
      if (selectedProvince === '全部') {
        this.query.divisionLevel = 1
      } else if (selectedCity === '全部') {
        this.query.divisionLevel = 2
      } else if (selectedCounty === '全部') {
        this.query.divisionLevel = 3
      } else {
        this.query.divisionLevel = 3
      }
      console.log(this.query.divisionId, 'this.divisionId')

      console.log('selectedCity---------', selectedCity)

      if (selectedProvince === '全部' || selectedCity === '全部' || selectedCounty === '全部') {
        delete this.query.divisionId
      }
      this.pageNo = 1
      this.teamList = []
      this.getTeamList()
      this.show = false
    },
    goDetail(item) {
      uni.navigateTo({
        url: `/pages/assistant/team-info?record=${JSON.stringify(item)}&tabType=${this.tabIndex}`
      })
    },
    // 获取团队成员
    async getTeamList() {
      this.query.pageNo = this.pageNo
      this.query.pageSize = this.pageSize
      const res = await getPageAssistant(this.query)
      if (res.result.records.length < this.pageSize) {
        this.loadMoreStatus = 'noMore'
      } else {
        this.loadMoreStatus = 'more'
      }
      // this.total = res.result.total
      this.teamList = this.teamList.concat(res.result.records)
      let index = Math.floor(Math.random() * 10)
      // this.phones = `tel:${this.teamList[index].assistantTel}`
    },
    changeType(index, id) {
      this.currentType = index
      this.teamList = []
      if (index === 1) {
        this.query['isAll'] = 1
      } else if (index === 0) {
        delete this.query['isAll']
        delete this.query['itemId']
      } else {
        {
          delete this.query['isAll']
          this.query['itemId'] = id
        }
      }
      this.getTeamList()
    },
    search(val) {
      this.query['keyword'] = val
      this.pageNo = 1
      this.teamList = []
      this.getTeamList()
    },
    clear() {
      delete this.query['keyword']
      this.getTeamList()
    },
    getCleanedCode() {
      let query = ''
      // 获取传统模式的查询参数（?code=xxx&openId=xxx）
      if (window.location.search) {
        query = window.location.search.substring(1)
      }
      // 获取 hash 模式的查询参数（#/path?code=xxx&openId=xxx）
      else if (window.location.hash.includes('?')) {
        query = window.location.hash.split('?')[1]
      }
      // 如果没有找到 query，返回 false
      if (!query) {
        return false
      }
      // 解析查询参数
      const vars = query.split('&')
      let code = ''

      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=')
        const key = decodeURIComponent(pair[0])
        const value = decodeURIComponent(pair[1])

        if (key === 'code') {
          code = value
          break // 找到后可以提前结束循环
        }
      }
      // 去除 code 中的空格和特殊字符
      if (code) {
        // 去除空格
        // let cleanedCode = code.replace(/\s*/g, '')
        // 处理特殊字符
        // cleanedCode = encodeURIComponent(cleanedCode)

        let cleanedCode = code
        return cleanedCode
      }

      return false
    },
    // 认证登录成功
    async wxLoginSuccess(token) {
      newSetToken(token)
      const res = await checkToken(token)
      const avatar = res.result.userInfo.avatar
      const nickname = res.result.userInfo.nickName
      this.$store.commit('SET_NAME', nickname)
      this.$store.commit('SET_AVATAR', avatar)
      this.$store.commit('SET_USERINFO', res.result.userInfo)
      this.$store.commit('setRoleId', 1)
      uni.setStorageSync('loginType', 1)
    }
  },
  computed: {}
}
</script>
<style>
page {
  background-color: #f4f8fc;
  padding: 20rpx;
}
</style>
<style lang="scss" scoped>
.main_box {
  padding: 30rpx;
  margin: 30rpx auto;
  min-height: 300rpx;
  background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
  border-radius: 32rpx;
  .title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 36rpx;
    color: #141414;
    line-height: 50rpx;
    text-align: left;
    font-style: normal;
  }
  .title2 {
    display: flex;
    align-items: center;
    margin-top: 20rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 30rpx;
    line-height: 42rpx;
    text-align: left;
    font-style: normal;
    color: #444444;
    .select {
      color: #057ffe;
      img {
        width: 21rpx;
        height: 25rpx;
      }
    }
    .select2 {
      margin-left: 20rpx;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        bottom: -20rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 55rpx;
        height: 10rpx;
        background: url('../../static/images/assistant/select.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }
  .line {
    width: 100%;
    height: 1rpx;
    background: #e7f0ff;
    margin: 38rpx 0;
  }
  .list {
    display: flex;
  }
  .city {
    flex: 1;
    height: 660rpx;
    overflow: auto;
    padding: 0 10rpx;
    // border-right: 1px solid #e7f0ff;
    & > div {
      height: 80rpx;
      font-family: PingFangSC, PingFang SC;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 32rpx;
      color: #333;
      line-height: 80rpx;
      text-align: center;
      font-style: normal;
    }
    .active {
      // color: #057ffe;
      background: #e7f0ff;
      border-radius: 8rpx;
      color: #057ffe;
      // transition: all 0.3s ease-in-out;
    }
  }
  .btns {
    display: flex;
    justify-content: center;
    margin-top: 60rpx;
    & > div {
      width: 282rpx;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 8rpx;
      border: 2rpx solid #057ffe;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 36rpx;
      color: #057ffe;
    }
    .next {
      background: #057ffe;
      color: #fff;
    }
  }
  .banner {
    width: 100%;
    height: 214rpx;
  }
  .title3 {
    width: 120rpx;
    height: 42rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 30rpx;
    color: #141414;
    position: relative;
    margin: 30rpx 0;
    &::after {
      content: '';
      position: absolute;
      top: 0rpx;
      left: 0px;
      width: 27rpx;
      height: 26rpx;
      background: url('../../static/images/assistant/3.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .time {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #666666;
  }
}
</style>
