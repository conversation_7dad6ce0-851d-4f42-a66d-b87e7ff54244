<template>
  <view :style="{
      paddingTop:  Props.statusBarHeight + 'px'
    }">
    <navbar :Props="Props" :title="headtitle" @black="black"></navbar>
    <view class="main_box">
      <view v-for="(item, index) in itemList" :key="index" class="item">
        <view class="icon">
          <view>
            <image
              v-show="item.itemStatus === 'dks'"
              src="@/static/images/assistant/black.png"
            ></image>
            <image
              v-show="item.itemStatus === 'dtj' || item.itemStatus === 'dcl'"
              src="@/static/images/assistant/blue.png"
            ></image>
            <image
              v-show="item.itemStatus === 'th'"
              src="@/static/images/assistant/red.png"
            ></image>
            <image
              v-show="item.itemStatus === 'tg'"
              src="@/static/images/assistant/green.png"
            ></image>
          </view>
          <view v-show="index !== itemList.length - 1"></view>
        </view>
        <view class="info">
          <text class="title">{{ item.itemName }}</text>
          <text>{{ item.startTime || '' }}</text>
        </view>
      </view>
    </view>
    <view class="status-btn" @click="$tab.navigateBack()"> 确定 </view>
  </view>
</template>
<script>
import navbar from '@/components/Navbar/index.vue'
export default {
  components: { navbar },
  data() {
    return {
      headtitle: '事项进度',
      Props: {
        imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: '', //导航高度(动态获取传参)
        bgColor: '', //导航栏背景色,不传参则默认#9CF
        capsuleTop: '', //胶囊顶部距离(动态获取传参)
        textColor: '', //导航标题字体颜色(不传默认#FFF)
        iconColor: '', //icon图标颜色(不传默认#FFF)
        blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
        backText: '' //默认字体(返回)
      },
      // test
      itemList: [
        {
          itemName: '办理事项',
          startTime: '2020-11-11 11:11:11',
          itemStatus: 'dks'
        },
        {
          itemName: '办理事项',
          startTime: '2020-11-11 11:11:11',
          itemStatus: 'dtj'
        }
      ]
    }
  },
  onLoad(options) {
    this.Props.statusBarHeight = getApp().globalData.statusBarHeight
    this.Props.capsuleTop = getApp().globalData.capsuleTop
    this.itemList = JSON.parse(options.itemList)
  },
  methods: {}
}
</script>

<style>
page {
  background-color: #fff;
}
</style>
<style lang="scss" scoped>
.main_box {
  padding: 60rpx 20rpx;
}
.status-btn {
  width: 522rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #057ffe;
  border-radius: 40rpx;
  margin: 60rpx auto;

  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  color: #ffffff;
}
.item {
  display: flex;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #666666;
  .icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    & > view:nth-child(1) {
      image {
        width: 30rpx;
        height: 30rpx;
      }
    }
    & > view:nth-child(2) {
      margin-top: -2rpx;
      width: 4rpx;
      height: 130rpx;
      background-color: #8f939c;
    }
  }
  .info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    text-align: center;
    .title {
      font-weight: 500;
      font-size: 26rpx;
      color: #444444;
      margin-right: 20rpx;
      text-align: left;
    }
  }
}
</style>
