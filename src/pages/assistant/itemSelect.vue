<template>
  <div>
    <!-- <div class="back_box" @click="goBack">
      <img src="/static/images/admin/xq_back.png" alt="" />
      返回
    </div> -->
    <div class="main-box">
      <div class="title">请选择您需要帮代办的事项</div>
      <div class="cell" @click="showItemType">
        <div class="label label2">事项类型:</div>
        <div class="value2">
          <span>{{ itemTypeName ? itemTypeName : '请选择' }}</span>
          <img src="/static/images/assistant/down.png" />
        </div>
      </div>
      <div class="cell2" @click="showItemList">
        <div class="label label2">办理事项:</div>
        <div class="value2">
          <span>{{ itemNameText ? itemNameText : '请选择' }}</span>
          <img src="/static/images/assistant/down.png" />
        </div>
      </div>

      <div class="btns" @click="nextHandle">下一步</div>
    </div>
  </div>
</template>

<script>
import {
  getItemList,
  addNewRecord,
  getDictItem,
  getItemListByType,
  getItemType,
  getItemListNew,
  addNewRecordNew,
  commitRecord,
  saveRecordNew
} from '@/api/assistant/index.js'
import config from '@/config'
import navbar from '@/components/Navbar/index.vue'
import waves from '@/components/waves/waves.vue'
import { newSetToken, removeToken } from '@/utils/auth'
// 登录导入
import { checkToken, loginByPasswd } from '@/api/login'
export default {
  components: {
    navbar,
    waves
  },
  data() {
    return {
      headtitle: '帮办员信息',
      Props: {
        imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: '', //导航高度(动态获取传参)
        bgColor: '', //导航栏背景色,不传参则默认#9CF
        capsuleTop: '', //胶囊顶部距离(动态获取传参)
        textColor: '', //导航标题字体颜色(不传默认#FFF)
        iconColor: '', //icon图标颜色(不传默认#FFF)
        blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
        backText: '' //默认字体(返回)
      },
      itemList: ['全流程落地', '多流程', '单流程'],
      itemIndex: 0,
      checkboxValue1: [],
      checkboxList1: [],
      record: {},
      // 办理事项
      itemName: '',
      itemList: [],
      itemSelected: [],
      baseUrl: config.baseUrl,
      itemTypeName: '',
      itemLevelName: '',
      itemLevel: '',
      tabType: '',
      // 事项 type
      itemType: 1,
      businessType: 0,
      userInfo: {},
      itemTypeList: [],
      itemLevelList: [],
      query: {},
      itemNameText: ''
    }
  },
  async onLoad(option) {
    this.userInfo =
      typeof this.$store.state.user.userInfo == 'string'
        ? JSON.parse(this.$store.state.user.userInfo)
        : this.$store.state.user.userInfo
    this.loginType = uni.getStorageSync('loginType')
    let res = JSON.parse(option.record)
    this.query = res
    this.itemTypeName = res.itemType
    this.itemNameText = res.itemName
    // console.log(this.query)
  },
  async onShow() {
    // let code = this.getCleanedCode()
    // if (code) {
    //   uni.request({
    //     url: config.baseUrl + '/boot/miniapp/auth/loginByCode',
    //     method: 'POST',
    //     data: {
    //       code: code
    //     },
    //     success: (res) => {
    //       this.wxLoginSuccess(res.data.result.token)
    //     },
    //     fail: (err) => {
    //       console.error(err)
    //     }
    //   })
    // }
    // 事项类型
    const res = await getItemType()
    this.itemTypeList = res.result
  },
  computed: {
    maskedPhone() {
      const phone = this.query.contactPhone || ''
      return phone.length === 11 ? phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : phone
    },
    maskedIdCard() {
      const idCard = this.query.idCard || ''
      return idCard.length === 18
        ? idCard.replace(/(\d{3})\d{6}(\d{3}[0-9Xx])/, '$1******$2')
        : idCard
    }
  },
  methods: {
    goBack() {
       uni.navigateBack()
    },
    nextHandle() {
      if (!this.query.itemName) {
        uni.showToast({
          icon: 'none',
          title: '请选择办理事项'
        })
        return
      }
      uni.navigateTo({
        url: `/pages/assistant/xqbb?query=${JSON.stringify(this.query)}`
      })
      console.log('事项选择', this.query)
    },
    updatePhone(event) {
      this.query.contactPhone = event.target.value
    },
    updateIdCard(event) {
      this.query.idCard = event.target.value
    },
    selectArea() {
      if (this.query.isDetail == 1) {
        return
      }
      uni.redirectTo({
        url: `/pages/assistant/index?type=1&record=${JSON.stringify(this.query)}`
      })
    },
    closePopup() {
      this.$refs.popup.close()
    },
    // 获取事项列表
    async getItemListNewList() {
      const res = await getItemListNew({
        itemType: this.query.itemType
      })
      this.itemList = res.result
    },
    // 事项类型选择
    showItemType() {
      if (this.query.isDetail == 1) {
        return
      }
      uni.showActionSheet({
        itemList: this.itemTypeList.map((i) => i),
        success: (res) => {
          this.itemNameText = ''
          this.query.itemName = ''
          this.itemTypeName = this.itemTypeList[res.tapIndex]
          this.query.itemType = this.itemTypeList[res.tapIndex]
          this.getItemListNewList()
        }
      })
    },
    // 事项选择
    showItemList() {
      if (!this.query.itemType) {
        uni.showToast({
          icon: 'none',
          title: '请先选择事项类型'
        })
        return
      }
      uni.showActionSheet({
        itemList: this.itemList.map((i) => i.itemName),
        success: (res) => {
          this.query.itemName = this.itemList[res.tapIndex].itemName
          this.itemNameText = this.itemList[res.tapIndex].itemName
          this.query.itemId = this.itemList[res.tapIndex].itemId
          this.query.divisionLevel = this.itemList[res.tapIndex].divisionLevel

          if (this.query.divisionLevel == 1) {
            this.query.divisionId = this.query.selectArr[0]
          }
          if (this.query.divisionLevel == 2) {
            this.query.divisionId = this.query.selectArr[1]
          }
          if (this.query.divisionLevel == 3) {
            this.query.divisionId = this.query.selectArr[2]
          }
        }
      })
    },
    deleteTag(index) {
      this.checkboxValue1.splice(index, 1)
      if (this.checkboxValue1.length < 1) {
        this.checkboxList1.map((i) => {
          i.disabled = false
        })
      }
      this.itemSelected.splice(index, 1)
    },
    copy(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: '复制成功'
          })
        }
      })
    },
    checkboxChange(n) {
      this.itemSelected = []
      if (this.record.isAllItem == '1' && this.tabType == 0 && this.itemType == 1) {
        console.log('jinlaile', this.checkboxList1)
        this.checkboxList1.map((i) => {
          i.disabled = false
        })
        n.map((item) => {
          if (item === '全流程落地') {
            this.checkboxList1.map((i) => {
              if (i.name !== '全流程落地') {
                i.disabled = true
                this.itemSelected.push(i.id)
              }
            })
          } else {
            this.checkboxList1[0].disabled = true
            const itemId = (this.checkboxList1[0].disabled = this.checkboxList1.find(
              (i) => i.name === item
            ).id)
            this.itemSelected.push(itemId)
          }
        })
      } else {
        n.map((item) => {
          const itemId = this.checkboxList1.find((i) => i.name === item).id
          this.itemSelected.push(itemId)
        })
      }
      console.log('itemSelected', this.itemSelected)
    },
    showPopup() {
      if (!this.itemLevel || !this.itemType) {
        uni.showToast({
          title: '请先选择事项级别和事项类型',
          icon: 'none'
        })
        return
      }
      this.$refs.popup.open('bottom')
    },
    itemChange(e) {
      this.itemIndex = e.target.value
    },
    async onSubmit(type = 0) {
      // 办理事项必填
      if (!this.query.itemName) {
        uni.showToast({
          icon: 'none',
          title: '请选择办理事项'
        })
        return
      }
      let res = null
      if (type == 0) {
        res = await commitRecord(this.query)
      } else {
        res = await saveRecordNew(this.query)
      }
      if (res.success) {
        uni.showToast({
          title: `${type == 0 ? '提交' : '保存'}成功`,
          icon: 'none'
        })
        setTimeout(() => {
          this.$tab.navigateTo('/pages/mine/handlingRecord?type=0')
        }, 500)
      }
    },
    getCleanedCode() {
      let query = ''
      // 获取传统模式的查询参数（?code=xxx&openId=xxx）
      if (window.location.search) {
        query = window.location.search.substring(1)
      }
      // 获取 hash 模式的查询参数（#/path?code=xxx&openId=xxx）
      else if (window.location.hash.includes('?')) {
        query = window.location.hash.split('?')[1]
      }
      // 如果没有找到 query，返回 false
      if (!query) {
        return false
      }
      // 解析查询参数
      const vars = query.split('&')
      let code = ''

      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=')
        const key = decodeURIComponent(pair[0])
        const value = decodeURIComponent(pair[1])

        if (key === 'code') {
          code = value
          break // 找到后可以提前结束循环
        }
      }
      // 去除 code 中的空格和特殊字符
      if (code) {
        // 去除空格
        // let cleanedCode = code.replace(/\s*/g, '')
        // 处理特殊字符
        // cleanedCode = encodeURIComponent(cleanedCode)

        let cleanedCode = code
        return cleanedCode
      }

      return false
    },
    // 认证登录成功
    async wxLoginSuccess(token) {
      newSetToken(token)
      const res = await checkToken(token)
      const avatar = res.result.userInfo.avatar
      const nickname = res.result.userInfo.nickName
      this.$store.commit('SET_NAME', nickname)
      this.$store.commit('SET_AVATAR', avatar)
      this.$store.commit('SET_USERINFO', res.result.userInfo)
      this.$store.commit('setRoleId', 1)
      uni.setStorageSync('loginType', 1)
    }
  }
}
</script>
<style>
page {
  background-color: #f4f8fc;
  padding: 20rpx;
}
</style>
<style lang="scss" scoped>
.back_box {
  height: 60rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  img {
    width: 28rpx;
    height: 40rpx;
    transform: rotate(180deg);
    margin-right: 10rpx;
  }
}
.main-box {
  padding: 30rpx;
  margin: 30rpx auto;
  // min-height: 500rpx;
  // background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
  background: url('/static/images/assistant/bg.png') no-repeat;
  background-size: 100% 100%;
  border-radius: 32rpx;
  .title {
    width: 100%;
    height: 50rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 36rpx;
    color: #141414;
    line-height: 50rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 30rpx;
  }
  .cell {
    height: 80rpx;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
  }
  .cell2 {
    min-height: 80rpx;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
  }
  .label {
    height: 80rpx;
    line-height: 80rpx;
    width: 160rpx;
    text-align: right;
    margin-right: 15rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #333333;
  }
  .label2 {
    position: relative;
    &::after {
      content: '*';
      position: absolute;
      left: 5rpx;
      top: 5rpx;
      color: #f24f4b;
    }
  }
  .value {
    height: 80rpx;
    line-height: 80rpx;
    flex: 1;
    background: #f6f6f6;
    border-radius: 6rpx;
    padding-left: 20rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #444444;
    font-style: normal;
    input {
      height: 80rpx;
      font-size: 32rpx;
    }
  }
  .value2 {
    min-height: 60rpx;
    // line-height: 60rpx;
    flex: 1;
    background: #ffffff;
    border-radius: 6rpx;
    border: 2rpx solid #dddddd;
    padding: 0 20rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #444444;
    font-style: normal;
    display: flex;
    align-items: center;
    justify-content: space-between;
    img {
      width: 28rpx;
      height: 14rpx;
    }
  }
  .btns {
    width: 282rpx;
    margin: 50rpx auto 0 auto;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 8rpx;
    border: 2rpx solid #057ffe;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 36rpx;
    color: #057ffe;
    text-align: center;
    background: #057ffe;
    border-radius: 8rpx;
    color: #fff;
  }

  .cbfw {
    margin: 60rpx 0rpx;
    .title2 {
      display: flex;
      font-size: 33rpx;
      font-weight: 500;
      justify-content: space-between;
      margin-bottom: 20rpx;
    }
    .list {
      // text-align: center;
      & > div {
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
      }
    }
  }
}
.popup-content {
  height: 700rpx;
  overflow: auto;
  padding: 100rpx 30rpx 30rpx 30rpx;
  position: relative;
  .btns {
    width: 95%;
    height: 60rpx;
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
    position: fixed;
    top: 0rpx;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    z-index: 2;
    .b1 {
      color: #999999;
    }
    .b2 {
      color: #057ffe;
    }
  }
}
.submit {
  width: 600rpx;
  margin: 20rpx auto;
}
.mini-btn {
  width: 120rpx;
  height: 48rpx;
  border-radius: 32rpx;
  border: 1rpx solid #d0d0d0;
  text-align: center;
  line-height: 48rpx;
}
</style>
<style lang="scss">
::v-deep .u-tag__close {
  z-index: 2 !important;
}
.u-tag--medium {
  min-height: 52rpx !important;
}
</style>
