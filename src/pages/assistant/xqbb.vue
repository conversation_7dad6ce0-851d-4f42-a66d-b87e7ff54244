<template>
  <div>
    <!-- <div class="back_box" @click="goBack">
      <img src="/static/images/admin/xq_back.png" alt="" />
      返回
    </div> -->
    <div class="main_box">
      <div @click="getPhones" class="banner-box">
        <img src="/static/images/assistant/xqrx.png" class="banner" alt="" />
      </div>
      <div @click="go" class="banner-box">
        <img src="/static/images/assistant/xqbb.png" class="banner" alt="" />
      </div>
      <div>
        <div class="title3">工作时间</div>
        <div class="time">
          <span style="font-weight: 400">{{ isWorkDay() ? '(工作日)' : '(非工作日)' }}</span>
          <span>上午 {{ amTime }} </span>
          <span>下午 {{ pmTime }} </span>
        </div>
      </div>

      <uni-popup ref="popup">
        <div class="popup-content">
          <div class="title">很抱歉,现在是非工作时间</div>
          <div class="btn" @click="closePopup">确定</div>
        </div>
      </uni-popup>
      <uni-popup
        class="answerBoxs"
        ref="guide"
        type="center"
        mask-background-color="rgba(0,0,0,0.1)"
      >
        <!-- 			<view class="answer-content">
				<view class="business">
					业务须知
				</view>
				<view class="">
					<u-parse :content="noticeContent"></u-parse>
				</view>
				<view class="query" @click="iknow">我已知晓</view>
			</view> -->

        <view class="content">
          <image
            class="report_title"
            src="@/static/images/interaction/report_title.png"
            mode=""
          ></image>
          <image
            class="cross"
            @click="handleBack()"
            src="@/static/images/cross.png"
            mode=""
          ></image>
          <view class="business b34"> 业务须知 </view>
          <view class="entering">
            <u-parse :content="noticeContent"></u-parse>
          </view>
          <div class="btns">
            <div class="btn" @click="iknow()">我已知晓</div>
          </div>
        </view>
      </uni-popup>
    </div>
  </div>
</template>

<script>
import {
  getItemList,
  getAssistantByItem,
  administrativeDivisionRootList,
  getFrequentItem,
  getPageAssistant,
  getAssistantByPoint,
  addTelRecord,
  getHelpTelInfo,
  addNewRecordNew,
  getInstructionInfo
} from '@/api/assistant/index.js'
import config from '@/config'
import { newSetToken, removeToken } from '@/utils/auth'
// 登录导入
import { checkToken, loginByPasswd } from '@/api/login'
export default {
  components: {},
  data() {
    return {
      tabBerLists: [],
      headtitle: '小乔帮您办',
      loginType: 1,
      searchValue: '',
      teamList: [],
      typeList: [],
      loadMoreStatus: 'more',
      params: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      currentType: 0,
      imgBaseUrl: config.baseUrl,
      query: {},
      keyword: '',
      keyword2: '',
      // phones: 'tel:18884888828',
      phones: '',
      loginForm: {
        username: '15571578328',
        password: 'Hwj@15571578328',
        captcha: '',
        checkKey: ''
      },
      text1: [],
      tabList: [
        {
          name: '政务服务'
        },
        {
          name: '生活服务'
        }
      ],
      tabIndex: 0,
      show: false, // 控制 picker 显示与隐藏
      columns: [[], [], []], // 省、市、县的数据列
      fullData: [], // 存储所有省市县数据的完整列表
      show2: false,
      inputValueText: '',
      userInfo: {},
      pageNo: 1,
      pageSize: 10,
      total: 0,
      provinceIndex: null,
      cityIndex: null,
      areaIndex: null,
      selectText: '请选择',
      selectText2: '',
      currentIndex: 0,
      amTime: '',
      pmTime: '',
      phones2: '',
      noticeContent: ''
    }
  },
  watch: {},
  async onLoad(options) {
    // 用户信息
    this.userInfo =
      typeof this.$store.state.user.userInfo == 'string'
        ? JSON.parse(this.$store.state.user.userInfo)
        : this.$store.state.user.userInfo
    this.loginType = uni.getStorageSync('loginType')
    this.query = JSON.parse(options.query)

    const res2 = await getHelpTelInfo({
      divisionLevel: this.query.divisionLevel,
      divisionId: this.query.divisionId
    })
    if (res2.success) {
      this.phones = `tel:${res2.result.phoneNumber}`
      this.amTime = res2.result.amTime
      this.pmTime = res2.result.pmTime
      this.phones2 = res2.result.phoneNumber
    }
    this.getNotice()
  },
  async onShow() {
    // this.fetchAllRegions()
    // const res = await getFrequentItem()
    // res.result.map((item) => {
    //   this.text1.push(item.itemName)
    // })
    // let code = this.getCleanedCode()
    // if (code) {
    //   uni.request({
    //     url: config.baseUrl + '/boot/miniapp/auth/loginByCode',
    //     method: 'POST',
    //     data: {
    //       code: code
    //     },
    //     success: (res) => {
    //       this.wxLoginSuccess(res.data.result.token)
    //     },
    //     fail: (err) => {
    //       console.error(err)
    //     }
    //   })
    // }
  },
  computed() {},
  methods: {
    goBack() {
      uni.navigateBack()
    },
    // 判断是否是工作日
    isWorkDay() {
      const now = new Date()
      const day = now.getDay()
      return day !== 0 && day !== 6
    },
    handleBack() {
      this.$refs.guide.close()
    },
    async getNotice() {
      const res = await getInstructionInfo()
      console.log(res, '22222')
      if (res.result[0]?.content) {
        this.noticeContent = res.result[0]?.content
        this.$refs.guide.open()
      }
    },
    guideDialog() {
      this.site = this.newsList.netHandle
      this.$refs.guide.open()
    },
    iknow() {
      this.$refs.guide.close()
    },
    nextHandle() {
      if (!this.query.divisionId) {
        uni.showToast({
          title: '请选择服务区域',
          icon: 'none'
        })
        return
      }
      this.currentIndex = 1
    },
    queryPopup() {
      this.keyword = this.keyword2
      this.query['keyword'] = this.keyword
      this.pageNo = 1
      this.teamList = []
      this.loadMoreStatus = 'more'
      this.getTeamList()
      this.$refs.popup.close()
    },
    closePopup() {
      // this.keyword2 = ''
      // this.provinceIndex = null
      // this.cityIndex = null
      // this.areaIndex = null
      // this.$set(this.columns, 1, [])
      // this.$set(this.columns, 2, [])
      // this.query['keyword'] = ''
      // delete this.query.divisionId
      // delete this.query.divisionLevel
      // this.pageNo = 1
      // this.teamList = []
      // this.loadMoreStatus = 'more'
      // this.getTeamList()
      this.$refs.popup.close()
    },
    async go() {
      if (!this.checkTime()) {
        this.$refs.popup.open('center')
      } else {
        const res = await addNewRecordNew({
          itemName: this.query.itemName,
          itemType: this.query.itemType,
          itemId: this.query.itemId,
          divisionId: this.query.divisionId,
          divisionFullName: this.query.divisionFullName,
          divisionLevel: this.query.divisionLevel
        })
        if (res.success) {
          console.log('-------------', res.result)
          res.result.itemId = this.query.itemId
          res.result.itemName = this.query.itemName
          res.result.itemType = this.query.itemType
          uni.navigateTo({
            url: `/pages/assistant/team-info?record=${JSON.stringify(res.result)}
            `
          })
        }
      }
    },
    // 检测时间
    checkTime() {
      const now = new Date()
      const currentHours = now.getHours()
      const currentMinutes = now.getMinutes()

      // 解析上午时间范围
      const [amStart, amEnd] = this.amTime.split('-').map((time) => time.split(':').map(Number))
      const [amStartHour, amStartMinute] = amStart
      const [amEndHour, amEndMinute] = amEnd

      // 解析下午时间范围
      const [pmStart, pmEnd] = this.pmTime.split('-').map((time) => time.split(':').map(Number))
      const [pmStartHour, pmStartMinute] = pmStart
      const [pmEndHour, pmEndMinute] = pmEnd

      // 检查是否在上午时间范围
      const isMorning =
        (currentHours > amStartHour ||
          (currentHours === amStartHour && currentMinutes >= amStartMinute)) &&
        (currentHours < amEndHour || (currentHours === amEndHour && currentMinutes <= amEndMinute))

      // 检查是否在下午时间范围
      const isAfternoon =
        (currentHours > pmStartHour ||
          (currentHours === pmStartHour && currentMinutes >= pmStartMinute)) &&
        (currentHours < pmEndHour || (currentHours === pmEndHour && currentMinutes <= pmEndMinute))

      return isMorning || isAfternoon
    },
    async getPhones() {
      // if (!this.isWorkDay()) {
      //   this.$refs.popup.open('center')
      //   return
      // }
      if (!this.checkTime()) {
        this.$refs.popup.open('center')
        return
      }
      let phone = ''
      if (this.phones) {
        phone = this.phones
      } else {
        phone = `tel:${this.userInfo.mobilePhone}`
      }
      const a = document.createElement('a')
      a.setAttribute('href', phone)
      if (this.isWorkDay()) {
        a.click()
      }
      addTelRecord({
        phoneNumber: this.phones2,
        divisionFullName: this.query.divisionFullName,
        divisionId: this.query.divisionId,
        divisionLevel: this.query.divisionLevel,
        itemType: this.query.itemType,
        itemId: this.query.itemId,
        itemName: this.query.itemName,
        divisionLevel: this.query.divisionLevel
      })
    },
    getQueryVariable(variable) {
      let query = ''
      // 检查传统查询参数 (window.location.search) 是否存在
      if (window.location.search) {
        query = window.location.search.substring(1)
      }
      // 检查是否有 hash 模式中的查询参数
      else if (window.location.hash.includes('?')) {
        query = window.location.hash.split('?')[1]
      }
      if (!query) {
        // alert('没有找到查询字符串')
        this.keyword = '没有找到查询字符串'
        return false
      }
      const vars = query.split('&')
      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=')
        if (pair[0] === variable) {
          // alert('code: ' + pair[1])
          this.keyword = 'code: ' + pair[1]
          return pair[1]
        }
      }
      // alert('没有找到 ' + variable)
      this.keyword = '没有找到 ' + variable
      return false
    },
    // 通知点击
    noticeClick() {
      // 跳转人员详情 模拟
      let item = this.teamList[0]
      uni.navigateTo({
        url: `/pages/assistant/team-info?record=${JSON.stringify(item)}&tabType=${this.tabIndex}`
      })
    },
    tabClick(index) {
      this.tabIndex = index
      // this.query.itemType = index
      this.query.businessType = index == 0 ? 1 : 2
      this.pageNo = 1
      this.teamList = []
      this.getTeamList()
    },
    cancel() {
      this.show = false
    },
    openPicker() {
      this.$refs.popup.open('top')
    },
    // 省点击
    selectProvince(item, index) {
      this.provinceIndex = index
      this.$set(this.columns, 1, [])
      if (item.name === '全部') {
        this.$set(this.columns, 2, [])
        this.query.divisionLevel = 1
        delete this.query.divisionId
      } else {
        this.query.divisionId = item.id
        delete this.query.divisionLevel
      }
      this.columns[1].push(
        {
          name: '全部',
          id: null
        },
        ...item.children
      )
    },
    // 市点击
    selectCity(item, index) {
      this.selectText2 = ''
      this.$set(this.columns, 2, [])
      this.areaIndex = null
      this.cityIndex = index
      this.selectText = item.name
      this.query.divisionId = item.id
      this.query.divisionLevel = 2
      this.query.divisionFullName = '嘉鱼县' + ',' + item.name
      this.columns[2].push(...item.children)
    },
    // 区点击
    selectArea(item, index) {
      this.areaIndex = index
      this.query.divisionId = item.id
      this.selectText2 = this.selectText + '/' + item.name
      this.query.divisionLevel = 3
      this.query.divisionFullName = '嘉鱼县' + ',' + this.selectText + ',' + item.name
      console.log(this.query.divisionFullName)
    },
    async fetchAllRegions() {
      const res = await administrativeDivisionRootList({
        pid: 0
      })
      this.$set(this.columns, 1, [])
      // 市 数据
      this.columns[1].push(...res.result[0].children)
    },
    setInitialColumns() {
      // Initialize only the first level (province data)
      this.columns[0] = this.fullData.map((province) => province.name)
      this.columns[1] = [] // Hide the city data initially
      this.columns[2] = [] // Hide the county data initially

      console.log('Data initialized', this.columns)
      this.show2 = true
    },
    changeHandler({ columnIndex, index }) {
      const picker = this.$refs.uPicker

      if (columnIndex === 0) {
        // When selecting a province
        if (index === 0) {
          this.columns[1] = []
          this.columns[2] = []
          picker.setColumnValues(1, this.columns[1])
          picker.setColumnValues(2, this.columns[2])
        } else {
          const selectedProvince = this.fullData[index]
          // Load the cities only after selecting a province
          this.columns[1] = selectedProvince.children
            ? ['全部', ...selectedProvince.children.map((city) => city.name)]
            : ['全部']
          this.columns[2] = [] // Clear counties when switching provinces
          picker.setColumnValues(1, this.columns[1])
          picker.setColumnValues(2, this.columns[2])
        }
      } else if (columnIndex === 1) {
        // When selecting a city
        const selectedProvinceValue = picker.getValues()[0]
        const selectedProvince = this.fullData.find(
          (province) => province.name === selectedProvinceValue
        )

        if (index === 0) {
          this.columns[2] = []
          picker.setColumnValues(2, this.columns[2])
        } else if (selectedProvince) {
          const selectedCity = selectedProvince.children[index - 1]
          // Load counties only after selecting a city
          this.columns[2] =
            selectedCity && selectedCity.children
              ? ['全部', ...selectedCity.children.map((county) => county.name)]
              : ['全部']
          picker.setColumnValues(2, this.columns[2])
        }
      }
    },
    confirmHandler(e) {
      // 获取选中的名称
      const selectedProvince = e.value[0]
      const selectedCity = e.value[1]
      const selectedCounty = e.value[2]

      let provinceInfo = null
      let cityInfo = null
      let countyInfo = null

      // 处理省级选择
      if (selectedProvince === '全部') {
        provinceInfo = {
          id: null,
          name: '全部',
          administrative_level: '省'
        }
      } else {
        // 查找选中的省份
        const province = this.fullData.find((p) => p.name === selectedProvince)
        if (province) {
          provinceInfo = {
            id: province.id,
            name: province.name,
            administrative_level: province.administrative_level || '省'
          }

          // 处理市级选择
          if (selectedCity === '全部') {
            cityInfo = {
              id: null,
              name: '全部',
              administrative_level: '市'
            }
          } else {
            const city = province.children?.find((c) => c.name === selectedCity)
            if (city) {
              cityInfo = {
                id: city.id,
                name: city.name,
                administrative_level: city.administrative_level || '市'
              }

              // 处理县级选择
              if (selectedCounty === '全部') {
                countyInfo = {
                  id: null,
                  name: '全部',
                  administrative_level: '县'
                }
              } else {
                const county = city.children?.find((c) => c.name === selectedCounty)
                if (county) {
                  countyInfo = {
                    id: county.id,
                    name: county.name,
                    administrative_level: county.administrative_level || '县'
                  }
                }
              }
            }
          }
        }
      }

      console.log('Selected Province:', provinceInfo)
      console.log('Selected City:', cityInfo)
      console.log('Selected County:', countyInfo)

      // 更新显示文本
      this.inputValueText = `${provinceInfo?.name || ''}/${cityInfo?.name || ''}/${
        countyInfo?.name || ''
      }`

      // // 更新 divisionId
      // const provinceId = provinceInfo?.id || ''
      // const cityId = cityInfo?.id || ''
      // const countyId = countyInfo?.id || ''

      // this.query.divisionId = `${provinceId}${cityId ? ',' + cityId : ''}${
      //   countyId ? ',' + countyId : ''
      // }`
      // Update divisionId to only use the last selected ID
      const provinceId = provinceInfo?.id || ''
      const cityId = cityInfo?.id || ''
      const countyId = countyInfo?.id || ''

      // Only use the ID from the lowest selected level
      this.query.divisionId = countyId || cityId || provinceId

      // name === 全部的时候  this.query.divisionLevel =
      // 省 1 市 2 区3
      if (selectedProvince === '全部') {
        this.query.divisionLevel = 1
      } else if (selectedCity === '全部') {
        this.query.divisionLevel = 2
      } else if (selectedCounty === '全部') {
        this.query.divisionLevel = 3
      } else {
        this.query.divisionLevel = 3
      }
      console.log(this.query.divisionId, 'this.divisionId')

      console.log('selectedCity---------', selectedCity)

      if (selectedProvince === '全部' || selectedCity === '全部' || selectedCounty === '全部') {
        delete this.query.divisionId
      }
      this.pageNo = 1
      this.teamList = []
      this.getTeamList()
      this.show = false
    },
    goDetail(item) {
      uni.navigateTo({
        url: `/pages/assistant/team-info?record=${JSON.stringify(item)}&tabType=${this.tabIndex}`
      })
    },
    // 获取团队成员
    async getTeamList() {
      this.query.pageNo = this.pageNo
      this.query.pageSize = this.pageSize
      const res = await getPageAssistant(this.query)
      if (res.result.records.length < this.pageSize) {
        this.loadMoreStatus = 'noMore'
      } else {
        this.loadMoreStatus = 'more'
      }
      // this.total = res.result.total
      this.teamList = this.teamList.concat(res.result.records)
      let index = Math.floor(Math.random() * 10)
      // this.phones = `tel:${this.teamList[index].assistantTel}`
    },
    changeType(index, id) {
      this.currentType = index
      this.teamList = []
      if (index === 1) {
        this.query['isAll'] = 1
      } else if (index === 0) {
        delete this.query['isAll']
        delete this.query['itemId']
      } else {
        {
          delete this.query['isAll']
          this.query['itemId'] = id
        }
      }
      this.getTeamList()
    },
    search(val) {
      this.query['keyword'] = val
      this.pageNo = 1
      this.teamList = []
      this.getTeamList()
    },
    clear() {
      delete this.query['keyword']
      this.getTeamList()
    },
    getCleanedCode() {
      let query = ''
      // 获取传统模式的查询参数（?code=xxx&openId=xxx）
      if (window.location.search) {
        query = window.location.search.substring(1)
      }
      // 获取 hash 模式的查询参数（#/path?code=xxx&openId=xxx）
      else if (window.location.hash.includes('?')) {
        query = window.location.hash.split('?')[1]
      }
      // 如果没有找到 query，返回 false
      if (!query) {
        return false
      }
      // 解析查询参数
      const vars = query.split('&')
      let code = ''

      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=')
        const key = decodeURIComponent(pair[0])
        const value = decodeURIComponent(pair[1])

        if (key === 'code') {
          code = value
          break // 找到后可以提前结束循环
        }
      }
      // 去除 code 中的空格和特殊字符
      if (code) {
        // 去除空格
        // let cleanedCode = code.replace(/\s*/g, '')
        // 处理特殊字符
        // cleanedCode = encodeURIComponent(cleanedCode)

        let cleanedCode = code
        return cleanedCode
      }

      return false
    },
    // 认证登录成功
    async wxLoginSuccess(token) {
      newSetToken(token)
      const res = await checkToken(token)
      const avatar = res.result.userInfo.avatar
      const nickname = res.result.userInfo.nickName
      this.$store.commit('SET_NAME', nickname)
      this.$store.commit('SET_AVATAR', avatar)
      this.$store.commit('SET_USERINFO', res.result.userInfo)
      this.$store.commit('setRoleId', 1)
      uni.setStorageSync('loginType', 1)
    }
  },
  computed: {}
}
</script>
<style>
page {
  background-color: #f4f8fc;
  padding: 30rpx 20rpx;
}
</style>
<style lang="scss" scoped>
.back_box {
  height: 60rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  img {
    width: 28rpx;
    height: 40rpx;
    transform: rotate(180deg);
    margin-right: 10rpx;
  }
}
.main_box {
  padding: 20rpx;
  min-height: 300rpx;
  background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
  border-radius: 32rpx;

  .title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 36rpx;
    color: #141414;
    line-height: 50rpx;
    text-align: left;
    font-style: normal;
  }

  .title2 {
    display: flex;
    align-items: center;
    margin-top: 20rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 30rpx;
    line-height: 42rpx;
    text-align: left;
    font-style: normal;
    color: #444444;

    .select {
      color: #057ffe;

      img {
        width: 21rpx;
        height: 25rpx;
      }
    }

    .select2 {
      margin-left: 20rpx;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -20rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 55rpx;
        height: 10rpx;
        background: url('../../static/images/assistant/select.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .line {
    width: 100%;
    height: 1rpx;
    background: #e7f0ff;
    margin: 60rpx 0;
  }

  .list {
    display: flex;
    // justify-content: space-between;
  }

  .city {
    flex: 1;
    height: 600rpx;
    overflow: auto;

    & > div {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 30rpx;
      color: #666666;
      line-height: 42rpx;
      text-align: left;
      font-style: normal;
      margin-bottom: 30rpx;
    }

    .active {
      color: #057ffe;
    }
  }

  .btns {
    display: flex;
    justify-content: space-between;
    margin-top: 30rpx;

    & > div {
      width: 282rpx;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 8rpx;
      border: 2rpx solid #057ffe;
      text-align: center;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 34rpx;
      color: #057ffe;
    }

    & > div:nth-child(2) {
      background: #057ffe;
      color: #fff;
    }
  }

  .banner-box {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .banner {
    width: max-content;
    height: 214rpx;
  }

  .title3 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 36rpx;
    color: #141414;
    line-height: 50rpx;
    position: relative;
    margin: 30rpx 0;

    &::after {
      content: '';
      position: absolute;
      top: 0rpx;
      left: 0px;
      width: 27rpx;
      height: 27rpx;
      background: url('../../static/images/assistant/3.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .time {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    text-align: left;
    font-style: normal;

    & > span {
      margin-right: 10rpx;
    }
  }

  .popup-content {
    width: 600rpx;
    height: 299rpx;
    background: #ffffff;
    border-radius: 16rpx;
    position: relative;

    .title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 30rpx;
      color: #444444;
      line-height: 42rpx;
      text-align: right;
      font-style: normal;
      position: absolute;
      z-index: 3;
      top: 26%;
      left: 18%;
      width: 380rpx;
      text-align: center;
    }

    .btn {
      width: 474rpx;
      height: 76rpx;
      line-height: 76rpx;
      background: linear-gradient(310deg, #0cb6ff 0%, #057ffe 100%);
      border-radius: 36rpx;
      text-align: center;
      color: #fff;
      position: absolute;
      z-index: 3;
      top: 53%;
      left: 18%;
      width: 380rpx;
    }

    &::before {
      content: '';
      width: 106%;
      height: 95px;
      position: absolute;
      z-index: 2;
      top: -10px;
      left: -10px;
      background: url('/static/images/assistant/bq.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}

.answerBoxs {
  height: 500rpx;

  .content {
    width: 600rpx;
    margin: 32rpx;
    padding: 55rpx 34rpx 30rpx 34rpx;
    display: flex;
    gap: 30rpx;
    flex-direction: column;
    // align-items: center;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
    border-radius: 32rpx;
    position: relative;

    .btns {
      .btn {
        width: 474rpx;
        height: 76rpx;
        background: linear-gradient(310deg, #0cb6ff 0%, #057ffe 100%);
        border-radius: 36rpx;
        font-weight: 500;
        font-size: 30rpx;
        color: #ffffff;
        line-height: 76rpx;
        text-align: center;
        font-style: normal;
        margin: 0 auto;
      }
    }

    .report_title {
      width: 100%;
      height: 180rpx;
      position: absolute;
      top: 0;
      left: 0;
    }

    .entering {
      padding-top: 80rpx;
    }

    .cross {
      width: 36rpx;
      height: 36rpx;
      position: absolute;
      top: 42rpx;
      right: 35rpx;
    }

    .intro {
      position: relative;
      width: 618rpx;
      height: 200rpx;
      padding: 0 5rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #444444;
      line-height: 44rpx;
      font-style: normal;

      .intro_text {
        position: absolute;
      }
    }

    .cell {
      // height: 180rpx;
      // margin-bottom: 30rpx;
      display: flex;
      align-items: center;
      padding-top: 80rpx;
    }

    .text {
      width: 610rpx;
      height: 590rpx;
      background: #f5f7fb;
      border-radius: 20rpx;
      padding: 30rpx;

      &:hover {
        border: solid 1rpx rgba(200, 200, 200, 0.7);
      }

      &.row {
        display: flex;
        height: 100%;
      }
    }
  }
}
.b34 {
  font-weight: 600;
  font-size: 34rpx;
  color: #444444;
  position: absolute;
}
</style>
