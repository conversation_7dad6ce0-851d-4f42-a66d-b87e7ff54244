<template>
  <view>
    <view :style="{ fontSize: fontSizeMedium }" class="main-box">
      <view
        class="progress-box2"
        @click="
          $tab.navigateTo(`/pages/assistant/process?itemList=${JSON.stringify(record.itemList)}`)
        "
      >
        <!-- <image class="bg" src="@/static/images/assistant/jd.png"></image> -->
        <view class="title">办理进度</view>
        <view class="box">
          <view class="progress" :style="{ width: currentProgress + '%' }">
            <view :style="{ right: currentProgress === 0 ? '-106rpx' : '' }" class="text"
              >{{ currentProgress }}%</view
            >
          </view>
        </view>
      </view>
      <view v-if="record.currentItem">
        <view class="card">
          <image class="bg_t" src="@/static/images/assistant/bg_t.png"></image>
          <view class="card-box">
            <view class="title">业务信息</view>
            <view class="cell">
              <view class="label">业务编号</view>
              <view class="value">{{ record.recordId }}</view>
            </view>
            <view class="cell">
              <view class="label">办理事项</view>
              <view class="value">
                <text>{{ record.currentItem.itemName }}</text>
              </view>
            </view>
            <view class="cell">
              <view class="label">申请时间</view>
              <view class="value">{{ record.applyTime }} </view>
            </view>
          </view>
          <image class="bg_b" src="@/static/images/assistant/bg_b.png"></image>
        </view>
        <view class="card">
          <image class="bg_t" src="@/static/images/assistant/bg_t.png"></image>
          <div class="card-box">
            <view class="title">业务员信息</view>
            <view class="cell">
              <view class="label">姓名</view>
              <view class="value">{{ record.assistantName }} </view>
            </view>
            <view class="cell">
              <view class="label">联系方式</view>
              <view class="value">{{ record.assistantTel }} </view>
            </view>
            <view class="cell">
              <view class="label">服务位置</view>
              <view class="value">{{ record.serviceLocal }} </view>
            </view>
            <view class="cell">
              <view class="label">窗口位置</view>
              <view class="value">{{ record.windowLocal }} </view>
            </view>
          </div>
          <image class="bg_b" src="@/static/images/assistant/bg_b.png"></image>
        </view>
        <view class="card" v-for="(item, index) in record.currentItem.menuList" :key="index">
          <image class="bg_t" src="@/static/images/assistant/bg_t.png"></image>
          <div class="card-box">
            <view class="title">{{ item.menuName }}</view>
            <view class="cell3" v-for="(menu, i) in item.itemFieldList" :key="i">
              <view v-if="menu.fieldType == 'sck'" class="skc_box">
                <view style="display: flex; align-items: center" class="label">
                  <text v-if="menu.isRequired == '1'" style="color: red; margin-right: 6rpx"
                    >*</text
                  >
                  <text> {{ menu.fieldName }}</text>
                </view>
                <view class="value value2">
                  <view style="display: flex; align-items: center; justify-content: space-between">
                    <view
                      class="download"
                      v-if="menu.templateFiles && menu.templateFiles.length > 0"
                      @click="downFile(menu.templateFiles[0])"
                      hover-class="hover_class"
                    >
                      <image src="/static/images/assistant/download.png"></image>
                      模板下载</view
                    >
                    <view
                      v-if="menu.fieldType == 'sck'"
                      class="upload_btn"
                      hover-class="hover_class"
                    >
                      <view class="upload" @click="chooseFile2(menu.itemFieldId)">
                        <image src="/static/images/assistant/upload.png"></image>
                        <text>文件上传</text>
                      </view>
                    </view>
                  </view>
                  <view v-if="menu.fieldType == 'sck' && fieldFileList[menu.itemFieldId]">
                    <view
                      v-for="(file, fileIndex) in fieldFileList[menu.itemFieldId]"
                      :key="index"
                      style="margin: 20rpx 0"
                    >
                      <view
                        style="display: flex; justify-content: space-between; align-items: center"
                      >
                        <view
                          style="display: flex; align-items: center"
                          @click="
                            () => {
                              downFile(file, 1)
                            }
                          "
                        >
                          <u-icon name="attach" />
                          {{ file.name }}
                        </view>
                        <image
                          class="delete"
                          src="/static/images/assistant/sc.png"
                          @click="
                            () => {
                              deleteFile2(menu.itemFieldId, fileIndex, file.id)
                            }
                          "
                        ></image>
                      </view>
                      <view class="progress-box">
                        <progress
                          :percent="file.progress"
                          active
                          :duration="10"
                          show-info
                          stroke-width="3"
                        />
                      </view>
                    </view>
                  </view>
                </view>
              </view>
              <!-- 没有上传框 -->
              <view v-else class="no_sck_box">
                <view style="display: flex; align-items: center" class="label">
                  <text v-if="menu.isRequired == '1'" style="color: red; margin-right: 6rpx"
                    >*</text
                  >
                  <text> {{ menu.fieldName }}</text>
                </view>
                <view class="value">
                  <view>
                    <uni-datetime-picker
                      v-if="menu.fieldType == 'rqk'"
                      type="datetime"
                      return-type="string"
                      v-model="menu.fieldValue"
                      :disabled="!isEdit"
                    ></uni-datetime-picker>
                    <input
                      v-if="menu.fieldType == 'wbk'"
                      v-model="menu.fieldValue"
                      placeholder="请输入"
                      type="text"
                      :disabled="!isEdit"
                    />
                    <uni-data-select
                      v-if="menu.fieldType == 'xlk'"
                      v-model="menu.fieldValue"
                      :localdata="fieldData[menu.fieldName]"
                      :disabled="!isEdit"
                    ></uni-data-select>
                  </view>
                </view>
              </view>
            </view>
          </div>
          <image class="bg_b" src="@/static/images/assistant/bg_b.png"></image>
        </view>
        <!-- 补充 -->
        <view
          class="card"
          v-for="(item, cardIndex) in record.currentItem.supplementMenuList"
          :key="`card-${cardIndex}`"
        >
          <image class="bg_t" src="@/static/images/assistant/bg_t.png"></image>
          <div class="card-box">
            <view class="title">{{ item.menuName }}</view>
            <view
              class="cell3"
              v-for="(menu, menuIndex) in item.itemFieldList"
              :key="`menu-${cardIndex}-${menuIndex}`"
            >
              <view v-if="menu.fieldType == 'sck'" class="skc_box">
                <view style="display: flex; align-items: center" class="label">
                  <text v-if="menu.isRequired == '1'" style="color: red; margin-right: 6rpx"
                    >*</text
                  >
                  <text> {{ menu.fieldName }}</text>
                </view>
                <view class="value value2">
                  <view style="display: flex; align-items: center; justify-content: space-between">
                    <view
                      class="download"
                      v-if="menu.templateFiles && menu.templateFiles.length > 0"
                      @click="downFile(menu.templateFiles[0])"
                      hover-class="hover_class"
                    >
                      <image src="/static/images/assistant/download.png"></image>
                      模板下载
                    </view>
                    <view
                      v-if="menu.fieldType == 'sck'"
                      class="upload_btn"
                      hover-class="hover_class"
                    >
                      <view class="upload" @click="chooseFile21(menu.id)">
                        <image src="/static/images/assistant/upload.png"></image>
                        <text>文件上传</text>
                      </view>
                    </view>
                  </view>
                  <view v-if="menu.fieldType == 'sck' && fieldFileList2[menu.id]">
                    <view
                      v-for="(file, fileIndex) in fieldFileList2[menu.id]"
                      :key="`file-${menu.id}-${fileIndex}`"
                      style="margin: 20rpx 0"
                    >
                      <view
                        style="display: flex; justify-content: space-between; align-items: center"
                      >
                        <view
                          style="display: flex; align-items: center"
                          @click="
                            () => {
                              downFile(file, 1)
                            }
                          "
                        >
                          <u-icon name="attach" />
                          {{ file.name }}
                        </view>
                        <image
                          class="delete"
                          src="/static/images/assistant/sc.png"
                          @click="
                            () => {
                              deleteFile2(menu.itemFieldId, fileIndex, file.id)
                            }
                          "
                        ></image>
                      </view>
                      <view class="progress-box">
                        <progress
                          :percent="file.progress"
                          active
                          :duration="10"
                          show-info
                          stroke-width="3"
                        />
                      </view>
                    </view>
                  </view>
                </view>
              </view>
              <!-- 没有上传框 -->
              <view v-else class="no_sck_box">
                <view style="display: flex; align-items: center" class="label">
                  <text v-if="menu.isRequired == '1'" style="color: red; margin-right: 6rpx"
                    >*</text
                  >
                  <text> {{ menu.fieldName }}</text>
                </view>
                <view class="value">
                  <view>
                    <uni-datetime-picker
                      v-if="menu.fieldType == 'rqk'"
                      type="datetime"
                      return-type="string"
                      v-model="menu.fieldValue"
                      :disabled="!isEdit"
                    ></uni-datetime-picker>
                    <input
                      v-if="menu.fieldType == 'wbk'"
                      v-model="menu.fieldValue"
                      placeholder="请输入"
                      type="text"
                      :disabled="!isEdit"
                    />
                    <uni-data-select
                      v-if="menu.fieldType == 'xlk'"
                      v-model="menu.fieldValue"
                      :localdata="fieldData2[menu.fieldName]"
                      :disabled="!isEdit"
                    ></uni-data-select>
                  </view>
                </view>
              </view>
            </view>
          </div>
          <image class="bg_b" src="@/static/images/assistant/bg_b.png"></image>
        </view>
        <view class="card" v-if="record.currentItem.proxyFileList">
          <image class="bg_t" src="@/static/images/assistant/bg_t.png"></image>
          <div class="card-box">
            <view class="title">帮办委托书</view>
            <view class="cell4">
              <view>
                <!-- <text class="sub_title">帮办委托书</text> -->
                <view class="btns">
                  <view
                    v-if="
                      record &&
                      record.currentItem &&
                      record.currentItem.proxyFileTemplate &&
                      record.currentItem.proxyFileTemplate.length > 0
                    "
                    class="download"
                    @click="downFile(record.currentItem.proxyFileTemplate[0])"
                    hover-class="hover_class"
                  >
                    <image src="/static/images/assistant/download.png"></image>
                    模板下载</view
                  >
                  <view @click="chooseFile" class="upload" hover-class="hover_class">
                    <image src="/static/images/assistant/upload.png"></image>
                    文件上传</view
                  >
                </view>
              </view>
              <view :style="{ fontSize: fontSizeSmall }">
                支持扩展名： .docx .xlsx .pptx .jpg .png
              </view>
              <view v-for="(item, index) in fileList" :key="index" style="margin: 20rpx 0">
                <view style="display: flex; justify-content: space-between; align-items: center">
                  <view style="display: flex; align-items: center" @click="downFile(item, 1)">
                    <image class="fj" src="/static/images/assistant/fj.png"></image>
                    {{ item.name }}({{ item.filedType }})
                  </view>
                  <image
                    class="delete"
                    src="/static/images/assistant/sc.png"
                    @click="deleteFile(index, item.id)"
                  ></image>
                </view>
                <view class="progress-box">
                  <progress
                    :percent="item.progress"
                    active
                    :duration="5"
                    show-info
                    stroke-width="3"
                  />
                </view>
              </view>
            </view>
          </div>
          <image class="bg_b" src="@/static/images/assistant/bg_b.png"></image>
        </view>
        <!-- <view class="card">
          <image class="bg_t" src="@/static/images/assistant/bg_t.png"></image>
          <div class="card-box">
            <view class="title">其他</view>
            <view class="cell2">
              <view class="label">备注</view>
              <view class="textarea value">
                <textarea
                  :disabled="!isEdit"
                  placeholder="请输入备注信息"
                  v-model="record.currentItem.remark"
                ></textarea>
              </view>
            </view>
          </div>
          <image class="bg_b" src="@/static/images/assistant/bg_b.png"></image>
        </view> -->
      </view>
      <view
        class="btn-box"
        v-if="
          userInfo.isAssist == 1 &&
          !isEdit &&
          record.needAssign == 0 &&
          loginType == 0 &&
          (record.currentItem.itemStatus === 'dcl' ||
            record.currentItem.itemStatus === 'dtj' ||
            record.currentItem.itemStatus === 'th')
        "
      >
        <view @click="handleEdit">编辑</view>
        <view @click="openPopup(1)">退回</view>
        <view @click="openPopup(2)">通过</view>
      </view>
      <view
        class="btn-box"
        v-if="isEdit && (loginType == 1 || (userInfo.isAssist == 1 && loginType == 0))"
      >
        <view @click="handleReset">重置</view>
        <view @click="submit(0)">保存</view>
        <view @click="submit(1)">提交</view>
        <view v-show="isEdit && loginType == 0" @click="isEdit = false">取消</view>
        <view @click="handleBack">返回</view>
      </view>
      <!-- 悬浮按钮 -->
      <u-transition
        :show="showAnimation"
        mode="fade-right"
        :duration="500"
        timingFunction="ease-in-out"
      >
        <view class="suspension">
          <image
            @click="
              $tab.navigateTo(
                '/pages/interaction/im/chat?recordId=' +
                  record.recordId +
                  '&title=' +
                  record.assistantName +
                  '&assistantId=' +
                  userInfo.id +
                  '&bbyAvatar=' +
                  record.assistantAvatar || ''
              )
            "
            src="/static/images/assistant/zx.png"
          ></image>
          <image
            v-if="record.currentItem.hasNotification == 1"
            @click="$tab.navigateTo('/pages/assistant/oneTime?item=' + record.currentItem.itemId)"
            src="/static/images/assistant/zn.png"
          />
        </view>
      </u-transition>
    </view>

    <uni-popup
      ref="popup"
      v-if="record && record.currentItem && record.currentItem.instructionManagement"
    >
      <view class="popup-content">
        <image class="bg" src="/static/images/assistant/p-bg.png"></image>
        <view class="title">{{ record.currentItem.instructionManagement.title }}</view>
        <view class="content" v-html="record.currentItem.instructionManagement.content"> </view>
        <view class="footer" @click="$refs.popup.close()">我已知晓</view>
      </view>
    </uni-popup>
    <uni-popup ref="popup2">
      <view class="popup-content2">
        <view class="popup-bg"> <image src="@/static/images/admin/p-bg.png"></image></view>
        <view class="popup-title">
          {{ popupType === 1 ? '退回' : '通过' }}
        </view>
        <view class="popup-body">
          <view>
            <view class="label">
              {{ popupType === 1 ? '退回理由' : '通过理由' }}
            </view>
            <view class="textarea">
              <textarea placeholder="(100字以内)" v-model="popupForm.reason"></textarea>
            </view>
          </view>
          <view class="btns">
            <view @click="closePopup">返回</view>
            <view @click="popupSubmit">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { saveRecord, submitRecord, getDictItem, deleteFile } from '@/api/assistant/index.js'
import { back, pass } from '@/api/home/<USER>'
import config from '@/config'
import navbar from '@/components/Navbar/index.vue'
import { getAccessToken } from '@/utils/auth'
export default {
  components: {
    navbar
  },
  data() {
    return {
      record: {},
      headtitle: '业务办理',
      Props: {
        imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: '', //导航高度(动态获取传参)
        bgColor: '', //导航栏背景色,不传参则默认#9CF
        capsuleTop: '', //胶囊顶部距离(动态获取传参)
        textColor: '', //导航标题字体颜色(不传默认#FFF)
        iconColor: '', //icon图标颜色(不传默认#FFF)
        blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
        backText: '' //默认字体(返回)
      },
      formData: {
        id: '',
        itemId: '',
        menuList: [],
        recordId: '',
        remark: '',
        recordFileList: []
      },
      fileList: [],
      currentItemIndex: 0,
      baseUrl: config.baseUrl,
      show: false,
      columns: [],
      fieldData: {},
      fieldData2: {},
      // 流程上传文件列表
      fieldFileList: {},
      // 补充字段
      fieldFileList2: {},
      userInfo: {},
      isEdit: false,
      popupForm: {
        businessLevel: '',
        recordItemId: '',
        reason: '',
        recordId: '',
        userName: ''
      },
      loginType: '',
      popupType: 1,
      isProgressShow: true,
      currentProgress: 0,
      targetProgress: 100,
      animationDuration: 1000,
      extensions: ['doc', 'pdf', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'jpg', 'png'],
      showAnimation: false
    }
  },
  async onLoad(option) {
    // 用户信息
    this.userInfo =
      typeof this.$store.state.user.userInfo == 'string'
        ? JSON.parse(this.$store.state.user.userInfo)
        : this.$store.state.user.userInfo
    this.loginType = uni.getStorageSync('loginType')
    console.log('loginType', this.loginType)

    // 获取流程数据
    // this.record = JSON.parse(option.record)
    try {
      // 解码传入的数据
      const decodedRecord = decodeURIComponent(option.record)
      // 解析 JSON
      this.record = JSON.parse(decodedRecord)
    } catch (error) {
      console.error('JSON 解析错误:', error)
      uni.showToast({
        icon: 'none',
        title: '数据加载错误，请重试'
      })
    }
    // 须知

    if (this.loginType == 1 && this.record.currentItem.instructionManagement) {
      setTimeout(() => {
        this.$refs.popup.open('center')
      }, 500)
    }
    // 当前进度条
    this.targetProgress = Number(this.record.transactProgress)
    this.animateProgress()

    // 用户是否可以编辑
    if (
      this.loginType == 1 &&
      (this.record.currentItem.itemStatus === 'dtj' || this.record.currentItem.itemStatus === 'th')
    ) {
      this.isEdit = true
    }
    // 下个办理事项
    this.record['isCurrentItem'] = true
    this.record['currentItemIndex'] = this.record.itemList.findIndex(
      (item) => item.itemId === this.record.currentItem.itemId
    )
    if (this.record.currentItemIndex === this.record.itemList.length - 1) {
      this.record['isCurrentItem'] = false
    } else {
      this.record['isCurrentItem'] = true
      this.record['currentItemIndex'] = this.record.currentItemIndex + 1
    }
    // 填充字典字段
    this.fieldData = {}
    this.fieldData2 = {}
    await Promise.all(
      this.record.currentItem.menuList.map((menu) => {
        return Promise.all(
          menu.itemFieldList.map(async (item) => {
            if (item.dictionaryEncoding) {
              const res = await getDictItem({ dictCode: item.dictionaryEncoding })
              this.$set(this.fieldData, item.fieldName, res.result)
            }
          })
        )
      })
    )
    if (this.record.currentItem.supplementMenuList) {
      await Promise.all(
        this.record.currentItem.supplementMenuList.map((menu) => {
          return Promise.all(
            menu.itemFieldList.map(async (item) => {
              if (item.dictionaryEncoding) {
                const res = await getDictItem({ dictCode: item.dictionaryEncoding })
                this.$set(this.fieldData2, item.fieldName, res.result)
              }
            })
          )
        })
      )
    }

    // 填充 姓名和手机号
    this.record.currentItem.menuList.map((menu) => {
      menu.itemFieldList.map((field) => {
        if (field.fieldName === '联系人姓名' && !field.fieldValue) {
          field.fieldValue = this.userInfo.userRealName
        }
        if (field.fieldName === '联系方式' && !field.fieldValue) {
          field.fieldValue = this.userInfo.mobilePhone
        }
      })
    })
    //  回填流程附件列表
    this.record.currentItem.recordFileList.map((item) => {
      if (item.fileUrl) {
        let fileList = []
        fileList.push({
          name: item.fieldName,
          url: item.fileUrl,
          progress: 100,
          filedType: item.fileFormat,
          id: item.id
        })
        this.$set(this.fieldFileList, item.itemFieldId, fileList)
      }
    })
    //  回填补充字段附件列表
    if (this.record.currentItem.supplementFileList) {
      this.record.currentItem.supplementFileList.map((item) => {
        if (item.fileUrl) {
          let fileList = []
          fileList.push({
            name: item.fileName,
            url: item.fileUrl,
            progress: 100,
            filedType: item.fileFormat,
            id: item.recordFieldId
          })
          this.$set(this.fieldFileList2, item.recordFieldId, fileList)
        }
      })
    }
    // 回填委托书附件列表
    if (this.record.currentItem.proxyFileList && this.record.currentItem.proxyFileList.length > 0) {
      this.record.currentItem.proxyFileList.map((item) => {
        if (item.fileUrl) {
          this.fileList.push({
            name: item.fileName,
            url: item.fileUrl,
            progress: 100,
            filedType: item.fileFormat
          })
        }
      })
    }

    // 表单参数
    this.formData.id = this.record.currentItem.id || ''
    this.formData.itemId = this.record.currentItem.itemId || ''
    // this.formData.proxyFile = this.record.proxyFile || ''
    this.formData.recordId = this.record.currentItem.recordId || ''
    this.formData.remark = this.record.currentItem.remark || ''
    console.log('=---------------------------------', this.formData)

    this.showAnimation = true
  },
  onReady() {},
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge
    }
  },
  methods: {
    animateProgress() {
      const startTime = Date.now()
      const step = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / this.animationDuration, 1)
        this.currentProgress = Math.floor(progress * this.targetProgress)
        if (progress < 1) {
          setTimeout(step, 16)
        }
      }
      setTimeout(step, 16)
    },
    handleEdit() {
      this.isEdit = true
    },
    openPopup(type) {
      this.popupForm.businessLevel = this.record.businessLevel
      this.popupForm.recordItemId = this.record.currentItem.id
      this.popupForm.recordId = this.record.recordId
      this.popupForm.userName = this.record.assistantId
      this.popupType = type
      this.$refs.popup2.open('center')
    },
    closePopup() {
      this.$refs.popup2.close()
    },
    deleteFile(index) {
      if (!this.isEdit) {
        uni.showToast({
          icon: 'none',
          title: '请先点击编辑!'
        })
        return
      }
      this.fileList.splice(index, 1)
    },
    deleteFile2(itemFieldId, index) {
      if (!this.isEdit) {
        uni.showToast({
          icon: 'none',
          title: '请先点击编辑!'
        })
        return
      }
      this.fieldFileList[itemFieldId].splice(index, 1)
    },
    deleteFile21(itemFieldId, index) {
      if (!this.isEdit) {
        uni.showToast({
          icon: 'none',
          title: '请先点击编辑!'
        })
        return
      }
      this.fieldFileList2[itemFieldId].splice(index, 1)
    },
    deleteFile3(itemFieldId, index) {
      if (!this.isEdit) return
      this.record.currentItem.menuList.map((menu) => {
        menu.itemFieldList.map((field) => {
          if (field.itemFieldId == itemFieldId) {
            field.fieldValue = ''
          }
        })
      })
    },
    downFile(template, type = 0) {
      console.log(template, 'template----------------')
      if (
        (template.fileFormat || template.filedType) === 'png' ||
        (template.fileFormat || template.filedType) === 'jpg'
      ) {
        if (type == 1) {
          uni.previewImage({
            urls: [this.baseUrl + '/boot/' + template.url]
          })
        } else {
          uni.previewImage({
            urls: [this.baseUrl + '/' + template.fileUrl]
          })
        }
        return
      }
      uni.showLoading({
        title: '正在打开...'
      })
      uni.downloadFile({
        url:
          type == 1
            ? this.baseUrl + '/boot/' + template.url
            : this.baseUrl + '/boot/' + template.fileUrl,
        header: {
          Authorization: getAccessToken()
        },
        // filePath: `${uni.env.USER_DATA_PATH}/${template.fileName || template.name}.${
        //   template.fileFormat || template.filedType
        // }`,
        success: (res) => {
          if (res.statusCode === 200) {
            console.log(template.fileFormat, '22222')
            console.log(template.filedType, '33333')
            console.log(res, '00000000')
            uni.openDocument({
              filePath: res.tempFilePath,
              fileType: template.fileFormat || template.filedType,
              showMenu: true,
              success: function (res2) {
                uni.hideLoading()
              },
              fail: function (res2) {
                uni.$u.toast('打开文档失败,请检查格式是否正确')
              }
            })
          }
        }
      })
    },
    // 委托书附件上传
    chooseFile() {
      if (!this.isEdit) return
      if (this.fileList.length > 0) {
        uni.showToast({
          icon: 'none',
          title: '最多上传一个附件'
        })
        return
      }
      uni.chooseFile({
        count: 1,
        type: 'file',
        extensions: this.extensions,
        success: (res) => {
          const str = res.tempFiles[0].name.substring(0, res.tempFiles[0].name.lastIndexOf('.'))
          const fileType = res.tempFiles[0].name.substring(
            res.tempFiles[0].name.lastIndexOf('.') + 1
          )
          if (!this.extensions.includes(fileType)) {
            uni.showToast({
              icon: 'none',
              title: '文件格式不正确'
            })
            return
          }
          this.fileList.push({
            name: str,
            filedType: fileType,
            progress: 0,
            recordId: this.record.recordId,
            itemId: this.record.currentItem.itemId,
            fileName: str,
            fileType: '2',
            fileSize: res.tempFiles[0].size
            // recordFieldId: this.record.currentItem.recordId
          })
          uni.uploadFile({
            header: {
              Authorization: uni.getStorageSync('ACCESS_TOKEN')
            },
            url: config.baseUrl + '/boot' + '/miniapp/api/assist/upload',
            filePath: res.tempFiles[0].path,
            name: 'file',
            formData: {
              biz: `record/${this.record.recordId}/${this.record.currentItem.itemId}`
            },
            success: (uploadFileRes) => {
              this.fileList[this.fileList.length - 1].progress = 100
              this.fileList[this.fileList.length - 1].url = JSON.parse(uploadFileRes.data).message
              console.log('委托书上传成功', this.fileList)
            }
          })
        }
      })
    },
    // 流程附件上传
    chooseFile2(id) {
      if (!this.isEdit) return
      if (this.fieldFileList[id] && this.fieldFileList[id].length > 0) {
        uni.showToast({
          icon: 'none',
          title: '最多上传一个附件'
        })
        return
      }
      uni.chooseFile({
        count: 1,
        type: 'file',
        extensions: this.extensions,
        success: (res) => {
          console.log('选择了文件', res)
          const fileName = res.tempFiles[0].name
          const str = fileName.substring(0, fileName.lastIndexOf('.'))
          const fileType = fileName.substring(fileName.lastIndexOf('.') + 1)
          if (!this.extensions.includes(fileType)) {
            uni.showToast({
              icon: 'none',
              title: '文件格式不正确'
            })
            return
          }
          if (!this.fieldFileList[id]) {
            this.$set(this.fieldFileList, id, [])
          }
          this.fieldFileList[id].push({
            name: str,
            fileFormat: fileType,
            progress: 0,
            recordId: this.record.recordId,
            itemId: this.record.currentItem.itemId,
            fileName: str,
            fileType: '3',
            itemFieldId: id,
            fileSize: res.tempFiles[0].size
          })
          uni.uploadFile({
            header: {
              Authorization: uni.getStorageSync('ACCESS_TOKEN')
            },
            url: config.baseUrl + '/boot' + '/miniapp/api/assist/upload',
            filePath: res.tempFiles[0].path,
            name: 'file',
            formData: {
              biz: `record/${this.record.recordId}/${this.record.currentItem.itemId}`
            },
            success: (uploadFileRes) => {
              const lastFileIndex = this.fieldFileList[id].length - 1
              this.$set(this.fieldFileList[id][lastFileIndex], 'progress', 100)
              this.$set(
                this.fieldFileList[id][lastFileIndex],
                'url',
                JSON.parse(uploadFileRes.data).message
              )
              console.log('流程附件上传成功', this.fieldFileList)
            }
          })
        }
      })
    },
    chooseFile21(id) {
      if (!this.isEdit) return
      if (this.fieldFileList2[id] && this.fieldFileList2[id].length > 0) {
        uni.showToast({
          icon: 'none',
          title: '最多上传一个附件'
        })
        return
      }
      uni.chooseFile({
        count: 1,
        type: 'file',
        extensions: this.extensions,
        success: (res) => {
          console.log('选择了文件', res)
          const fileName = res.tempFiles[0].name
          const str = fileName.substring(0, fileName.lastIndexOf('.'))
          const fileType = fileName.substring(fileName.lastIndexOf('.') + 1)
          if (!this.extensions.includes(fileType)) {
            uni.showToast({
              icon: 'none',
              title: '文件格式不正确'
            })
            return
          }
          if (!this.fieldFileList2[id]) {
            this.$set(this.fieldFileList2, id, [])
          }
          this.fieldFileList2[id].push({
            name: str,
            fileFormat: fileType,
            progress: 0,
            recordId: this.record.recordId,
            itemId: this.record.currentItem.itemId,
            fileName: str,
            fileType: '3',
            itemFieldId: id,
            fileSize: res.tempFiles[0].size
          })
          uni.uploadFile({
            header: {
              Authorization: uni.getStorageSync('ACCESS_TOKEN')
            },
            url: config.baseUrl + '/boot' + '/miniapp/api/assist/upload',
            filePath: res.tempFiles[0].path,
            name: 'file',
            formData: {
              biz: `record/${this.record.recordId}/${this.record.currentItem.itemId}`
            },
            success: (uploadFileRes) => {
              const lastFileIndex = this.fieldFileList2[id].length - 1
              this.$set(this.fieldFileList2[id][lastFileIndex], 'progress', 100)
              this.$set(
                this.fieldFileList2[id][lastFileIndex],
                'url',
                JSON.parse(uploadFileRes.data).message
              )
              console.log('补充字段附件上传成功', this.fieldFileList2)
            }
          })
        }
      })
    },
    // 提交
    async submit(type) {
      // 流程附件回填
      for (let i = 0; i < this.record.currentItem.recordFileList.length; i++) {
        const item = this.record.currentItem.recordFileList[i]
        const fieldFileArr = this.fieldFileList[item.itemFieldId]
        if (fieldFileArr) {
          fieldFileArr.forEach((fieldFile) => {
            item.fileFormat = fieldFile.fileFormat
            item.fileName = fieldFile.name
            item.fileType = fieldFile.fileType
            item.fileUrl = fieldFile.url
            item.fileSize = fieldFile.fileSize
          })
        }
      }
      // 补充字段附件
      if (this.record.currentItem.supplementMenuList) {
        for (let i = 0; i < this.record.currentItem.supplementMenuList.length; i++) {
          const menu = this.record.currentItem.supplementMenuList[i]
          for (let j = 0; j < menu.itemFieldList.length; j++) {
            const field = menu.itemFieldList[j]
            if (field.fieldType === 'sck') {
              const fileList = this.fieldFileList2[field.id] || []
              if (!fileList.length) {
                uni.showToast({
                  icon: 'none',
                  title: `请上传${field.fieldName}`
                })
                return
              }
              field.fieldValue = fileList.map((item) => item.url).join(',')
            }
          }
        }
        for (let i = 0; i < this.record.currentItem.supplementFileList.length; i++) {
          const item = this.record.currentItem.supplementFileList[i]
          const fieldFileArr = this.fieldFileList2[item.recordFieldId]
          if (fieldFileArr) {
            fieldFileArr.forEach((fieldFile) => {
              item.fileFormat = fieldFile.fileFormat
              item.fileName = fieldFile.name
              item.fileType = fieldFile.fileType
              item.fileUrl = fieldFile.url
              item.fileSize = fieldFile.fileSize
            })
          }
        }
        for (let i = 0; i < this.record.currentItem.supplementMenuList.length; i++) {
          const menu = this.record.currentItem.supplementMenuList[i]
          for (let j = 0; j < menu.itemFieldList.length; j++) {
            const field = menu.itemFieldList[j]
            if (field.isRequired == '1' && !field.fieldValue) {
              uni.showToast({
                icon: 'none',
                title: `${field.fieldName}为必填项`
              })
              return
            }
          }
        }
        this.formData.supplementMenuList = this.record.currentItem.supplementMenuList
        this.formData.supplementFileList = this.record.currentItem.supplementFileList
      }
      // 帮办委托书附件回填
      if (this.record.currentItem.proxyFileList) {
        if (this.fileList.length < 1) {
          uni.showToast({
            icon: 'none',
            title: '请上传帮办委托书'
          })
          return
        }
        this.record.currentItem.proxyFileList.map((item) => {
          this.fileList.map((file) => {
            if (item.itemId === file.itemId) {
              item.fileFormat = file.filedType
              item.fileName = file.name
              item.fileType = file.fileType
              item.fileUrl = file.url
              item.fileSize = file.fileSize
            }
          })
        })
        this.formData['proxyFileList'] = this.record.currentItem.proxyFileList
      }
      for (let i = 0; i < this.record.currentItem.menuList.length; i++) {
        const menu = this.record.currentItem.menuList[i]
        for (let j = 0; j < menu.itemFieldList.length; j++) {
          const field = menu.itemFieldList[j]
          if (field.fieldType === 'sck') {
            const fileList = this.fieldFileList[field.itemFieldId] || []
            if (!fileList.length) {
              uni.showToast({
                icon: 'none',
                title: `请上传${field.fieldName}`
              })
              return
            }
            field.fieldValue = fileList.map((item) => item.url).join(',')
          }
        }
      }
      // 流程
      for (let i = 0; i < this.record.currentItem.menuList.length; i++) {
        const menu = this.record.currentItem.menuList[i]
        for (let j = 0; j < menu.itemFieldList.length; j++) {
          const field = menu.itemFieldList[j]
          if (field.isRequired == '1' && !field.fieldValue) {
            uni.showToast({
              icon: 'none',
              title: `${field.fieldName}为必填项`
            })
            return
          }
        }
      }
      this.formData.recordFileList = this.record.currentItem.recordFileList

      this.formData.remark = this.record.currentItem.remark
      this.formData.menuList = this.record.currentItem.menuList

      this.formData.itemName = this.record.currentItem.itemName
      console.log('提交数据', this.formData)
      let res = null
      if (type == 0) {
        res = await saveRecord(this.formData)
      }
      if (type == 1) {
        res = await submitRecord(this.formData)
      }
      if (res.success) {
        uni.showToast({
          icon: 'none',
          title: res.result
        })
        setTimeout(() => {
          if (this.loginType == 0) {
            this.isEdit = false
          } else {
            if (type == 0) {
              uni.redirectTo({
                url: `/pages/mine/handlingRecord?type=0`
              })
            }
            if (type == 1) {
              uni.redirectTo({
                url: `/pages/assistant/business-detail?record=${JSON.stringify(this.record)}`
              })
            }
          }
        }, 1000)
      } else {
        uni.showToast({
          icon: 'none',
          title: res.message
        })
      }
    },
    // 帮办员保存
    async handleSave() {
      const res = await saveRecord(this.formData)
      if (res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message
        })
        setTimeout(() => {
          if (this.loginType == 0) {
            uni.redirectTo({
              url: `/pages/admin/adminHandlingRecord?type=0`
            })
          }
          if (this.loginType == 1) {
            uni.redirectTo({
              url: `/pages/mine/handlingRecord?type=0`
            })
          }
        }, 1000)
      }
    },
    async popupSubmit() {
      let res = null
      if (this.popupType == 1) {
        res = await back(this.popupForm)
      }
      if (this.popupType == 2) {
        res = await pass(this.popupForm)
      }
      if (res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message
        })
        setTimeout(() => {
          this.$refs.popup.close()
          uni.redirectTo({
            url: `/pages/admin/adminHandlingRecord?type=0`
          })
        }, 500)
      } else {
        uni.showToast({
          icon: 'none',
          title: res.message
        })
      }
    },
    // 重置表单
    handleReset() {
      this.record.currentItem.menuList.forEach((item) => {
        item.itemFieldList.forEach((field) => {
          field.fieldValue = ''
        })
      })
      this.fileList = []
      this.fieldFileList = {}
      this.fieldFileList2 = {}
      uni.showToast({
        icon: 'none',
        title: '重置成功'
      })
    },
    handleBack() {
      uni.navigateBack()
    },
    // 帮办员保存表单数据
    handleFormData(flag) {
      const submitStatus = flag
      const recordId = this.model.recordId
      const recordItemId = this.currentItem.id
      const businessLevel = this.currentItem.businessLevel
      let addSupplementList = []
      this.supplementFieldListAll.map((item) => {
        const menuOrder = item.menuOrder
        item.itemFieldList.map((field) => {
          if (!field.id) field.id = ''
          if (!!field.fieldSort || field.fieldSort === 0) field.filedSort = field.fieldSort
          field.recordId = recordId
          field.isCustom = '1'
          this.$nextTick(() => {
            field.fieldValue = this.form.getFieldValue(
              `formField-${this.currentItem.itemId}-${menuOrder}-${field.filedSort}`
            )
          })

          addSupplementList.push(field)
        })
      })
      const deleteSupplementIdList = !!this.deleteSupplementIdList.length
        ? this.deleteSupplementIdList
        : null
      this.fileDataSource.forEach((file) => {
        file.recordId = this.model.recordId
        file.fileType = '3'
      })
      this.proxyFileDataSource.forEach((file) => {
        file.recordId = this.model.recordId
        file.fileType = '2'
      })
      let fileList = [...this.fileDataSource, ...this.proxyFileDataSource]
      fileList = fileList.filter((file) => {
        return !!file.fileName || file.fileName === ''
      })

      let recordItemFieldList = []
      const itemId = this.currentItem.itemId
      this.currentItem.menuList.forEach((menu) => {
        const menuOrder = menu.menuOrder
        menu.itemFieldList.forEach((field) => {
          if (field.fieldType === 'sck') return
          const filedSort = field.filedSort
          // 获取当前字段的新值
          let recordItemFieldTemp = field
          recordItemFieldTemp.fieldValue = this.form.getFieldValue(
            `formField-${itemId}-${menuOrder}-${filedSort}`
          )
          recordItemFieldList.push(recordItemFieldTemp)
        })
      })
      return {
        submitStatus,
        recordId,
        recordItemId,
        fileList,
        recordItemFieldList,
        businessLevel,
        addSupplementList,
        deleteSupplementIdList
      }
    }
  }
}
</script>

<style>
page {
  background: #f4f8fc;
}
</style>
<style lang="scss" scoped>
.main-box {
  .progress-box2 {
    position: relative;
    width: 720rpx;
    height: 250rpx;
    margin: 0 auto;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    color: #444444;
    background: url('../../static/images/assistant/jd.png') no-repeat;
    background-size: 100% 100%;
    // .bg {
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   z-index: -1;
    //   width: 100%;
    //   height: 250rpx;
    // }
    .title {
      position: absolute;
      top: 15%;
      left: 6%;
    }
    .box {
      position: absolute;
      top: 60%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 620rpx;
      height: 52rpx;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 34rpx;
      padding: 5rpx;
      .progress {
        height: 100%;
        background: linear-gradient(270deg, #007fff 0%, #97bcff 59%, #c8fff7 100%);
        border-radius: 34rpx;
        position: relative;
      }
    }
    .text {
      width: 106rpx;
      height: 56rpx;
      line-height: 56rpx;
      background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
      box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(81, 138, 176, 0.5);
      border-radius: 34rpx;
      text-align: center;
      position: absolute;
      top: 50%;
      right: -32rpx;
      transform: translateY(-50%);
    }
    margin-bottom: 30rpx;
  }
  .card {
    position: relative;
    margin: 0rpx auto 20rpx auto;
    width: 720rpx;
    .card-box {
      margin-top: -10rpx;
      background: url(https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/bg_c.png) no-repeat;
      background-size: 100% 100%;
      padding: 10rpx 60rpx;

      & > view:last-child {
        border: none;
        .no_sck_box,
        .skc_box {
          border: none;
        }
      }
    }
    .bg_t {
      width: 100%;
      height: 60rpx;
    }
    .bg_b {
      width: 100%;
      height: 30rpx;
    }
    .upload {
      width: 186rpx;
      height: 56rpx;
      border-radius: 32rpx;
      border: 1rpx solid #d0d0d0;
      display: flex;
      align-items: center;
      justify-content: center;
      image {
        width: 32rpx;
        height: 24rpx;
        margin-right: 10rpx;
      }
    }
    .download {
      width: 186rpx;
      height: 56rpx;
      border-radius: 32rpx;
      border: 1rpx solid #d0d0d0;
      display: flex;
      align-items: center;
      justify-content: center;
      image {
        width: 25rpx;
        height: 24rpx;
        margin-right: 10rpx;
      }
    }
    .title {
      position: relative;
      z-index: 2;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 36rpx;
      color: #057ffe;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: block;
        width: 18rpx;
        height: 18rpx;
        background: #057ffe;
        border-radius: 50%;
        margin-right: 10rpx;
      }
      margin-bottom: 20rpx;
    }
    .fj,
    .delete {
      width: 26rpx;
      height: 26rpx;
    }
  }
  .cell {
    display: flex;
    justify-content: space-between;
    padding: 30rpx 0;
    font-family: PingFangSC, PingFang SC;
    border-bottom: 2rpx solid #e7f0ff;
    & > view:nth-child(1) {
      min-width: 200rpx;
      margin-right: 20rpx;
    }
  }
  .cell3 {
    width: 100;
    margin: 0 auto;
    .skc_box {
      padding: 30rpx 0rpx;
      border-bottom: 2rpx solid #e7f0ff;
      .tem_list {
        margin: 10rpx 0;
        font-size: 28rpx;
        & > view {
          padding: 10rpx 0;
          color: rgba(0, 137, 255, 1);
        }
      }
      .upload_btn {
        margin: 10rpx 0;
      }
    }
    .no_sck_box {
      display: flex;
      justify-content: space-between;
      padding: 30rpx 0rpx;
      border-bottom: 2rpx solid #e7f0ff;
      & > view:nth-child(1) {
        width: 200rpx;
        margin-right: 20rpx;
      }
      .value {
        flex: 1;
        & > view:nth-child(1) {
          height: 60rpx;
          input {
            height: 100%;
            line-height: 60rpx;
            padding-left: 20rpx;
            border-radius: 6rpx;
            border: 2rpx solid #dddddd;
            background: #ffffff;
          }
        }
      }
    }
  }
  .cell2 {
    width: 100%;
    padding: 30rpx 0rpx;
    .label {
      margin-bottom: 10rpx;
    }
    .textarea {
      width: 100%;
      height: 250rpx;
      margin: 0 auto;
      background: #f5f7fb;
      border-radius: 20rpx;
      padding: 20rpx;
    }
  }
  .cell4 {
    width: 100%;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    .sub_title {
      font-weight: 500;
      font-size: 30rpx;
      color: #333333;
    }
    & > view:nth-child(1) {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      .btns {
        display: flex;
        align-items: center;
        & > view {
          width: 186rpx;
          height: 56rpx;
          border-radius: 32rpx;
          border: 1rpx solid #d0d0d0;
          text-align: center;
          margin-right: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .download {
        image {
          width: 25rpx;
          height: 24rpx;
          margin-right: 10rpx;
        }
      }
      .upload {
        image {
          width: 32rpx;
          height: 24rpx;
          margin-right: 10rpx;
        }
      }
    }
    .fj,
    .delete {
      width: 26rpx;
      height: 26rpx;
    }
    .fj {
      margin-right: 10rpx;
    }
  }
  .btn-box {
    display: flex;
    justify-content: center;
    // margin-top: 30rpx;
    padding: 60rpx 20rpx;
    & > view {
      flex: 1;
      height: 66rpx;
      line-height: 66rpx;
      background: #057ffe;
      box-shadow: 0rpx 2rpx 6rpx 0rpx rgba(0, 0, 0, 0.12);
      border-radius: 8rpx;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 26rpx;
      color: #ffffff;
      margin: 0rpx 10rpx;
    }
  }
  .label {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 30rpx;
    color: #333333;
  }
  .value {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 30rpx;
    color: #666666;
  }
  .value2 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #666666;
    font-size: 26rpx;
  }
  .suspension {
    position: fixed;
    right: 0%;
    bottom: 20%;
    display: flex;
    flex-direction: column;
    image {
      width: 108rpx;
      height: 108rpx;
      margin-bottom: 10rpx;
    }
  }
}
.hover_class {
  opacity: 0.8;
}
.input_hover_class {
  input {
    border: 2rpx solid #057ffe !important;
  }
}
.popup-content {
  position: relative;
  width: 600rpx;
  max-height: 800rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: #fff;
  border-radius: 16rpx;
  padding: 60rpx 30rpx;
  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 174rpx;
  }
  .title {
    font-weight: 600;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 42rpx;
    color: #181818;
    z-index: 1;
  }
  .content {
    text-indent: 2em;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #444444;
    line-height: 48rpx;
    overflow: auto;
  }
  .footer {
    width: 282rpx;
    height: 80rpx;
    margin: 0 auto;
    line-height: 80rpx;
    background: #057ffe;
    border-radius: 8rpx;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    color: #ffffff;
  }
}
.popup-content2 {
  position: relative;
  width: 600rpx;
  min-height: 600rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  background: #fff;

  .popup-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    image {
      width: 100%;
      height: 174rpx;
    }
  }
  .popup-title {
    font-size: 36rpx;
    color: #333;
    text-align: center;
    font-weight: 600;
    width: 194rpx;
    height: 64rpx;
    line-height: 64rpx;
    position: relative;
    z-index: 1;
    margin: 0 auto 30rpx;
  }
  .popup-body {
    position: relative;
    z-index: 1;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    .textarea {
      width: 538rpx;
      height: 253rpx;
      margin: 20rpx auto;
      background: #f5f7fb;
      border-radius: 20rpx;
      padding: 20rpx;
    }
    .btns {
      display: flex;
      justify-content: space-around;
      & > view:nth-child(1) {
        width: 220rpx;
        height: 80rpx;
        border-radius: 8rpx;
        border: 2rpx solid #057ffe;
        line-height: 80rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #057ffe;
        text-align: center;
      }
      & > view:nth-child(2) {
        width: 220rpx;
        height: 80rpx;
        line-height: 80rpx;
        background: #057ffe;
        border-radius: 8rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #ffffff;
        text-align: center;
      }
    }
  }
}

::v-deep .uni-select__input-text,
.uni-select__input-placeholder,
.uni-date__x-input {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 30rpx !important;
  color: #666666 !important;
}
</style>
