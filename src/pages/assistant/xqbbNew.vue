<template>
  <div>
    <div class="main_box">
      <div class="top-box">
        <div class="title-box">小乔帮您办</div>
        <!-- 表单部分 -->
        <div class="form-box">
          <!-- <div class="cell">
            <div class="label">业务流水号:</div>
            <div class="value2">{{ recordId }}</div>
          </div>
          <div class="cell">
            <div class="label label2">
              <span>*</span>
              姓名:
            </div>
            <div class="value2">
              <input disabled type="text" placeholder="请输入姓名" v-model="query.contactName" />
            </div>
          </div>
          <div class="cell">
            <div class="label label2"><span>*</span>手机号:</div>
            <div class="value2 input-container">
              <input
                disabled
                type="text"
                placeholder="请输入手机号"
                v-model="query.contactPhone"
                @input="handlePhoneInput"
                :class="{ 'input-hidden': !showPhone && query.contactPhone }"
                maxlength="11"
              />
              <div
                v-if="!showPhone && query.contactPhone"
                class="mask-overlay"
                @click="showPhone = true"
              >
                {{ formatPhone(query.contactPhone) }}
              </div>
              <img
                :src="showPhone ? '/static/index/hidden.png' : '/static/index/show.png'"
                @click="togglePhoneVisibility"
                class="eye-icon"
              />
            </div>
          </div>
          <div class="cell">
            <div class="label label2"><span>*</span>身份证号:</div>
            <div class="value2 input-container">
              <input
                disabled
                type="text"
                placeholder="请输入身份证号"
                v-model="query.idCard"
                @input="handleIdCardInput"
                :class="{ 'input-hidden': !showIdCard && query.idCard }"
                maxlength="18"
              />
              <div
                v-if="!showIdCard && query.idCard"
                class="mask-overlay"
                @click="showIdCard = true"
              >
                {{ formatIdCard(query.idCard) }}
              </div>
              <img
                :src="showIdCard ? '/static/index/hidden.png' : '/static/index/show.png'"
                @click="toggleIdCardVisibility"
                class="eye-icon"
              />
            </div>
          </div> -->
          <div class="cell" @click="openAreaPopup">
            <div class="label label2"><span>*</span>申办地区:</div>
            <div class="value2">
              <span>{{ query.divisionFullName || '请选择' }}</span>
              <img src="/static/images/assistant/down.png" />
            </div>
          </div>
          <div class="cell" @click="showItemType">
            <div class="label label2"><span>*</span>申办部门:</div>
            <div class="value2">
              <span>{{ itemTypeName || '请选择' }}</span>
              <img src="/static/images/assistant/down.png" />
            </div>
          </div>
          <div class="cell2" v-if="itemTypeName !== '其他'" @click="showItemList">
            <div class="label label2"><span>*</span>可办事项:</div>
            <div class="value2">
              <span>{{ itemNameText || '请选择' }}</span>
              <img src="/static/images/assistant/down.png" />
            </div>
          </div>
          <div class="cell2" v-else>
            <div class="label label2">
              <span>*</span>
              可办事项:
            </div>
            <div class="value2">
              <input type="text" placeholder="请输入办理事项" v-model="query.helpContent" />
            </div>
          </div>
        </div>
        <!-- 提交按钮 -->
        <div class="btns">
          <!-- <div class="btn" @click="saveForm">保存</div> -->
          <div class="btn btn2" @click="submitForm">提交</div>
        </div>
      </div>

      <!-- 热线和帮办按钮 -->
      <div @click="getPhones" class="banner-box">
        <img src="/static/index/xqrx.png" class="banner" alt="" />
      </div>

      <!-- 工作时间 -->
      <div class="work-time">
        <div class="title3">
          工作时间
          <!-- <span style="font-weight: 400">{{ isWorkDay() ? '(工作日)' : '(非工作日)' }}</span> -->
        </div>
        <div class="time">
          <span>上午 {{ this.amTime }}</span>
          <span>下午 {{ this.pmTime }} </span>
          <span>(法定节假日除外)</span>
        </div>
      </div>
    </div>

    <!-- 地区选择弹窗 -->
    <uni-popup ref="areaPopup" type="bottom">
      <div class="area-popup">
        <div class="popup-header">
          <div>选择地区</div>
          <img src="/static/images/admin/close.png" alt="" @click="closeAreaPopup" />
        </div>

        <div class="area-list">
          <!-- 省列表 -->
          <div class="city">
            <div :class="provinceIndex ? 'active' : ''">嘉鱼县</div>
          </div>
          <!-- 市区列表 -->
          <div class="city">
            <div
              :class="tempAreaSelection.cityIndex == index ? 'active' : ''"
              v-for="(item, index) in columns[1]"
              :key="index"
              @click="selectCity(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
          <!-- 区列表 -->
          <div class="city">
            <div
              :class="tempAreaSelection.areaIndex == index ? 'active' : ''"
              v-for="(item, index) in columns[2]"
              :key="index"
              @click="selectAreaItem(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>

        <div class="popup-footer">
          <div class="btn cancel" @click="closeAreaPopup">取消</div>
          <div class="btn confirm" @click="confirmArea">确定</div>
        </div>
      </div>
    </uni-popup>

    <!-- 非工作时间提示弹窗 -->
    <uni-popup ref="popup">
      <div class="popup-content">
        <div class="title">很抱歉,现在是非工作时间</div>
        <div class="btn" @click="closePopup">确定</div>
      </div>
    </uni-popup>

    <!-- 业务须知弹窗 -->
    <uni-popup class="answerBoxs" ref="guide" type="center" mask-background-color="rgba(0,0,0,0.1)">
      <view class="content">
        <image
          class="report_title"
          src="@/static/images/interaction/report_title.png"
          mode=""
        ></image>
        <image class="cross" @click="handleBack()" src="@/static/images/cross.png" mode=""></image>
        <view class="business b34">业务须知</view>
        <view class="entering">
          <u-parse :content="noticeContent"></u-parse>
        </view>
        <div class="btns">
          <div class="btn" @click="iknow()">我已知晓</div>
        </div>
      </view>
    </uni-popup>
  </div>
</template>

<script>
import {
  getItemType,
  getItemListNew,
  administrativeDivisionRootList,
  getHelpTelInfo,
  addNewRecordNew,
  getInstructionInfo,
  addTelRecord,
  commitRecord,
  saveRecordNew
} from '@/api/assistant/index.js'
import { jsonp } from 'vue-jsonp'
import dayjs from 'dayjs'
export default {
  data() {
    return {
      recordId: this.generateFlowId(), // 生成业务流水号
      userInfo: {},
      query: {
        contactName: '',
        contactPhone: '',
        idCard: '',
        divisionFullName: '嘉鱼县',
        divisionId: '',
        divisionLevel: '1',
        itemId: '',
        itemName: '',
        itemType: '',
        helpContent: '',
        selectArr: []
      },
      initialRecord: null,
      itemTypeName: '',
      itemNameText: '',
      itemTypeList: [],
      itemList: [],

      // 地区选择相关
      columns: [[], [], []],
      provinceIndex: true,
      cityIndex: null, // 不默认选中任何二级地区
      areaIndex: null,
      selectText: '',
      selectArr: {
        province: '',
        city: '',
        area: ''
      },

      // 临时地区选择状态（用于弹窗中的选择，只有点击确定才应用）
      tempAreaSelection: {
        cityIndex: null,
        areaIndex: null,
        selectText: '',
        divisionId: '',
        divisionFullName: '',
        divisionLevel: '1'
      },

      // 工作时间相关
      amTime: '8:30-12:00',
      pmTime: '15:00-18:00',
      amTimeDong: '8:30-12:00', // 冬令时上午时间
      pmTimeDong: '15:00-18:00', // 冬令时下午时间
      phones: '',
      phones2: '',
      noticeContent: '',

      // 表单记录ID
      formRecordId: '',

      // 加载状态
      loading: false,

      // 显示/隐藏控制
      showPhone: false,
      showIdCard: false
    }
  },
  async onLoad() {
    // 加载用户信息
    this.userInfo =
      typeof this.$store.state.user.userInfo == 'string'
        ? JSON.parse(this.$store.state.user.userInfo)
        : this.$store.state.user.userInfo

    // 加载地区数据并初始化默认值
    await this.fetchAllRegions()

    // 根据初始地区级别获取部门列表
    await this.loadDepartmentList()

    // 获取工作时间和电话
    const res2 = await getHelpTelInfo({
      divisionLevel: this.query.divisionLevel,
      divisionId: this.query.divisionId
    })
    if (res2.success) {
      this.phones = `tel:${res2.result.phoneNumber}`
      this.amTime = res2.result.amTime
      this.pmTime = res2.result.pmTime
      // 获取冬令时时间，如果接口没有返回则使用夏令时时间作为默认值
      this.amTimeDong = res2.result.amTimeDong || res2.result.amTime
      this.pmTimeDong = res2.result.pmTimeDong || res2.result.pmTime
      this.phones2 = res2.result.phoneNumber
    }

    // 加载业务须知
    this.getNotice()

    // 获取当前位置
    this.getCurrentLocation()

    // 初始化时获取基本信息
    await this.getInitialRecord()
  },
  async onShow() {
    // 每次显示页面时刷新业务流水号（仅在页面初始化完成后）
    if (this.query.divisionId) {
      await this.refreshBusinessRecord()
    }
  },
  methods: {
    // 刷新业务流水号
    async refreshBusinessRecord() {
      // 重新生成本地业务流水号
      this.recordId = this.generateFlowId()

      // 调用 addNewRecordNew 获取新的记录
      await this.getInitialRecord()
    },

    // 从接口加载部门列表
    async loadDepartmentList() {
      try {
        const res = await getItemType({
          divisionLevel: this.query.divisionLevel
        })

        if (res.success && res.result) {
          // 将接口返回的部门数据设置到itemTypeList
          this.itemTypeList = [...res.result, '其他'] // 添加"其他"选项
        } else {
          console.error('获取部门列表失败:', res.message)
          // 如果接口失败，设置默认的"其他"选项
          this.itemTypeList = ['其他']
        }
      } catch (error) {
        console.error('获取部门列表出错:', error)
        // 如果接口出错，设置默认的"其他"选项
        this.itemTypeList = ['其他']
      }
    },

    // 生成业务流水号
    generateFlowId() {
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const random = Math.floor(Math.random() * 10000)
        .toString()
        .padStart(4, '0')
      return `${year}${month}${day}${random}`
    },

    // 获取当前位置
    getCurrentLocation() {
      let url = 'https://apis.map.qq.com/ws/geocoder/v1/'
      let data = {
        coord_type: 5,
        get_poi: 0,
        output: 'jsonp',
        key: 'G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV',
        location: ''
      }

      wx.getLocation({
        type: 'gcj02',
        isHighAccuracy: true,
        highAccuracyExpireTime: 10000,
        success: async (res) => {
          data.location = res.latitude + ',' + res.longitude
          jsonp(url, data).then((res2) => {
            console.log('地图位置', res2)
            this.address = res2.result.formatted_addresses?.recommend || res2.result.address
          })
        }
      })
    },

    // 获取业务须知
    async getNotice() {
      const res = await getInstructionInfo()
      if (res.result[0]?.content) {
        this.noticeContent = res.result[0]?.content
        this.$refs.guide.open()
      }
    },

    // 初始化时获取基本信息
    async getInitialRecord() {
      if (this.loading) return
      this.loading = true

      try {
        // 只传递地区信息调用接口
        const areaData = {
          divisionId: this.query.divisionId,
          divisionFullName: this.query.divisionFullName,
          divisionLevel: this.query.divisionLevel
        }

        const res = await addNewRecordNew(areaData)
        if (res.success) {
          // 保存完整的返回数据
          this.initialRecord = res.result

          // 记录返回的记录ID，并更新显示的业务流水号
          this.formRecordId = res.result.recordId
          this.query.recordId = res.result.recordId
          this.recordId = res.result.recordId // 更新显示的业务流水号

          // 如果有返回其他信息，更新到表单
          if (res.result.contactName) this.query.contactName = res.result.contactName
          if (res.result.contactPhone) this.query.contactPhone = res.result.contactPhone
          if (res.result.idCard) this.query.idCard = res.result.idCard

          console.log('初始化获取信息成功', res.result)
        } else {
          console.error('初始化获取信息失败', res.message)
        }
      } catch (error) {
        console.error('初始化获取信息出错:', error)
      } finally {
        this.loading = false
      }
    },

    // 加载地区数据
    async fetchAllRegions() {
      const res = await administrativeDivisionRootList({ pid: 0 })

      // 省数据
      this.columns[0].push(res.result[0])
      this.selectArr.province = res.result[0].id
      this.provinceIndex = true

      // 设置默认选中嘉鱼县（省级）
      this.query.divisionId = res.result[0].id
      this.query.divisionFullName = res.result[0].name

      // 显示所有二级地区（不限制只有潘家湾镇）
      this.columns[1] = res.result[0].children || []

      // 不默认选中任何二级地区，让用户自己选择
      this.cityIndex = null
      this.areaIndex = null
      this.selectText = ''
      this.columns[2] = []
    },

    // 打开地区选择弹窗
    openAreaPopup() {
      // 初始化临时选择状态为当前的选择
      this.initTempAreaSelection()
      this.$refs.areaPopup.open()
    },

    // 关闭地区选择弹窗
    closeAreaPopup() {
      this.$refs.areaPopup.close()
      // 重置临时选择状态
      this.resetTempAreaSelection()
    },

    // 初始化临时地区选择状态
    initTempAreaSelection() {
      // 根据当前query的地区信息设置临时选择状态
      if (this.query.divisionLevel === '1') {
        // 只选择了嘉鱼县
        this.tempAreaSelection.cityIndex = null
        this.tempAreaSelection.areaIndex = null
        this.tempAreaSelection.selectText = ''
        this.columns[2] = [] // 清空三级地区
      } else if (this.query.divisionLevel === '2') {
        // 选择了二级地区
        const cityItem = this.columns[1].find((item) => item.id === this.query.divisionId)
        if (cityItem) {
          this.tempAreaSelection.cityIndex = this.columns[1].findIndex(
            (item) => item.id === this.query.divisionId
          )
          this.tempAreaSelection.selectText = cityItem.name
          this.tempAreaSelection.areaIndex = null
          // 加载对应的三级地区
          this.loadThirdLevelAreas(cityItem)
        }
      } else if (this.query.divisionLevel === '3') {
        // 选择了三级地区，需要先找到对应的二级地区
        const allSecondLevel = this.columns[1]
        let parentCity = null
        let cityIndex = -1

        for (let i = 0; i < allSecondLevel.length; i++) {
          const city = allSecondLevel[i]
          if (city.children && city.children.find((child) => child.id === this.query.divisionId)) {
            parentCity = city
            cityIndex = i
            break
          }
        }

        if (parentCity) {
          this.tempAreaSelection.cityIndex = cityIndex
          this.tempAreaSelection.selectText = parentCity.name
          // 加载对应的三级地区
          this.loadThirdLevelAreas(parentCity)
          // 设置三级地区选择
          const filteredChildren = this.columns[2] // 使用过滤后的children
          const areaItem = filteredChildren.find((child) => child.id === this.query.divisionId)
          if (areaItem) {
            this.tempAreaSelection.areaIndex = filteredChildren.findIndex(
              (child) => child.id === this.query.divisionId
            )
          }
        }
      }

      // 设置临时选择的地区信息
      this.tempAreaSelection.divisionId = this.query.divisionId
      this.tempAreaSelection.divisionFullName = this.query.divisionFullName
      this.tempAreaSelection.divisionLevel = this.query.divisionLevel
    },

    // 重置临时地区选择状态
    resetTempAreaSelection() {
      this.tempAreaSelection.cityIndex = null
      this.tempAreaSelection.areaIndex = null
      this.tempAreaSelection.selectText = ''
      this.tempAreaSelection.divisionId = ''
      this.tempAreaSelection.divisionFullName = ''
      this.tempAreaSelection.divisionLevel = '1'
    },

    // 加载三级地区数据
    loadThirdLevelAreas(cityItem) {
      if (cityItem.name === '潘家湾镇') {
        let arr = ['头墩社区', '肖家洲村', '四邑村', '红光社区', '潘湾社区', '潘家湾村']
        let arr2 = cityItem.children.filter((child) => arr.includes(child.name))
        this.columns[2] = arr2
      } else {
        this.columns[2] = cityItem.children || []
      }
    },

    // 确认地区选择并获取基本信息
    async confirmArea() {
      // 检查临时选择状态
      if (!this.tempAreaSelection.divisionId || !this.tempAreaSelection.divisionFullName) {
        uni.showToast({
          icon: 'none',
          title: '请选择完整的地区信息'
        })
        return
      }

      // 将临时选择应用到正式的query对象
      this.query.divisionId = this.tempAreaSelection.divisionId
      this.query.divisionFullName = this.tempAreaSelection.divisionFullName
      this.query.divisionLevel = this.tempAreaSelection.divisionLevel

      // 同步更新其他相关状态
      this.cityIndex = this.tempAreaSelection.cityIndex
      this.areaIndex = this.tempAreaSelection.areaIndex
      this.selectText = this.tempAreaSelection.selectText

      if (this.loading) return
      this.loading = true

      try {
        // 清空部门和事项选择
        this.itemTypeName = ''
        this.itemNameText = ''
        this.query.itemType = ''
        this.query.itemName = ''
        this.query.itemId = ''
        this.query.helpContent = ''

        // 根据新的地区级别重新加载部门列表
        await this.loadDepartmentList()

        // 只传递地区信息调用接口
        const areaData = {
          divisionId: this.query.divisionId,
          divisionFullName: this.query.divisionFullName,
          divisionLevel: this.query.divisionLevel
        }

        const res = await addNewRecordNew(areaData)
        if (res.success) {
          // 更新完整的初始记录数据，包含地区相关字段
          this.initialRecord = res.result

          // 记录返回的记录ID，并更新显示的业务流水号
          this.formRecordId = res.result.recordId
          this.query.recordId = res.result.recordId
          this.recordId = res.result.recordId // 更新显示的业务流水号

          // 如果有返回其他信息，更新到表单
          if (res.result.contactName) this.query.contactName = res.result.contactName
          if (res.result.contactPhone) this.query.contactPhone = res.result.contactPhone
          if (res.result.idCard) this.query.idCard = res.result.idCard

          // 地区变化后重新获取工作时间和电话信息
          const helpTelRes = await getHelpTelInfo({
            divisionLevel: this.query.divisionLevel,
            divisionId: this.query.divisionId
          })
          if (helpTelRes.success) {
            this.phones = `tel:${helpTelRes.result.phoneNumber}`
            this.amTime = helpTelRes.result.amTime
            this.pmTime = helpTelRes.result.pmTime
            // 获取冬令时时间，如果接口没有返回则使用夏令时时间作为默认值
            this.amTimeDong = helpTelRes.result.amTimeDong || helpTelRes.result.amTime
            this.pmTimeDong = helpTelRes.result.pmTimeDong || helpTelRes.result.pmTime
            this.phones2 = helpTelRes.result.phoneNumber
          }

          uni.showToast({
            icon: 'success',
            title: '地区选择成功'
          })

          this.closeAreaPopup()
        } else {
          uni.showToast({
            icon: 'none',
            title: res.message || '获取信息失败'
          })
        }
      } catch (error) {
        console.error('获取基本信息出错:', error)
        uni.showToast({
          icon: 'none',
          title: '获取信息失败'
        })
      } finally {
        this.loading = false
      }
    },

    // 市点击
    selectCity(item, index) {
      // 如果点击的是已选中的项，则取消选择
      if (this.tempAreaSelection.cityIndex === index) {
        // 取消选择，回到只选择嘉鱼县的状态
        this.tempAreaSelection.cityIndex = null
        this.tempAreaSelection.areaIndex = null
        this.tempAreaSelection.selectText = ''
        this.tempAreaSelection.divisionId = this.selectArr.province // 回到省级ID
        this.tempAreaSelection.divisionLevel = '1'
        this.tempAreaSelection.divisionFullName = '嘉鱼县'
        this.$set(this.columns, 2, []) // 清空第三级选项
        return
      }

      // 选择新的第二级地区
      this.$set(this.columns, 2, [])
      this.tempAreaSelection.areaIndex = null
      this.tempAreaSelection.cityIndex = index
      this.tempAreaSelection.selectText = item.name
      this.tempAreaSelection.divisionId = item.id
      this.tempAreaSelection.divisionLevel = '2'
      this.tempAreaSelection.divisionFullName = '嘉鱼县' + item.name

      // 筛选子区域
      this.loadThirdLevelAreas(item)
    },

    // 区点击
    selectAreaItem(item, index) {
      // 如果点击的是已选中的项，则取消选择
      if (this.tempAreaSelection.areaIndex === index) {
        // 取消选择，回到第二级地区状态
        this.tempAreaSelection.areaIndex = null
        // 回到第二级地区的状态
        const selectedCity = this.columns[1][this.tempAreaSelection.cityIndex]
        if (selectedCity) {
          this.tempAreaSelection.divisionId = selectedCity.id
          this.tempAreaSelection.divisionLevel = '2'
          this.tempAreaSelection.divisionFullName = '嘉鱼县' + this.tempAreaSelection.selectText
        }
        return
      }

      // 选择新的第三级地区
      this.tempAreaSelection.areaIndex = index
      this.tempAreaSelection.divisionId = item.id
      this.tempAreaSelection.divisionFullName =
        '嘉鱼县' + this.tempAreaSelection.selectText + item.name
      this.tempAreaSelection.divisionLevel = '3'
    },

    // 事项类型选择
    showItemType() {
      // 确认已经选择了地区
      if (!this.query.divisionId) {
        uni.showToast({
          icon: 'none',
          title: '请先选择帮代办地区'
        })
        return
      }

      uni.showActionSheet({
        itemList: this.itemTypeList.map((i) => i),
        success: (res) => {
          this.itemNameText = ''
          this.query.itemName = ''
          this.itemTypeName = this.itemTypeList[res.tapIndex]
          this.query.itemType = this.itemTypeList[res.tapIndex]

          if (this.itemTypeName !== '其他') {
            this.getItemListNewList()
          } else {
            // 清空可办事项
            this.query.itemId = ''
            this.query.itemName = ''
            this.query.helpContent = ''
          }
        }
      })
    },

    // 获取事项列表
    async getItemListNewList() {
      const res = await getItemListNew({
        itemType: this.query.itemType
      })
      this.itemList = res.result
    },

    // 事项选择
    showItemList() {
      if (!this.query.itemType) {
        uni.showToast({
          icon: 'none',
          title: '请先选择实施部门'
        })
        return
      }

      if (this.itemTypeName === '其他') {
        return
      }

      uni.showActionSheet({
        itemList: this.itemList.map((i) => i.itemName),
        success: (res) => {
          this.query.itemName = this.itemList[res.tapIndex].itemName
          this.itemNameText = this.itemList[res.tapIndex].itemName
          this.query.itemId = this.itemList[res.tapIndex].itemId
          this.query.divisionLevel =
            this.itemList[res.tapIndex].divisionLevel || this.query.divisionLevel
        }
      })
    },

    // 判断是否是工作日
    isWorkDay() {
      const now = new Date()
      const day = now.getDay()
      return day !== 0 && day !== 6
    },

    // 判断是否是夏令时
    isSummerTime() {
      const now = new Date()
      const month = now.getMonth() + 1 // getMonth() 返回 0-11，所以要加1

      // 夏令时通常是4月到10月（可根据实际需求调整）
      // 这里采用简单的月份判断，实际可能需要更精确的日期判断
      if (month >= 4 && month <= 10) {
        return true
      }
      return false
    },

    // 获取当前应该显示的上午时间
    getCurrentAmTime() {
      return this.isSummerTime() ? this.amTime : this.amTimeDong
    },

    // 获取当前应该显示的下午时间
    getCurrentPmTime() {
      return this.isSummerTime() ? this.pmTime : this.pmTimeDong
    },

    // 检测时间是否在工作时间内
    checkTime() {
      const now = new Date()
      const currentHours = now.getHours()
      const currentMinutes = now.getMinutes()

      // 根据当前季节获取对应的时间
      const currentAmTime = this.getCurrentAmTime()
      const currentPmTime = this.getCurrentPmTime()

      // 解析上午时间范围
      const [amStart, amEnd] = currentAmTime.split('-').map((time) => time.split(':').map(Number))
      const [amStartHour, amStartMinute] = amStart
      const [amEndHour, amEndMinute] = amEnd

      // 解析下午时间范围
      const [pmStart, pmEnd] = currentPmTime.split('-').map((time) => time.split(':').map(Number))
      const [pmStartHour, pmStartMinute] = pmStart
      const [pmEndHour, pmEndMinute] = pmEnd

      // 检查是否在上午时间范围
      const isMorning =
        (currentHours > amStartHour ||
          (currentHours === amStartHour && currentMinutes >= amStartMinute)) &&
        (currentHours < amEndHour || (currentHours === amEndHour && currentMinutes <= amEndMinute))

      // 检查是否在下午时间范围
      const isAfternoon =
        (currentHours > pmStartHour ||
          (currentHours === pmStartHour && currentMinutes >= pmStartMinute)) &&
        (currentHours < pmEndHour || (currentHours === pmEndHour && currentMinutes <= pmEndMinute))

      return isMorning || isAfternoon
    },

    // 拨打热线电话
    async getPhones() {
      if (!this.checkTime()) {
        this.$refs.popup.open('center')
        return
      }

      // 验证可办事项是否已选择或填写（在拨打电话前验证）
      if (this.itemTypeName === '其他') {
        if (!this.query.helpContent) {
          uni.showToast({ icon: 'none', title: '请输入办理事项' })
          return
        }
      } else {
        // 如果选择的是具体部门，必须选择可办事项
        if (!this.query.itemId || !this.query.itemName) {
          uni.showToast({ icon: 'none', title: '请选择可办事项' })
          return
        }
      }

      let phone = ''
      if (this.phones) {
        phone = this.phones
      } else {
        phone = `tel:${this.userInfo.mobilePhone}`
      }

      const a = document.createElement('a')
      a.setAttribute('href', phone)
      if (this.isWorkDay()) {
        a.click()
      }

      // 构建 addTelRecord 的参数
      const telRecordData = {
        phoneNumber: this.phones2,
        divisionFullName: this.query.divisionFullName,
        divisionId: this.query.divisionId,
        divisionLevel: this.query.divisionLevel,
        itemType: this.query.itemType
      }

      // 根据选择类型添加相应字段
      if (this.itemTypeName === '其他') {
        telRecordData.helpContent = this.query.helpContent
      } else {
        telRecordData.itemId = this.query.itemId
        telRecordData.itemName = this.query.itemName
      }

      addTelRecord(telRecordData)
    },

    // 关闭弹窗
    closePopup() {
      this.$refs.popup.close()
    },

    handleBack() {
      this.$refs.guide.close()
    },

    iknow() {
      this.$refs.guide.close()
    },

    // 表单验证
    validateForm() {
      if (!this.query.contactName) {
        uni.showToast({ icon: 'none', title: '请输入姓名' })
        return false
      }

      if (!this.query.contactPhone) {
        uni.showToast({ icon: 'none', title: '请输入手机号' })
        return false
      }

      if (!/^1[3456789]\d{9}$/.test(this.query.contactPhone)) {
        uni.showToast({ icon: 'none', title: '请输入正确的手机号' })
        return false
      }

      if (!this.query.idCard) {
        uni.showToast({ icon: 'none', title: '请输入身份证号' })
        return false
      }

      if (
        !/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
          this.query.idCard
        )
      ) {
        uni.showToast({ icon: 'none', title: '请输入正确的身份证号' })
        return false
      }

      if (!this.query.divisionFullName) {
        uni.showToast({ icon: 'none', title: '请选择帮代办地区' })
        return false
      }

      if (!this.query.itemType) {
        uni.showToast({ icon: 'none', title: '请选择实施部门' })
        return false
      }

      if (this.itemTypeName === '其他') {
        if (!this.query.helpContent) {
          uni.showToast({ icon: 'none', title: '请输入办理事项' })
          return false
        }
        // 如果是其他类型，把helpContent赋值给itemName
        this.query.itemName = this.query.helpContent
      } else {
        if (!this.query.itemName) {
          uni.showToast({ icon: 'none', title: '请选择可办事项' })
          return false
        }
      }

      return true
    },

    // 提交表单
    async submitForm() {
      if (!this.checkTime()) {
        this.$refs.popup.open('center')
        return
      }

      if (!this.validateForm()) {
        return
      }

      if (this.loading) return
      this.loading = true

      try {
        // 创建完整的提交数据，使用addNewRecordNew接口返回的数据作为基础
        let submitData = {}

        // 如果有初始数据，先使用它作为基础
        if (this.initialRecord) {
          submitData = { ...this.initialRecord }
        }

        // 只覆盖用户在表单中填写的个人信息，地区信息使用接口返回的
        // submitData.contactName = this.query.contactName
        // submitData.contactPhone = this.query.contactPhone
        // submitData.idCard = this.query.idCard

        // 地区相关字段使用addNewRecordNew接口返回的数据
        // divisionFullName, divisionId, divisionId_dictText, divisionLevel
        // 这些字段已经在initialRecord中，不需要覆盖

        // 添加事项相关字段
        if (this.itemTypeName === '其他') {
          // 如果选择了"其他"，使用helpContent字段
          submitData.helpContent = this.query.helpContent
          submitData.itemName = this.query.helpContent
          submitData.itemType = this.itemTypeName
        } else {
          // 否则使用选择的事项
          submitData.itemId = this.query.itemId
          submitData.itemName = this.query.itemName
          submitData.itemType = this.query.itemType
          submitData.helpContent = null // 清空helpContent字段
        }

        // 使用commitRecord进行提交
        const res = await commitRecord(submitData)

        if (res.success) {
          uni.showToast({
            title: '提交成功',
            icon: 'success'
          })

          // 跳转到处理记录页面
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/mine/handlingRecord?type=0'
            })
          }, 500)
        } else {
          uni.showToast({
            title: res.message || '提交失败',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '提交失败',
          icon: 'none'
        })
        console.error('提交表单出错:', error)
      } finally {
        this.loading = false
      }
    },

    // 保存表单
    async saveForm() {
      if (!this.checkTime()) {
        this.$refs.popup.open('center')
        return
      }

      if (!this.validateForm()) {
        return
      }

      if (this.loading) return
      this.loading = true

      try {
        // 创建完整的保存数据，使用addNewRecordNew接口返回的数据作为基础
        let submitData = {}

        // 如果有初始数据，先使用它作为基础
        if (this.initialRecord) {
          submitData = { ...this.initialRecord }
        }

        // 只覆盖用户在表单中填写的个人信息，地区信息使用接口返回的
        submitData.contactName = this.query.contactName
        submitData.contactPhone = this.query.contactPhone
        submitData.idCard = this.query.idCard

        // 地区相关字段使用addNewRecordNew接口返回的数据
        // divisionFullName, divisionId, divisionId_dictText, divisionLevel
        // 这些字段已经在initialRecord中，不需要覆盖

        // 添加事项相关字段
        if (this.itemTypeName === '其他') {
          // 如果选择了"其他"，使用helpContent字段
          submitData.helpContent = this.query.helpContent
          submitData.itemName = this.query.helpContent
          submitData.itemType = this.itemTypeName
        } else {
          // 否则使用选择的事项
          submitData.itemId = this.query.itemId
          submitData.itemName = this.query.itemName
          submitData.itemType = this.query.itemType
          submitData.helpContent = null // 清空helpContent字段
        }

        const res = await saveRecordNew(submitData)

        if (res.success) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })

          // 记录返回的ID和完整数据
          this.formRecordId = res.result.recordId
          this.query.recordId = res.result.recordId
          this.initialRecord = res.result
        } else {
          uni.showToast({
            title: res.message || '保存失败',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
        console.error('保存表单出错:', error)
      } finally {
        this.loading = false
      }
    },

    // 格式化手机号
    formatPhone(phone) {
      if (!phone) return ''
      if (phone.length >= 11) {
        return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
      }
      return phone
    },

    // 格式化身份证号
    formatIdCard(idCard) {
      if (!idCard) return ''
      if (idCard.length >= 15) {
        return idCard.replace(/(\d{3})(\d{8,10})(\d{4})/, '$1****$3')
      }
      return idCard
    },

    // 处理手机号输入
    handlePhoneInput(event) {
      const value = event.target.value.replace(/\D/g, '')
      this.query.contactPhone = value
    },

    // 处理身份证号输入
    handleIdCardInput(event) {
      let value = event.target.value.replace(/[^0-9Xx]/g, '')
      if (value.length <= 18) {
        this.query.idCard = value
      }
    },

    // 切换手机号可见性
    togglePhoneVisibility() {
      this.showPhone = !this.showPhone
    },

    // 切换身份证号可见性
    toggleIdCardVisibility() {
      this.showIdCard = !this.showIdCard
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background: #f4f8fc;
}

.main_box {
  width: 93%;
  margin: 30rpx auto;
  padding-bottom: 30rpx;
  .top-box {
    background: #ffffff;
    border-radius: 32rpx;
    padding-bottom: 20rpx;
  }
  .title-box {
    width: 100%;
    height: 136rpx;
    text-align: left;
    background: url('/static/index/sy_bg.png') no-repeat;
    background-size: 100% 100%;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    color: #487ab2;
    text-align: left;
    padding: 30rpx;
    box-sizing: border-box;
  }

  .form-box {
    padding: 0 30rpx;
    margin-bottom: 30rpx;
  }

  .cell {
    height: 120rpx;
    // margin-bottom: 30rpx;
    display: flex;
    align-items: center;
    border-bottom: 2rpx solid #e7f0ff;
  }

  .cell2 {
    height: 120rpx;
    display: flex;
    align-items: center;
  }

  .label {
    height: 80rpx;
    line-height: 80rpx;
    width: 200rpx;
    text-align: left;
    margin-right: 15rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #444444;
  }

  .label2 {
    position: relative;
    & > span {
      color: #f01a1a;
      margin-right: 8rpx;
    }
  }

  .value2 {
    min-height: 60rpx;
    flex: 1;
    padding: 0 20rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 30rpx;
    color: #444444;
    font-style: normal;
    display: flex;
    align-items: center;
    justify-content: space-between;

    img {
      width: 28rpx;
      height: 14rpx;
    }

    .eye-icon {
      width: 32rpx;
      height: 32rpx;
      cursor: pointer;
      opacity: 0.6;
      transition: opacity 0.3s;

      &:hover {
        opacity: 1;
      }
    }

    input {
      width: 100%;
      height: 60rpx;
      font-size: 30rpx;
    }
  }

  .input-container {
    position: relative;

    .input-hidden {
      color: transparent;
    }

    .mask-overlay {
      position: absolute;
      left: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      font-size: 30rpx;
      color: #444444;
      pointer-events: none;
      user-select: none;
    }
  }

  .action-box {
    margin-bottom: 30rpx;
  }

  .banner-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20rpx;
  }

  .banner {
    width: 100%;
    height: 188rpx;
  }

  .work-time {
    margin-top: 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: #fff;
    border-radius: 32rpx;
    .title3 {
      height: 139rpx;
      background: url('/static/index/gzsj.png') no-repeat;
      background-size: 100% 100%;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #487ab2;
      text-align: left;
      padding: 30rpx;
      box-sizing: border-box;
      // margin-bottom: 20rpx;
    }
    .time {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      padding: 10rpx 30rpx 20rpx 30rpx;
      box-sizing: border-box;
      span {
        margin-right: 20rpx;
      }
    }
  }
}

// 地区选择弹窗
.area-popup {
  background-color: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  padding: 30rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    div {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    img {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .area-list {
    display: flex;
    max-height: 600rpx;
    margin-bottom: 30rpx;
  }

  .city {
    flex: 1;
    height: 600rpx;
    overflow: auto;
    padding: 0 10rpx;

    & > div {
      height: 80rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 30rpx;
      color: #333;
      line-height: 80rpx;
      text-align: center;
    }

    .active {
      background: #e7f0ff;
      border-radius: 8rpx;
      color: #057ffe;
    }
  }

  .popup-footer {
    display: flex;
    justify-content: space-between;
    padding: 0 40rpx;

    .btn {
      width: 280rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      border-radius: 8rpx;
      font-size: 32rpx;
    }

    .cancel {
      border: 1px solid #057ffe;
      color: #057ffe;
    }

    .confirm {
      background-color: #057ffe;
      color: #ffffff;
    }
  }
}

// 非工作时间弹窗
.popup-content {
  width: 600rpx;
  height: 299rpx;
  background: #ffffff;
  border-radius: 16rpx;
  position: relative;

  .title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 30rpx;
    color: #444444;
    position: absolute;
    z-index: 3;
    top: 26%;
    left: 18%;
    width: 380rpx;
    text-align: center;
  }

  .btn {
    width: 474rpx;
    height: 76rpx;
    line-height: 76rpx;
    background: linear-gradient(310deg, #0cb6ff 0%, #057ffe 100%);
    border-radius: 36rpx;
    text-align: center;
    color: #fff;
    position: absolute;
    z-index: 3;
    top: 53%;
    left: 18%;
    width: 380rpx;
  }

  &::before {
    content: '';
    width: 106%;
    height: 95px;
    position: absolute;
    z-index: 2;
    top: -10px;
    left: -10px;
    background: url('/static/images/assistant/bq.png') no-repeat;
    background-size: 100% 100%;
  }
}

// 业务须知弹窗
.answerBoxs {
  .content {
    width: 600rpx;
    margin: 32rpx;
    padding: 55rpx 34rpx 30rpx 34rpx;
    display: flex;
    gap: 30rpx;
    flex-direction: column;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
    border-radius: 32rpx;
    position: relative;

    .btns {
      .btn {
        width: 474rpx;
        height: 76rpx;
        background: linear-gradient(310deg, #0cb6ff 0%, #057ffe 100%);
        border-radius: 36rpx;
        font-weight: 500;
        font-size: 30rpx;
        color: #ffffff;
        line-height: 76rpx;
        text-align: center;
        font-style: normal;
        margin: 0 auto;
      }
    }

    .report_title {
      width: 100%;
      height: 180rpx;
      position: absolute;
      top: 0;
      left: 0;
    }

    .entering {
      padding-top: 80rpx;
    }

    .cross {
      width: 36rpx;
      height: 36rpx;
      position: absolute;
      top: 42rpx;
      right: 35rpx;
    }
  }
}

.b34 {
  font-weight: 600;
  font-size: 34rpx;
  color: #444444;
  position: absolute;
}

.btns {
  display: flex;
  justify-content: space-around;
  margin: 30rpx 0;
  .btn {
    width: 600rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 8rpx;
    border: 2rpx solid #057ffe;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 34rpx;
    color: #057ffe;
    text-align: center;
  }
  .btn2 {
    background: #057ffe;
    color: #fff;
  }
}
</style>
