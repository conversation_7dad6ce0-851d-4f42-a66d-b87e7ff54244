<template>
  <div>
    <div class="back_box" @click="goBack" v-if="isUser != 1 && (pageType == 1 || pageType == 3)">
      <img src="/static/images/admin/xq_back.png" alt="" />
      返回
    </div>
    <div class="main-box">
      <div class="title">基本内容</div>
      <!-- <div class="cell">
        <div class="label">业务流水号:</div>
        <div class="value2">{{ query.recordId }}</div>
      </div>
      <div class="cell">
        <div class="label label3">姓名:</div>
        <div class="value2">
          <input
            :disabled="query.isDetail == 1"
            type="text"
            placeholder="请输入姓名"
            v-model="query.contactName"
          />
        </div>
      </div>
      <div class="cell">
        <div class="label label4">手机号:</div>
        <div class="value2 value3">
          <input
            v-if="query.isDetail == 1"
            :disabled="query.isDetail == 1"
            type="text"
            placeholder="请输入手机号"
            :value="maskedPhone"
            maxlength="11"
            @input="updatePhone"
          />
          <input
            v-if="query.isDetail !== 1"
            placeholder="请输入手机号"
            :value="pwd"
            maxlength="11"
            :disabled="!isShowPwd"
            @input="updatePhone"
          />
          <img
            class="pwd"
            @click="showPwd"
            v-if="query.isDetail !== 1 && isShowPwd"
            src="/static/images/ui_hide_pwd.png"
            alt=""
          />
          <img
            class="pwd"
            @click="showPwd"
            v-if="query.isDetail !== 1 && !isShowPwd"
            src="/static/images/ui_view_pwd.png"
            alt=""
          />
        </div>
      </div>
      <div class="cell">
        <div class="label label2">身份证号:</div>
        <div class="value2 value3">
          <input
            v-if="query.isDetail == 1"
            :disabled="query.isDetail == 1"
            type="text"
            placeholder="请输入身份证号"
            :value="maskedIdCard"
            maxlength="18"
            @input="updateIdCard"
          />
          <input
            v-if="query.isDetail !== 1"
            type="text"
            placeholder="请输入身份证号"
            :value="idCard"
            maxlength="18"
            :disabled="!isIdCardShow"
            @input="updateIdCard"
          />

          <img
            class="pwd"
            @click="showIdCard"
            v-if="query.isDetail !== 1 && isIdCardShow"
            src="/static/images/ui_hide_pwd.png"
            alt=""
          />
          <img
            class="pwd"
            @click="showIdCard"
            v-if="query.isDetail !== 1 && !isIdCardShow"
            src="/static/images/ui_view_pwd.png"
            alt=""
          />
        </div>
      </div> -->
      <div class="cell" @click="selectArea">
        <div class="label label5">申办地区:</div>
        <div class="value2">
          {{ query.divisionFullName }}
        </div>
      </div>
      <div class="cell" @click="showItemType" v-if="itemTypeName !== '其他'">
        <div class="label label2">申办部门:</div>
        <div class="value2 vbr">
          <span>{{ itemTypeName ? itemTypeName : '请选择' }}</span>
          <img src="/static/images/assistant/down.png" />
        </div>
      </div>
      <div class="cell2" @click="showItemList">
        <div class="label label2">办理事项:</div>
        <div class="value2 vbr">
          <span>{{ itemNameText ? itemNameText : '请选择' }}</span>
          <img v-if="itemTypeName !== '其他'" src="/static/images/assistant/down.png" />
        </div>
      </div>
      <div class="cell" v-if="query.isDetail == 1">
        <div class="label">申请时间:</div>
        <div class="value2">{{ query.createTime }}</div>
      </div>
      <div class="cell" v-if="query.isDetail == 1">
        <div class="label" v-if="pageType == 3">拒接说明:</div>
        <div class="label" v-else-if="helpStatus == tg">完成结果:</div>
        <div class="label" v-else>退回说明:</div>
        <div class="value2">{{ query.handleResult || query.refuseReason }}</div>
        <!-- <div class="value2" v>已完成</div> -->
      </div>
    </div>

    <div class="main-box" v-if="pageType == 1">
      <div class="title">小乔助手</div>
      <div class="cell">
        <div class="label">姓名:</div>
        <div class="value2">{{ maskedAssistantName }}</div>
      </div>
      <div class="cell">
        <div class="label">手机号:</div>
        <div class="value2 value3">{{ maskedPhone2 }}</div>
      </div>
      <!-- <div class="cell">
        <div class="label">服务位置:</div>
        <div class="value2">{{ query.serviceLocal }}</div>
      </div>
      <div class="cell">
        <div class="label">服务窗口:</div>
        <div class="value2">{{ query.windowLocal }}</div>
      </div> -->
    </div>

    <div class="main-box" v-if="pageType == 1 && isUser != 1">
      <div v-if="query.handleResult && pageType == 1" class="title">办理结果</div>
      <div style="margin-bottom: 20rpx" v-if="query.handleResult && pageType == 1">
        {{ query.handleResult }}
      </div>
      <div v-if="query.refuseReason && pageType == 1" class="title">退回理由</div>
      <div style="margin-bottom: 20rpx" v-if="query.refuseReason && pageType == 1">
        {{ query.refuseReason }}
      </div>

      <div v-if="pageType == 1" class="title">转派记录</div>
      <div v-if="pageType == 1" class="record-table">
        <div class="table_title">
          <span>操作人</span>
          <span>操作类型</span>
          <span>操作时间</span>
        </div>
        <div class="table_body">
          <div class="table_row" v-for="(item, index) in records" :key="index">
            <span>{{ maskName(item.operateName) }}</span>
            <span :class="{ 'status-back': item.operateType_dictText === '打回' }">
              {{ item.operateType_dictText }}
            </span>
            <span>{{ item.createTime }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="btns" v-if="editType == 0 && query.isDetail !== 1 && isUser != 1">
      <div class="btn" @click="onSubmitProcess(1)">保存</div>
      <div class="btn btn2" @click="onSubmitProcess(0)">提交</div>
    </div>

    <div class="btns" v-if="query.isDetail !== 1 && editType == 1">
      <div class="btn" @click="onSubmit(1)">保存</div>
      <div class="btn btn2" @click="onSubmit(0)">提交</div>
    </div>
    <div class="btns" v-if="editType == 2">
      <div class="btn" @click="showPopup">补充字段</div>
      <div class="btn" @click="onSubmit(1)">保存编辑</div>
      <div class="btn" @click="openPopup" v-if="isShowBtns">转派</div>
    </div>

    <uni-popup ref="popup">
      <div class="popup-content">
        <div class="popup-title">
          <div>补充字段</div>
          <img src="/static/images/admin/close.png" alt="" @click="closePopup" />
        </div>
        <div class="popup-item popup-item2">
          <div class="label">字段名称:</div>
          <div class="value">
            <input type="text" v-model="fieldName" placeholder="请输入字段名称" />
          </div>
        </div>
        <div class="popup-item">
          <div class="label">字段类型:</div>
          <div class="value">
            <e-select v-model="fieldType" clearable :options="fieldTypeList"></e-select>
          </div>
        </div>
        <div class="popup-item">
          <div class="label">是否必填:</div>
          <div class="value">
            <e-select v-model="isRequired" clearable :options="isRequiredList"></e-select>
          </div>
        </div>
        <view class="btn-box">
          <div class="btn" @click="addField">添加</div>
        </view>
      </div>
    </uni-popup>
  </div>
</template>

<script>
import {
  getItemType,
  getItemListNew,
  getJyAssistRecordHandle,
  commitRecord,
  saveRecordNew
} from '@/api/assistant/index.js'
import config from '@/config'
import {
  getProxyManagement,
  fileSaveOrUpdate,
  recordEdit,
  deleteFile,
  addJyHelpHandleRecordField,
  deleteJyHelpHandleRecordField,
  editJyHelpHandleRecordField
} from '@/api/admin'
import dayjs from 'dayjs'
import { helpHandAdminList, helpHandListNew, helpHandList, transfer } from '@/api/home/<USER>'
export default {
  components: {},
  data() {
    return {
      headtitle: '帮办员信息',
      itemList: ['全流程落地', '多流程', '单流程'],
      itemIndex: 0,
      checkboxValue1: [],
      checkboxList1: [],
      record: {},
      // 办理事项
      itemName: '',
      itemList: [],
      itemSelected: [],
      baseUrl: config.baseUrl,
      itemTypeName: '',
      itemLevelName: '',
      itemLevel: '',
      tabType: '',
      // 事项 type
      itemType: 1,
      businessType: 0,
      userInfo: {},
      itemTypeList: [],
      itemLevelList: [],
      query: {},
      itemNameText: '',
      isShowPwd: false,
      isIdCardShow: false,
      pwd: '',
      idCard: '',
      records: [],
      pageType: 0,
      editType: 0,
      fileUrl: '',
      fileName: '',
      extensions: ['pdf', 'doc', 'docx'],
      imgBaseUrl: config.imgBaseUrl,
      fileList: [],
      fieldName: '',
      fieldType: '',
      isRequired: '',
      fieldTypeList: [
        {
          text: '文本框',
          value: 'wbk'
        },
        {
          text: '上传框',
          value: 'sck'
        },
        {
          text: '日期框',
          value: 'rqk'
        }
      ],
      isRequiredList: [
        {
          text: '是',
          value: '1'
        },
        {
          text: '否',
          value: '0'
        }
      ],
      userList: [],
      selectTem: {},
      reason: '',
      // 默认地区
      area: '',
      // 默认事项
      itemText: '',
      // 委托书模板
      wtsFileList: '',
      isShowBtns: false,
      isUser: 0
    }
  },
  async onLoad(option) {
    this.userInfo =
      typeof this.$store.state.user.userInfo == 'string'
        ? JSON.parse(this.$store.state.user.userInfo)
        : this.$store.state.user.userInfo
    this.loginType = uni.getStorageSync('loginType')
    let res = JSON.parse(option.record)
    this.pageType = option.pageType
    this.editType = option.editType || 0
    this.isUser = option.isUser
    this.query = res
    // 如果是办事模式，表单可编辑
    if (this.editType == 1 || this.editType == 2) {
      this.query.isDetail = 0
    }
    this.pwd = res.contactPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    this.idCard = res.idCard.replace(/(\d{3})\d{6}(\d{3}[0-9Xx])/, '$1******$2')
    this.itemTypeName = res.itemType
    this.itemNameText = res.itemName

    if (this.editType == 1 || this.editType == 2 || this.editType == 3) {
      this.area = uni.getStorageSync('area') && JSON.parse(uni.getStorageSync('area'))
      if (!this.area && this.area.recordId !== res.recordId) {
        uni.setStorageSync(
          'area',
          JSON.stringify({
            recordId: res.recordId,
            divisionFullName: res.divisionFullName
          })
        )
      }
      this.itemText = res.itemName
      // 获取转派记录
      this.getJyAssistRecordHandle()
      // 获取帮办列表
      this.getTeamList()
      this.selectTem.assistantId = res.assistantId
      this.selectTem.assistantTel = res.assistantTel
      this.selectTem.serviceLocal = res.serviceLocal
      this.selectTem.windowLocal = res.windowLocal
    }
    // 委托书模板和文件列表
    if (this.pageType == 1) {
      this.fileList = []
      this.wtsFileList = []
      // 获取委托书
      const wtsList = await getProxyManagement({
        itemId: res.itemId
      })
      if (wtsList.result) {
        this.wtsFileList.push(wtsList.result)
      }
      this.fileList.push(...res.fileList)
    }
  },
  async onShow() {
    // 事项类型
    const res = await getItemType({
      divisionLevel: this.query.divisionLevel || 1
    })
    this.itemTypeList = res.result
  },
  computed: {
    maskedPhone() {
      const phone = this.query.contactPhone || ''
      return phone.length === 11 ? phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : phone
    },
    maskedPhone2() {
      const phone = this.query.assistantTel || ''
      return phone.length === 11 ? phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : phone
    },
    maskedIdCard() {
      const idCard = this.query.idCard || ''
      return idCard.length === 18
        ? idCard.replace(/(\d{3})\d{6}(\d{3}[0-9Xx])/, '$1******$2')
        : idCard
    },
    // 小乔助手姓名脱敏，只显示姓氏
    maskedAssistantName() {
      const name = this.query.assistantName || ''
      return name.length > 0 ? name.charAt(0) + '*'.repeat(name.length - 1) : name
    }
  },
  methods: {
    // 姓名脱敏方法，只显示姓氏
    maskName(name) {
      if (!name || name.length === 0) return name
      return name.charAt(0) + '*'.repeat(name.length - 1)
    },
    // 兼容安卓端上传
    uploadFileNew() {
      if (this.editType == 0 || this.editType == 3) {
        uni.showToast({
          icon: 'none',
          title: '流程已提交，无法修改'
        })
        return
      }

      if (window.android && typeof window.android.file === 'function') {
        console.log('[uploadFileNew] 调用安卓上传接口 window.android.file()')
        window.android.file()
      } else {
        console.warn('[uploadFileNew] 没有检测到 window.android.file 方法')
        uni.showToast({
          icon: 'none',
          title: '未检测到安卓上传接口'
        })
      }
    },
    // 转派
    async openPopup() {
      this.onSubmit(3)
    },
    async zpChange() {
      this.selectTem.recordId = this.query.recordId
      this.selectTem.reason = this.reason
      this.selectTem.assistantId = this.selectTem.value
      this.selectTem.isTransfer = '1'
      const res = await transfer(this.selectTem)
      if (res.success) {
        uni.showToast({
          title: '转派成功',
          icon: 'success'
        })
        setTimeout(() => {
          this.$tab.navigateTo('/pages/admin/adminHandlingRecord')
        }, 1000)
      } else {
        uni.showToast({
          title: res.message,
          icon: 'none'
        })
      }
    },
    change(e) {
      // 先清空当前选中项的信息，确保数据会更新
      this.selectTem = {
        ...this.selectTem,
        assistantId: e,
        assistantTel: '',
        serviceLocal: '',
        windowLocal: ''
      }

      // 再设置新的数据
      const selectedUser = this.userList.find((item) => item.value == e)
      if (selectedUser) {
        this.$nextTick(() => {
          this.selectTem.assistantTel = selectedUser.assistantTel
          this.selectTem.serviceLocal = selectedUser.serviceLocal
          this.selectTem.windowLocal = selectedUser.windowLocal
          this.isShowBtns = true
        })
      }
    },
    // 获取团队成员
    async getTeamList() {
      this.userList = []
      const res = await helpHandList({
        divisionId: this.query.divisionId
      })
      res.result.map((item) => {
        this.userList.push({
          text: this.maskName(item.assistantName),
          value: item.assistantId,
          assistantTel: item.assistantTel,
          serviceLocal: item.serviceLocal,
          windowLocal: item.windowLocal
        })
      })
      // this.selectTem = this.userList.find((item) => item.value == this.query.assistantId)
      // console.log('this.selectTem', this.selectTem)
    },
    goBack() {
      if (this.pageType == 1 && (this.editType == 1 || this.editType == 2)) {
        uni.redirectTo({
          url: `/pages/admin/adminHandlingRecord`
        })
      } else {
        uni.navigateBack()
      }
    },
    // 添加补充字段
    async addField() {
      // console.log('添加补充字段')
      const res = await addJyHelpHandleRecordField({
        recordId: this.query.recordId,
        fieldName: this.fieldName,
        fieldType: this.fieldType,
        isRequired: this.isRequired
      })
      if (res.success) {
        uni.showToast({
          title: '添加成功',
          icon: 'success'
        })
        this.getList()
        this.closePopup()
      }
    },
    // 删除补充字段
    async deleteField(item) {
      const res = await deleteJyHelpHandleRecordField(item.id)
      if (res.success) {
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
        this.getList()
      }
    },
    async getList() {
      const query = {
        // pageNo: this.pageNo,
        // pageSize: this.pageSize,
        // keyword: this.keyword,
        recordId: this.query.recordId
      }
      let res = null
      if (this.userInfo.isAssistManger == 0) {
        res = await helpHandListNew(query)
      } else {
        res = await helpHandAdminList(query)
      }
      let isDetail = this.query.isDetail
      this.query = res.result.records[0]
      this.query.isDetail = isDetail
    },
    async delFile(item) {
      const res = await deleteFile({
        id: item.id
      })
      if (res.success) {
        this.fileList.splice(this.fileList.indexOf(item), 1)
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    },
    async delFile2(item) {
      const res = await deleteFile({
        id: item.id
      })
      if (res.success) {
        this.query.supplementFileList.splice(this.query.supplementFileList.indexOf(item), 1)
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    },
    async getJyAssistRecordHandle() {
      const res = await getJyAssistRecordHandle({
        recordId: this.query.recordId,
        pageNo: 1,
        pageSize: 10
      })
      if (res.success) {
        this.records = res.result.records
      }
    },
    showIdCard() {
      this.isIdCardShow = !this.isIdCardShow
      if (!this.isIdCardShow) {
        this.idCard = this.idCard.replace(/(\d{3})\d{6}(\d{3}[0-9Xx])/, '$1******$2')
      } else {
        this.idCard = this.query.idCard
      }
    },
    showPwd() {
      this.isShowPwd = !this.isShowPwd
      if (!this.isShowPwd) {
        this.pwd = this.pwd.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      } else {
        this.pwd = this.query.contactPhone
      }
    },
    updatePhone(event) {
      this.query.contactPhone = event.target.value
      this.pwd = event.target.value
    },
    updateIdCard(event) {
      this.query.idCard = event.target.value
      this.idCard = event.target.value
    },
    selectArea() {
      if (this.query.isDetail == 1) {
        return
      }
      uni.redirectTo({
        url: `/pages/assistant/index?type=1&record=${JSON.stringify(this.query)}&pageType=${
          this.pageType
        }&editType=${this.editType}`
      })
    },
    closePopup() {
      this.$refs.popup.close()
    },
    // 获取事项列表
    async getItemListNewList() {
      const res = await getItemListNew({
        itemType: this.query.itemType
      })
      this.itemList = res.result
    },
    // 事项类型选择
    showItemType() {
      if (this.query.isDetail == 1) {
        return
      }
      uni.showActionSheet({
        itemList: this.itemTypeList.map((i) => i),
        success: (res) => {
          this.itemNameText = ''
          this.query.itemName = ''
          this.itemTypeName = this.itemTypeList[res.tapIndex]
          this.query.itemType = this.itemTypeList[res.tapIndex]
          this.getItemListNewList()
          // 获取帮办员
          this.getTeamList()
        }
      })
    },
    // 事项选择
    showItemList() {
      if (!this.query.itemType) {
        uni.showToast({
          icon: 'none',
          title: '请先选择事项类型'
        })
        return
      }
      uni.showActionSheet({
        itemList: this.itemList.map((i) => i.itemName),
        success: (res) => {
          this.query.itemName = this.itemList[res.tapIndex].itemName
          this.itemNameText = this.itemList[res.tapIndex].itemName
          this.query.itemId = this.itemList[res.tapIndex].itemId
          // 获取帮办员
          this.getTeamList()
        }
      })
    },
    deleteTag(index) {
      this.checkboxValue1.splice(index, 1)
      if (this.checkboxValue1.length < 1) {
        this.checkboxList1.map((i) => {
          i.disabled = false
        })
      }
      this.itemSelected.splice(index, 1)
    },
    copy(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: '复制成功'
          })
        }
      })
    },
    checkboxChange(n) {
      this.itemSelected = []
      if (this.record.isAllItem == '1' && this.tabType == 0 && this.itemType == 1) {
        console.log('jinlaile', this.checkboxList1)
        this.checkboxList1.map((i) => {
          i.disabled = false
        })
        n.map((item) => {
          if (item === '全流程落地') {
            this.checkboxList1.map((i) => {
              if (i.name !== '全流程落地') {
                i.disabled = true
                this.itemSelected.push(i.id)
              }
            })
          } else {
            this.checkboxList1[0].disabled = true
            const itemId = (this.checkboxList1[0].disabled = this.checkboxList1.find(
              (i) => i.name === item
            ).id)
            this.itemSelected.push(itemId)
          }
        })
      } else {
        n.map((item) => {
          const itemId = this.checkboxList1.find((i) => i.name === item).id
          this.itemSelected.push(itemId)
        })
      }
      console.log('itemSelected', this.itemSelected)
    },
    showPopup() {
      this.$refs.popup.open('center')
    },
    itemChange(e) {
      this.itemIndex = e.target.value
    },
    uploadFile(item) {
      if (this.editType == 0 || this.editType == 3) {
        uni.showToast({
          icon: 'none',
          title: '流程已提交，无法修改'
        })
        return
      }
      uni.chooseFile({
        count: 1,
        type: 'file',
        extension: this.extensions,
        success: (res) => {
          const str = res.tempFiles[0].name.substring(0, res.tempFiles[0].name.lastIndexOf('.'))
          const fileType = res.tempFiles[0].name.substring(
            res.tempFiles[0].name.lastIndexOf('.') + 1
          )
          if (!this.extensions.includes(fileType)) {
            uni.showToast({
              icon: 'none',
              title: '请上传PDF、DOC或DOCX格式的文件'
            })
            return
          }

          uni.uploadFile({
            header: {
              Authorization: uni.getStorageSync('ACCESS_TOKEN')
            },
            url: config.baseUrl + '/boot' + '/miniapp/api/assist/upload',
            filePath: res.tempFiles[0].path,
            name: 'file',
            formData: {
              biz: 'img',
              fileFormat: fileType,
              fileName: str,
              fileSize: res.tempFiles[0].size
            },
            success: (uploadFileRes) => {
              if (uploadFileRes.statusCode !== 413) {
                const result = JSON.parse(uploadFileRes.data)
                if (result.success) {
                  if (this.fileList.length > 0) {
                    this.fileList[0].fileUrl = result.message
                  }
                  this.fileList.push({
                    fileName: str,
                    fileFormat: fileType,
                    fileSize: res.tempFiles[0].size,
                    fileUrl: result.message,
                    fileUploadDate: dayjs().format('YYYY-MM-DD HH:mm:ss')
                  })
                  fileSaveOrUpdate([
                    {
                      fileName: str,
                      fileFormat: fileType,
                      fileSize: res.tempFiles[0].size,
                      fileUrl: result.message,
                      fileUploadDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                      recordId: this.query.recordId,
                      itemId: this.query.itemId,
                      itemFieldId: item && item.id,
                      itemFieldName: item && item.title,
                      fileType: 2
                    }
                  ])
                  uni.showToast({
                    title: '上传成功',
                    icon: 'success'
                  })
                } else {
                  uni.showToast({
                    icon: 'none',
                    title: result.message || '上传失败'
                  })
                }
              } else {
                uni.showToast({
                  icon: 'none',
                  title: '上传文件过大'
                })
              }
            },
            fail: (err) => {
              uni.showToast({
                icon: 'none',
                title: '上传失败'
              })
            }
          })
        }
      })
    },
    // 补充字段上传
    uploadFile2(item) {
      if (this.query.supplementFileList.length >= 1) {
        uni.showToast({
          icon: 'none',
          title: '只能上传一个文件'
        })
        return
      }
      uni.chooseFile({
        count: 1,
        type: 'file',
        extension: this.extensions,
        success: (res) => {
          const str = res.tempFiles[0].name.substring(0, res.tempFiles[0].name.lastIndexOf('.'))
          const fileType = res.tempFiles[0].name.substring(
            res.tempFiles[0].name.lastIndexOf('.') + 1
          )
          if (!this.extensions.includes(fileType)) {
            uni.showToast({
              icon: 'none',
              title: '请上传PDF、DOC或DOCX格式的文件'
            })
            return
          }

          uni.uploadFile({
            header: {
              Authorization: uni.getStorageSync('ACCESS_TOKEN')
            },
            url: config.baseUrl + '/boot' + '/miniapp/api/assist/upload',
            filePath: res.tempFiles[0].path,
            name: 'file',
            formData: {
              biz: 'img',
              fileFormat: fileType,
              fileName: str,
              fileSize: res.tempFiles[0].size
            },
            success: (uploadFileRes) => {
              if (uploadFileRes.statusCode !== 413) {
                const result = JSON.parse(uploadFileRes.data)
                if (result.success) {
                  this.query.supplementFileList.push({
                    fileName: str,
                    fileFormat: fileType,
                    fileSize: res.tempFiles[0].size,
                    fileUrl: result.message,
                    fileUploadDate: dayjs().format('YYYY-MM-DD HH:mm:ss')
                  })
                  fileSaveOrUpdate([
                    {
                      fileName: str,
                      fileFormat: fileType,
                      fileSize: res.tempFiles[0].size,
                      fileUrl: result.message,
                      fileUploadDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                      recordId: this.query.recordId,
                      itemId: this.query.itemId,
                      itemFieldId: item && item.id,
                      itemFieldName: item && item.title,
                      fileType: 3
                    }
                  ])
                  uni.showToast({
                    title: '上传成功',
                    icon: 'success'
                  })
                } else {
                  uni.showToast({
                    icon: 'none',
                    title: result.message || '上传失败'
                  })
                }
              } else {
                uni.showToast({
                  icon: 'none',
                  title: '上传文件过大'
                })
              }
            },
            fail: (err) => {
              uni.showToast({
                icon: 'none',
                title: '上传失败'
              })
            }
          })
        }
      })
    },
    // 下载委托书
    downFile(template, type = 0) {
      if (!template.fileUrl) {
        uni.showToast({
          icon: 'none',
          title: '模板文件不存在'
        })
        return
      }
      if (
        (template.fileFormat || template.filedType) === 'png' ||
        (template.fileFormat || template.filedType) === 'jpg'
      ) {
        if (type == 1) {
          uni.previewImage({
            urls: [this.baseUrl + '/boot/' + template.url]
          })
        } else {
          uni.previewImage({
            urls: [this.baseUrl + '/' + template.fileUrl]
          })
        }
        return
      }
      uni.showLoading({
        title: '正在打开...'
      })
      uni.downloadFile({
        url:
          type == 1
            ? this.baseUrl + '/boot/' + template.url
            : this.baseUrl + '/boot/' + template.fileUrl,
        header: {
          Authorization: uni.getStorageSync('ACCESS_TOKEN')
        },
        success: (res) => {
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              fileType: template.fileFormat || template.filedType,
              showMenu: true,
              success: function (res2) {
                uni.hideLoading()
              },
              fail: function (res2) {
                uni.$u.toast('打开文档失败,请检查格式是否正确')
              }
            })
          }
        }
      })
    },
    // 帮办员
    async onSubmit(type = 0) {
      // 补充字段必填
      if (this.query.fieldList) {
        for (let i = 0; i < this.query.fieldList.length; i++) {
          if (this.query.fieldList[i].isRequired == '1' && !this.query.fieldList[i].fieldValue) {
            uni.showToast({
              icon: 'none',
              title: `请填写补充字段`
            })
            return
          }
        }
      }
      // 保存补充字段的编辑
      if (this.query.fieldList) {
        for (let i = 0; i < this.query.fieldList.length; i++) {
          if (this.query.fieldList[i].fieldValue !== this.query.fieldList[i].oldFieldValue) {
            const res = await editJyHelpHandleRecordField({
              id: this.query.fieldList[i].id,
              fieldValue: this.query.fieldList[i].fieldValue
            })
          }
        }
      }
      // 姓名必填
      if (!this.query.contactName) {
        uni.showToast({
          icon: 'none',
          title: '请输入姓名'
        })
        return
      }
      //手机号验证 和必填
      if (!this.query.contactPhone) {
        uni.showToast({
          icon: 'none',
          title: '请输入手机号'
        })
        return
      }
      if (!/^1[3456789]\d{9}$/.test(this.query.contactPhone)) {
        uni.showToast({
          icon: 'none',
          title: '请输入正确的手机号'
        })
        return
      }
      //身份证号验证
      if (!this.query.idCard) {
        uni.showToast({
          icon: 'none',
          title: '请输入身份证号'
        })
        return
      }
      if (
        !/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
          this.query.idCard
        )
      ) {
        uni.showToast({
          icon: 'none',
          title: '请输入正确的身份证号'
        })
        return
      }
      // 办理事项必填
      if (!this.query.itemName) {
        uni.showToast({
          icon: 'none',
          title: '请选择办理事项'
        })
        return
      }
      let res = null
      if (type == 0) {
        // 提交修改状态
        this.query.helpStatus = 'tg'
        res = await recordEdit(this.query)
      } else if (type == 3) {
        // 先编辑在转派
        if (!this.selectTem.assistantId) {
          uni.showToast({
            icon: 'none',
            title: '请先选择转派帮办员'
          })
          return
        }
        this.query.assistantId = this.selectTem.assistantId
        this.query.assistantTel = this.selectTem.assistantTel
        this.query.serviceLocal = this.selectTem.serviceLocal
        this.query.windowLocal = this.selectTem.windowLocal
        this.query.assignReason = this.selectTem.reason
        res = await recordEdit(this.query)
        this.zpChange()
      } else {
        // 编辑 保存
        res = await recordEdit(this.query)
      }
      if (res.success) {
        uni.showToast({
          title: `${type == 0 ? '提交' : '保存'}成功`,
          icon: 'none'
        })
        setTimeout(() => {
          this.$tab.navigateTo('/pages/admin/adminHandlingRecord')
        }, 500)
      }
    },
    // 流程提交
    async onSubmitProcess(type = 0) {
      // 姓名必填
      if (!this.query.contactName) {
        uni.showToast({
          icon: 'none',
          title: '请输入姓名'
        })
        return
      }
      //手机号验证 和必填
      if (!this.query.contactPhone) {
        uni.showToast({
          icon: 'none',
          title: '请输入手机号'
        })
        return
      }
      if (!/^1[3456789]\d{9}$/.test(this.query.contactPhone)) {
        uni.showToast({
          icon: 'none',
          title: '请输入正确的手机号'
        })
        return
      }
      //身份证号验证
      if (!this.query.idCard) {
        uni.showToast({
          icon: 'none',
          title: '请输入身份证号'
        })
        return
      }
      if (
        !/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
          this.query.idCard
        )
      ) {
        uni.showToast({
          icon: 'none',
          title: '请输入正确的身份证号'
        })
        return
      }
      // 办理事项必填
      if (!this.query.itemName) {
        uni.showToast({
          icon: 'none',
          title: '请选择办理事项'
        })
        return
      }
      let res = null
      if (type == 0) {
        res = await commitRecord(this.query)
      } else {
        res = await saveRecordNew(this.query)
      }
      if (res.success) {
        uni.showToast({
          title: `${type == 0 ? '提交' : '保存'}成功`,
          icon: 'none'
        })
        setTimeout(() => {
          this.$tab.navigateTo('/pages/mine/handlingRecord?type=0')
        }, 500)
      }
    }
  },
  mounted() {
    // window.handleFileUri = (base64Str, fileName) => {
    //   console.log('[handleFileUri] 收到文件回调：', { fileName, base64Str })

    //   const fileType = fileName.split('.').pop().toLowerCase()
    //   const fileNameWithoutExt = fileName.replace(/\.[^/.]+$/, '')

    //   if (!this.extensions.includes(fileType)) {
    //     console.warn('[handleFileUri] 不支持的文件格式：', fileType)
    //     uni.showToast({
    //       icon: 'none',
    //       title: '请上传PDF、DOC或DOCX格式的文件'
    //     })
    //     return
    //   }

    //   try {
    //     const byteCharacters = atob(base64Str)
    //     const byteNumbers = new Array(byteCharacters.length)
    //     for (let i = 0; i < byteCharacters.length; i++) {
    //       byteNumbers[i] = byteCharacters.charCodeAt(i)
    //     }
    //     const byteArray = new Uint8Array(byteNumbers)
    //     const blob = new Blob([byteArray])

    //     console.log('[handleFileUri] Blob 构造成功，大小：', blob.size)

    //     const formData = new FormData()
    //     formData.append('file', blob, fileName)
    //     formData.append('biz', 'img')
    //     formData.append('fileFormat', fileType)
    //     formData.append('fileName', fileNameWithoutExt)
    //     formData.append('fileSize', blob.size)

    //     console.log('[handleFileUri] 开始上传...', formData)

    //     fetch(config.baseUrl + '/boot/miniapp/api/assist/upload', {
    //       method: 'POST',
    //       headers: {
    //         Authorization: uni.getStorageSync('ACCESS_TOKEN')
    //       },
    //       body: formData
    //     })
    //       .then((res) => {
    //         console.log('[handleFileUri] 上传返回 response：', res)
    //         return res.json()
    //       })
    //       .then((result) => {
    //         console.log('[handleFileUri] 上传结果：', result)
    //         if (result.success) {
    //           const fileData = {
    //             fileName: fileNameWithoutExt,
    //             fileFormat: fileType,
    //             fileSize: blob.size,
    //             fileUrl: result.message,
    //             fileUploadDate: dayjs().format('YYYY-MM-DD HH:mm:ss')
    //           }

    //           if (this.fileList.length > 0) {
    //             this.fileList[0].fileUrl = result.message
    //           }

    //           this.fileList.push(fileData)

    //           fileSaveOrUpdate([
    //             {
    //               ...fileData,
    //               recordId: this.query.recordId,
    //               itemId: this.query.itemId,
    //               fileType: 2
    //             }
    //           ])

    //           uni.showToast({
    //             title: '上传成功',
    //             icon: 'success'
    //           })
    //         } else {
    //           console.warn('[handleFileUri] 上传失败：', result.message)
    //           uni.showToast({
    //             icon: 'none',
    //             title: result.message || '上传失败'
    //           })
    //         }
    //       })
    //       .catch((err) => {
    //         console.error('[handleFileUri] 上传异常：', err)
    //         uni.showToast({
    //           icon: 'none',
    //           title: '上传失败'
    //         })
    //       })
    //   } catch (e) {
    //     console.error('[handleFileUri] base64 解析异常：', e)
    //     uni.showToast({
    //       icon: 'none',
    //       title: '解析失败，可能不是标准 base64'
    //     })
    //   }
    // }

    window.handleFileUri = (base64Str, fileName) => {
      // console.log('[handleFileUri] 收到文件回调：', { fileName, base64Str })
      // uni.showToast({
      //   title: '收到文件回调：' + base64Str,
      //   icon: 'none'
      // })

      const fileType = fileName.split('.').pop().toLowerCase()
      const fileNameWithoutExt = fileName.replace(/\.[^/.]+$/, '')

      // 检查文件类型是否支持
      if (!this.extensions.includes(fileType)) {
        console.warn('[handleFileUri] 不支持的文件格式：', fileType)
        uni.showToast({
          icon: 'none',
          title: '请上传PDF、DOC或DOCX格式的文件'
        })
        return
      }

      try {
        let byteArray

        // 判断 base64Str 是否为字节数组
        if (base64Str instanceof Uint8Array) {
          // 如果是 Uint8Array，直接使用
          byteArray = base64Str
        } else if (
          Array.isArray(base64Str) &&
          base64Str.every((num) => typeof num === 'number' && num >= 0 && num <= 255)
        ) {
          // 如果是普通数组 [72, 101, ...]，转换为 Uint8Array
          byteArray = new Uint8Array(base64Str)
        } else {
          // 按原始逻辑处理 Base64 字符串
          const byteCharacters = atob(base64Str)
          const byteNumbers = new Array(byteCharacters.length)
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i)
          }
          byteArray = new Uint8Array(byteNumbers)
        }

        // 构造 Blob
        const blob = new Blob([byteArray])
        console.log('[handleFileUri] Blob 构造成功，大小：', blob.size)

        // 构造 FormData
        const formData = new FormData()
        formData.append('file', blob, fileName)
        formData.append('biz', 'img')
        formData.append('fileFormat', fileType)
        formData.append('fileName', fileNameWithoutExt)
        formData.append('fileSize', blob.size)

        console.log('[handleFileUri] 开始上传...', formData)

        // 发起上传请求
        fetch(config.baseUrl + '/boot/miniapp/api/assist/upload', {
          method: 'POST',
          headers: {
            Authorization: uni.getStorageSync('ACCESS_TOKEN')
          },
          body: formData
        })
          .then((res) => {
            console.log('[handleFileUri] 上传返回 response：', res)
            return res.json()
          })
          .then((result) => {
            console.log('[handleFileUri] 上传结果：', result)
            if (result.success) {
              const fileData = {
                fileName: fileNameWithoutExt,
                fileFormat: fileType,
                fileSize: blob.size,
                fileUrl: result.message,
                fileUploadDate: dayjs().format('YYYY-MM-DD HH:mm:ss')
              }

              if (this.fileList.length > 0) {
                this.fileList[0].fileUrl = result.message
              }

              this.fileList.push(fileData)

              fileSaveOrUpdate([
                {
                  ...fileData,
                  recordId: this.query.recordId,
                  itemId: this.query.itemId,
                  fileType: 2
                }
              ])

              uni.showToast({
                title: '上传成功',
                icon: 'success'
              })
            } else {
              console.warn('[handleFileUri] 上传失败：', result.message)
              uni.showToast({
                icon: 'none',
                title: result.message || '上传失败'
              })
            }
          })
          .catch((err) => {
            console.error('[handleFileUri] 上传异常：', err)
            uni.showToast({
              icon: 'none',
              title: '上传失败'
            })
          })
      } catch (e) {
        console.error('[handleFileUri] 处理文件异常：', e)
        uni.showToast({
          icon: 'none',
          title: '文件处理失败，可能格式不正确'
        })
      }
    }
  }
}
</script>
<style>
page {
  background-color: #f7fbff;
  padding: 20rpx;
}
</style>
<style lang="scss" scoped>
.back_box {
  height: 60rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  img {
    width: 28rpx;
    height: 40rpx;
    transform: rotate(180deg);
    margin-right: 10rpx;
  }
}
.main-box {
  padding: 30rpx;
  margin: 30rpx auto;
  min-height: 360rpx;
  background: url('/static/images/admin/new2.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  .title {
    font-family: SourceHanSerifCN, SourceHanSerifCN;
    font-weight: 500;
    font-size: 34rpx;
    color: #2f3644;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: inline-block;
      width: 47rpx;
      height: 31rpx;
      background: url('/static/images/admin/new3.png') no-repeat center center;
      background-size: 100% 100%;
      margin-right: 10rpx;
    }
    margin-bottom: 30rpx;
  }
  .cell {
    height: 80rpx;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
  }
  .cell2 {
    min-height: 80rpx;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
  }
  .label {
    height: 80rpx;
    line-height: 80rpx;
    width: 160rpx;
    text-align: right;
    margin-right: 15rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 30rpx;
    color: #333333;
  }
  .label2 {
    position: relative;
    &::after {
      content: '*';
      position: absolute;
      left: 20rpx;
      top: 5rpx;
      color: #f24f4b;
    }
  }
  .label3 {
    position: relative;
    &::after {
      content: '*';
      position: absolute;
      left: 80rpx;
      top: 5rpx;
      color: #f24f4b;
    }
  }
  .label5 {
    position: relative;
    &::after {
      content: '*';
      position: absolute;
      left: -10rpx;
      top: 5rpx;
      color: #f24f4b;
    }
  }
  .label4 {
    position: relative;
    &::after {
      content: '*';
      position: absolute;
      left: 50rpx;
      top: 5rpx;
      color: #f24f4b;
    }
  }
  .value {
    height: 80rpx;
    line-height: 80rpx;
    flex: 1;
    background: #f6f6f6;
    border-radius: 6rpx;
    padding-left: 20rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 30rpx;
    color: #444444;
    font-style: normal;
    input {
      height: 80rpx;
      font-size: 30rpx;
    }
  }
  .value2 {
    min-height: 60rpx;
    flex: 1;
    background: #ebf5ff;
    border-radius: 6rpx;
    padding: 0 20rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 26rpx;
    color: #2f3644;
    font-style: normal;
    display: flex;
    align-items: center;
    justify-content: space-between;
    img {
      width: 28rpx;
      height: 14rpx;
    }
    input {
      font-size: 26rpx;
    }
  }
  .vbr {
    border: 1rpx solid #467c9c;
  }
  .value3 {
    position: relative;
    .pwd {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 36rpx;
      height: 32rpx;
    }
  }
}
.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0);
  z-index: 1000;
}

.popup-content {
  background: url('/static/images/admin/new2.png') no-repeat center center !important;
  background-size: 100% 100% !important;
  position: relative;
  padding: 20rpx;
  width: 600rpx;
  .popup-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: SourceHanSerifCN, SourceHanSerifCN;
    font-weight: 500;
    font-size: 34rpx;
    color: #2d3645;
    margin: 20rpx 0;
    padding: 0 20rpx;
    & > div {
      &::before {
        content: '';
        display: inline-block;
        width: 47rpx;
        height: 31rpx;
        background: url('/static/images/admin/new3.png') no-repeat center center;
        background-size: 100% 100%;
        margin-right: 10rpx;
      }
    }
    img {
      width: 41rpx;
      height: 41rpx;
    }
  }
  .popup-item {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 50rpx;
    .label {
      width: 160rpx;
      font-size: 30rpx;
      color: #333333;
      text-align: right;
    }
    .value {
      flex: 1;
      border-radius: 6rpx;
      // border: 2rpx solid #dddddd;
      // padding: 10rpx 20rpx;
      font-size: 30rpx;
      color: #333333;
    }
  }
  .popup-item2 {
    .value {
      padding: 0px 10px;
      min-height: 34px;
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: row;
      align-items: center;
      border-radius: 3px;
      border: 1px solid #dddddd;
      margin-left: 5px;
    }
  }
  .btn-box {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .btn {
    width: 230rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 8rpx;
    border: 2rpx solid #057ffe;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 34rpx;
    color: #057ffe;
    text-align: center;
  }
}
.submit {
  width: 600rpx;
  margin: 20rpx auto;
}
.mini-btn {
  width: 120rpx;
  height: 48rpx;
  border-radius: 32rpx;
  border: 1rpx solid #d0d0d0;
  text-align: center;
  line-height: 48rpx;
}
.record-table {
  width: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;

  .table_title {
    display: flex;
    align-items: center;
    height: 88rpx;
    background: #f4f8ff;
    border-bottom: 2rpx solid #e8e8e8;

    span {
      flex: 1;
      text-align: center;
      font-size: 28rpx;
      font-weight: 500;
      color: #2e8aff;
    }
  }

  .table_body {
    background: #ffffff;

    .table_row {
      display: flex;
      align-items: center;
      height: 88rpx;
      border-bottom: 2rpx solid #e8e8e8;

      &:last-child {
        border-bottom: none;
      }

      span {
        flex: 1;
        text-align: center;
        font-size: 28rpx;
        color: #333333;

        &.status-back {
          color: #f56c6c;
        }
      }
    }
  }
}
.proxy-upload {
  padding: 20rpx 0;
  .title {
    font-size: 30rpx;
    color: #333;
    margin-bottom: 20rpx;
    font-weight: 500;
  }
  .upload-area {
    min-height: 160rpx;
    border-radius: 6rpx;
    padding: 20rpx;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    .upload-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 120rpx;
      cursor: pointer;
      img {
        width: 48rpx;
        height: 48rpx;
        margin-bottom: 10rpx;
      }
      .upload-text {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
}
.file-box {
  & > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    color: #057ffe;
  }
  .file-name {
    font-size: 26rpx;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .file-actions {
    display: flex;
    align-items: center;
    gap: 15rpx;
  }
}
.btns {
  margin-top: 80rpx;
  display: flex;
  justify-content: space-around;
  .btn {
    width: 180rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 8rpx;
    border: 2rpx solid #057ffe;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 34rpx;
    color: #057ffe;
    text-align: center;
  }
  // .btn2 {
  //   background: #057ffe;
  //   border-radius: 8rpx;
  //   color: #fff;
  // }
}
.field-box {
  margin-bottom: 20rpx;
  .field-item {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    .field-item-label {
      width: 200rpx;
      font-size: 30rpx;
      color: #333333;
      text-align: right;
      position: relative;
      margin-right: 10rpx;
    }
    .required {
      &::after {
        content: '*';
        position: absolute;
        right: 0rpx;
        top: 8rpx;
        color: #f24f4b;
        font-size: 36rpx;
      }
    }
    .field-item-value {
      flex: 1;
      background: #ebf5ff;
      border-radius: 6rpx;
      padding: 0 20rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26rpx;
      color: #2f3644;
      display: flex;
      align-items: center;
    }
    .field-item-value2 {
      background: none;
      img {
        width: 38rpx;
        height: 35rpx;
      }
    }
    .field-item-delete {
      width: 30rpx;
      height: 30rpx;
      margin-left: 20rpx;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
::v-deep input {
  width: 100% !important;
  height: 60rpx !important;
}
::v-deep .uni-select {
  border: none !important;
  padding: 0 !important;
}
</style>
<style lang="scss">
::v-deep .u-tag__close {
  z-index: 2 !important;
}
.u-tag--medium {
  min-height: 52rpx !important;
}
</style>
