<template>
	<view :style="{
      paddingTop:  Props.statusBarHeight + 'px'
    }">
		<navbar :Props="Props" :title='headtitle' @black="black"></navbar>
		<view class="box">
			<view class="header">
				<image class="image" src="@/static/images/home/<USER>" mode=""></image>
				<view class="title">
					{{newsList.itemName}}
				</view>
			</view>
			<view class="tips">
				您需要准备的材料清单如下
			</view>
			<view class="file">
				<view class="boxs">
					<view class="items" v-for="(report, index) in newsList.downList" :key="index">
						<view class="content">
							<view class="">{{index + 1}}:</view>
							<view class="text">{{report.name}}</view>
							<view class="down" @click="downFile(report.path, report.name)">下载</view>
							<!-- <image class="image" :src="report.path" mode=""></image> -->
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import config from '@/config'
	import navbar from '@/components/Navbar/index.vue'
	import {
		getAccessToken
	} from '@/utils/auth'
	import {
		getOneTimeNotificationDetail
	} from "@/api/handled/index.js"
	export default {
		components: {
			navbar
		},
		data() {
			return {
				headtitle: '一次性告知',
				itemId: '',
				Props: {
					imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: "", //导航高度(动态获取传参)
					bgColor: "", //导航栏背景色,不传参则默认#9CF
					capsuleTop: "", //胶囊顶部距离(动态获取传参)
					textColor: "", //导航标题字体颜色(不传默认#FFF)
					iconColor: "", //icon图标颜色(不传默认#FFF)
					blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
					backText: "", //默认字体(返回)
				},
				newsList: []
			}
		},
		onLoad: function(data) {
			const that = this
			console.log(data, '2222')
			that.itemId = data.itemId
			that.Props.statusBarHeight = getApp().globalData.statusBarHeight
			that.Props.capsuleTop = getApp().globalData.capsuleTop
		},
		mounted() {
			this.getList()
		},
		methods: {
			downFile(path, name) {
				console.log(path, 'path-------')
				const dotIndex = path.lastIndexOf('.');
				// 从 '.' 后一位开始截取到字符串结束
				const fileExtension = path.substring(dotIndex + 1);
				console.log(fileExtension, '222')
				if(fileExtension == 'png' || fileExtension == 'jpg') {
					console.log(98888)
					uni.previewImage({
						urls: [path]
					})
					return
				}
				const nameindex = name.lastIndexOf('.');
				// 从字符串开始截取到 '.' 之前的部分
				const fileName = name.substring(0, nameindex);
				console.log(fileName, '-0000000')
				wx.downloadFile({
					url: path,
					header: {
						Authorization: getAccessToken()
					},
					filePath: `${wx.env.USER_DATA_PATH}/${fileName }.${fileExtension}`,
					success: (res) => {
						if (res.statusCode === 200) {
							wx.openDocument({
								filePath: res.filePath,
								fileType: fileExtension,
								showMenu: true,
								success: function(res2) {
									uni.hideLoading()
								},
								fail: function(res2) {
									uni.$u.toast('打开文档失败,请检查格式是否正确')
								}
							})
						}
					}
				})
			},
			async getList() {
				const that = this
				const res = await getOneTimeNotificationDetail({
					itemId: that.itemId,
				})
				const staticDomainURL = config.staticDomainURL
				console.log(res, '09999')
				that.newsList = res?.result
				if (that.newsList.materialAttachment.includes(',')) {
					that.newsList.materialAttachment = that.newsList.materialAttachment.split(',')
				} else {
					that.newsList.materialAttachment = [that.newsList.materialAttachment]
				}
				that.newsList.downList = Object.values(that.newsList.materialAttachment).map(path => {
					console.log(path, '22222')
					// 获取文件名的起始位置（即最后一个斜杠之后）
					let startIndex = path.lastIndexOf("/") + 1;

					// 获取文件名的结束位置（即最后一个下划线之前）
					let endIndex = path.lastIndexOf("_");

					// 获取文件名
					let fileName = path.substring(startIndex, endIndex);

					// 获取文件扩展名
					let fileExtension = path.substring(path.lastIndexOf("."));

					// 拼接文件名和扩展名
					let newFileName = fileName + fileExtension;
					return {
						name: newFileName,
						path: staticDomainURL + path
					};
				});


				console.log(that.newsList.downList, '-1---------------')
			},
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		}
	}
</script>

<style lang="scss">
	.box {
		position: relative;
		// padding: 0 32rpx;
		text-align: center;

		.header {
			width: 720rpx;
			height: 149rpx;
			position: relative;
			margin: 0 auto;

			.image {
				width: 720rpx;
				height: 149rpx;
			}

			.title {
				position: absolute;
				top: 50rpx;
				left: 51rpx;
				font-weight: 500;
				font-size: 36rpx;
				color: rgba(0, 0, 0, 0.9);
				line-height: 36rpx;
				text-align: left;
				font-style: normal;
				padding-right: 20rpx;
			}
		}

		.tips {
			font-weight: 400;
			font-size: 30rpx;
			color: #666666;
			line-height: 42rpx;
			text-align: left;
			font-style: normal;
			padding: 0 32rpx;
			margin: 24rpx 0;
		}

		.file {
			width: 686rpx;
			min-height: 848rpx;
			margin: 0 auto;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.06);
			border-radius: 32rpx;
			padding: 50rpx 30rpx;
			margin-bottom: 60rpx;
		}

		.content {
			display: flex;
			// align-items: center;
			// justify-content: space-between;	
			margin-top: 42rpx;
			border-bottom: 2rpx solid #E7F0FF;
			padding-bottom: 24rpx;
			.text {
				width: 402rpx;
				font-weight: 500;
				font-size: 30rpx;
				color: #333333;
				line-height: 42rpx;
				text-align: left;
				font-style: normal;
				word-wrap: break-word;
				margin-right: 90rpx;
				margin-left: 10rpx;
			}
		}

		.down {
			width: 100rpx;
			height: 46rpx;
			border-radius: 8rpx;
			border: 1rpx solid #057FFE;
			font-weight: 400;
			font-size: 25rpx;
			color: #057FFE;
			line-height: 46rpx;
			text-align: center;
			font-style: normal;
		}
	}
</style>