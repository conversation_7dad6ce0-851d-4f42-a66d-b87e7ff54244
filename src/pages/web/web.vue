<template>
	<view>    
		<web-view :src="url"></web-view> 
	</view>
</template>
<script>
	export default {
		data() {
			return {
				url: ''
			};
		},
		onLoad(e) {
			this.url = 'https://apis.map.qq.com/tools/poimarker?type=0&marker=coord:39.96554,116.26719;title:成都;addr:北京市海淀区复兴路32号院&key=G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV&referer=myapp'
			console.log(e, '9888')
			// this.url = 'https://apis.map.qq.com/uri/v1/routeplan?type=bus&from=我的家&fromcoord=39.980683,116.302&to=中关村&tocoord=39.9836,116.3164&policy=1&referer=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77'
			// this.url = 'https://mapapi.qq.com/web/mapComponents/locationMarker/v/index.html?type=0&marker=coord:'+ e.latitude +',' + e.longitude + ';title:目标地;addr:目标地&referer=myapp&key=G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV'
			// this.url = 'https://mapapi.qq.com/web/mapComponents/locationMarker/v/index.html?type=0&?marker=coord:'+ e.longitude + ',' + e.latitude + ';title:超好吃冰激凌;addr:手帕口桥北铁路道口;' + '&referer=myapp&key=G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV'
			console.log(this.url, '')
		}
	}
</script>
