<template>
	<view class="tzgg">
		<image class="cover-image" src="@/static/images/home/<USER>" mode=""></image>
		<image class="title" src="@/static/images/home/<USER>" mode=""></image>
		<image @click="more()" class="more" src="@/static/images/home/<USER>"></image>
		<view class="tzgg_reports">
			<view class="report" v-for="(report, index) in reportList" :key="index">
				<image class="content_background" src="@/static/images/home/<USER>" mode=""></image>
				<view class="content">
					<view :style="{ fontSize: fontSizeMedium }" class="text">{{report.title}}</view>
					<view class="header">
						<view :style="{ fontSize: fontSizeSmall }" class="type">来源：</view>
						<view :style="{ fontSize: fontSizeSmall }" class="type">{{report.releaseSource}}</view>
						<view class="stick">|</view>
						<view :style="{ fontSize: fontSizeSmall }" class="date">上传时间：</view>
						<view :style="{ fontSize: fontSizeSmall }" class="date">{{report.createTime}}</view>
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		getNoticelistByUser
	} from "@/api/home/<USER>"
	import {
		forEach
	} from "lodash"
	export default {
		onLoad: function() {},
		data() {
			return {
				reportList: [{
						type: '政务中心',
						date: '2024-08-20',
						text: ' 尊敬的用户，为了提供更好的服务体验，我们将在北京时间9月12日凌......'
					},
					{
						type: '政务中心',
						date: '2024-08-20',
						text: '我们计划于9月15日晚上10 我们计划于9月15日晚上10点开始进行系统......'
					},
					{
						type: '政务中心',
						date: '2024-08-20',
						text: '通知：本次系统升级将带来全新的用户界面和增强的功能模块，以满......'
					},
					{
						type: '政务中心',
						date: '2024-08-20',
						text: '温馨提示：系统将于今晚11点进入维护状态，以完成最新的软件版本......'
					},
					{
						type: '政务中心',
						date: '2024-08-20',
						text: '亲爱的用户，系统将在接下来的周末进行一次重大更新，此次更新......'
					}
				]
			}
		},
		mounted() {
			this.getComplaintList()
		},
		methods: {
			async getComplaintList() {

				const param = {
					keyWord: ''
				}
				const res = await getNoticelistByUser(param)
				this.reportList = res.result.sysNoticeList
				this.reportList.forEach((item, index) => {
					item.createTime = item.createTime.substring(0, 10);
				})
			},
			more() {
				uni.navigateTo({
					url: '/pages/Realtime/index'
				});
			}
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		}
	}
</script>

<style lang="scss">
	.text {
		font-weight: 500;
		font-size: 28rpx;
		color: #444;
		font-style: normal;
	}

	.tzgg {
		position: relative;
		margin: 0 12rpx;

		.cover-image {
			width: 726rpx;
			height: 1070rpx;
		}

		.title {
			position: absolute;
			width: 175rpx;
			height: 50rpx;
			left: 40rpx;
			top: 46rpx;
		}

		.more {
			position: absolute;
			width: 162rpx;
			height: 44rpx;
			right: 52rpx;
			top: 32rpx;
		}

		.tzgg_reports {
			background-color: #FFF;
			display: flex;
			flex-direction: column;
			width: 630rpx;
			position: absolute;
			left: 40rpx;
			top: 91rpx;

			.report {
				width: 630rpx;
				position: relative;
				margin-top: 20rpx;

				.content_background {
					width: 630rpx;
					height: 167rpx;
				}

				.content {
					width: 630rpx;
					height: 167rpx;
					position: absolute;
					top: 0rpx;
					left: 0rpx;
					padding: 22rpx 54rpx 0 44rpx;

					.type {
						font-family: SourceHanSansCN, SourceHanSansCN;
						font-weight: 400;
						font-size: 24rpx;
						color: #73B1DB;
						line-height: 36rpx;
						font-style: normal;
					}

					.stick {
						font-family: SourceHanSansCN, SourceHanSansCN;
						font-weight: 400;
						font-size: 24rpx;
						color: #73B1DB;
						line-height: 36rpx;
						font-style: normal;
						margin: 0 6rpx;
					}

					.date {
						font-family: SourceHanSansCN, SourceHanSansCN;
						font-weight: 400;
						font-size: 24rpx;
						color: #73B1DB;
						line-height: 36rpx;
						font-style: normal;
					}

					.header {
						display: flex;
						align-items: center;
						margin-top: 13rpx;
					}
				}

			}

		}


	}
</style>