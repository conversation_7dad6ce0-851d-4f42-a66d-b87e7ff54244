<template>
	<view class="content">
		<!-- 宫格组件 -->
		<view :style="{ fontSize: fontSizeLarge }">特色服务</view>
		<view class="grid-body" :style="{ fontSize: fontSizeMedium }">
			<uni-grid :column="4" :showBorder="false" >
				<uni-grid-item v-for="(report, index) in FeatureList" :key="index" >
					<view class="grid-item-box" @click="changeGrid(report.name)">
						<image class="image" :src="report.photoUrl" mode=""></image>
						<text class="text">{{ report.name }}</text>
					</view>
				</uni-grid-item>
			</uni-grid>
		</view>
	</view>
</template>

<script>
	import {
		getFeatureList
	} from "@/api/home/<USER>"
	export default {
		data() {
			return {
				FeatureList: []
			};
		},
		methods: {
			changeGrid(e) {
				console.log(e, '2222')
			},
			async getComplaintList() {
				const param = {

				}
				const res = await getFeatureList(param)
				console.log(res, '2222')
				this.FeatureList = res.data
			},
		},
		mounted() {
			this.getComplaintList()
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		},
	}
</script>

<style lang="scss">
	.content {
		margin: 30rpx;
	}

	.text {
		text-align: center;
		margin-top: 10rpx;
	}

	.grid-item-box {
		flex: 1;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 15px 0;
	}
	.image {
		width: 60rpx;
		height: 60rpx;
	}
</style>