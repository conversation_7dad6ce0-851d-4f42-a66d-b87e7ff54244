<template>
	<view>
		<view class="home-container" :style="{paddingTop:  Props.statusBarHeight + 'px'}">
			<navbar :Props="Props" :title="title" @black="black"></navbar>
			<!-- <button class="system-mode" size="mini" @click='changeMode'>关怀模式</button> -->
			<!-- 轮播图 -->
			<swiper-image-vue></swiper-image-vue>
			<!-- 政务服务地图 -->
			<service-map-vue></service-map-vue>
			<!-- 小乔帮办 -->
			<xiao-qiao-assistant-vue></xiao-qiao-assistant-vue>
			<!-- 惠企服务 -->
			<enterprise-collaboration-vue></enterprise-collaboration-vue>
			<!-- 实时信息 -->
			<real-time-info-vue></real-time-info-vue>
			<image @click="answers" class="robot" src="@/static/images/home/<USER>" mode=""></image>
		</view>

		<TabBar :current="current" :tabBarList="tabBerLists" />
	</view>
</template>

<script>
	import swiperImageVue from './swiper-image.vue'
	import enterpriseCollaborationVue from './enterprise-collaboration.vue'
	import serviceMapVue from './service-map.vue'
	import realTimeInfoVue from './real-time-info.vue'
	import xiaoQiaoAssistantVue from './xiao-qiao-assistant.vue'
	import navbar from '@/components/Navbar/index.vue'
	import global from '@/utils/global.js'
	import TabBar from '@/components/TabBar/index.vue'
	export default {
		components: {
			swiperImageVue,
			enterpriseCollaborationVue,
			serviceMapVue,
			realTimeInfoVue,
			xiaoQiaoAssistantVue,
			navbar,
			TabBar
		},
		data() {
			return {
				title: '首页',
				Props: {
					imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: '', //导航高度(动态获取传参)
					bgColor: '', //导航栏背景色,不传参则默认#9CF
					capsuleTop: '', //胶囊顶部距离(动态获取传参)
					textColor: '', //导航标题字体颜色(不传默认#FFF)
					iconColor: '', //icon图标颜色(不传默认#FFF)
					blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
					backText: '' //默认字体(返回)
				},
				tabBerLists: [],
				current: 0
			}
		},
		onShow() {
			this.tabBerLists = uni.getStorageSync('tabBarList')
			global.tabBarCurrent = 0
			this.current = global.tabBarCurrent ? global.tabBarCurrent : 0
		},
		onLoad() {
			this.Props.statusBarHeight = getApp().globalData.statusBarHeight
			this.Props.capsuleTop = getApp().globalData.capsuleTop
			console.log(this.Props, 'props')

			// uni.hideTabBar({
			// 	animation: false,
			// 	success: () => {},
			// 	fail: () => {
			// 		console.log('隐藏失败')
			// 	}
			// })
		},
		methods: {
			changeMode() {
				console.log('mode')
			},
			black() {
				console.log('返回上一页回调事件')
			},
			answers() {
				uni.navigateTo({
					url: '/pages/ai/index'
				});
			}
		},

		onReachBottom() {
			uni.$emit('loadMore')
		}
	}
</script>

<style lang="scss">
	.robot {
		width: 105rpx;
		height: 122rpx;
		position: fixed;
		right: 16rpx;
		top: 900rpx;
	}

	.home-container {
		background-color: #f6f9fe;
	}

	.system-mode {
		position: fixed;
		top: 5px;
		left: 10px;
		z-index: 999;
	}

	/* #ifndef APP-NVUE */
	page {
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		background-color: #fff;
		min-height: 100%;
		height: auto;
	}

	view {
		font-size: 14px;
		line-height: inherit;
	}

	/* #endif */
</style>
