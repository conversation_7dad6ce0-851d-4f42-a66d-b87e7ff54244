<template>
	<view class="cover-content">
		<view class="cover-view">
			<image class="cover-image" src="@/static/images/home/<USER>" mode=""></image>
			<image class="inlet" @click="$tab.switchTab('/pages/assistant/index')"
				src="@/static/images/home/<USER>" mode="">
			</image>
			<view class="cover-sub">
				<view class="cbfw_img">
					<image class="cbfw" src="@/static/images/home/<USER>" mode=""></image>
					<image class="change" @click="batch" src="@/static/images/home/<USER>" mode=""></image>
				</view>
				<view class="xq_reports" v-for="(report, index) in reportList" :key="index">
					<view class="content" @click="detail(report.item)">
						<view class="title">{{index + 1}}.</view>
						<view :style="{ fontSize: fontSizeMedium }"  class="text">{{report.item_dictText}}</view>
						<image class="back" src="@/static/images/home/<USER>" mode=""></image>
					</view>
					<view v-show="index != 4" class="line"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getOneTimeNotificationList
	} from "@/api/handled/index.js"
	export default {
		data() {
			return {
				reportList: [{
						text: '国有建设用地使用权成交确认书流程'
					},
					{
						text: '国有建设用地使用权出让合同（土地出让合同）'
					},
					{
						text: '国有建设用地使用权首次登记（土地证）'
					},
					{
						text: '企业投资项目备案'
					},
					{
						text: '企业投资项目备案'
					},
				]
			};
		},
		methods: {
			batch() {
				this.getList()
			},
			changeGrid(e) {
				console.log(e)
				// this.$modal.showToast('模块建设中~')
			},
			detail(item) {
				console.log(item, 'item')
				uni.navigateTo({
					url: '/pages/assistant/oneTime?item=' + item
				});
			},
			async getList() {
				const res = await getOneTimeNotificationList()
				console.log(res, '09999')
				this.reportList = res?.result
				// this.params.total = res?.result?.total		
			},
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		},
		mounted() {
			this.getList()
		},
	}
</script>

<style lang="scss">
	.cover-content {
		padding: 0 32rpx;
		margin-bottom: 26rpx;
		margin-top: 10rpx;
		.cover-view {
			position: relative;
			width: 686rpx;
			height: 868rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 2rpx 12rpx 0rpx rgba(0, 108, 126, 0.16);
			border-radius: 40rpx;

			.cover-image {
				width: 635rpx;
				height: 170rpx;
				margin: 26rpx 26rpx 0 26rpx;
			}

			.inlet {
				position: absolute;
				width: 134rpx;
				height: 50rpx;
				top: 86rpx;
				right: 56rpx;
			}

			.cover-sub {
				margin-top: 43rpx;
				padding: 0 26rpx;

				.cbfw_img {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 50rpx;
					.cbfw {
						width: 157rpx;
						height: 50rpx;
					}

					.change {
						width: 162rpx;
						height: 50rpx;
					}
				}

				.xq_reports {

					.content {
						display: flex;
						

						.title {
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 28rpx;
							color: #4278F9;
							line-height: 40rpx;
							text-align: left;
							font-style: normal;
							margin-right: 8rpx;
						}

						.text {
							width: 600rpx;
							font-family: PingFangSC, PingFang SC;
							font-weight: 500;
							font-size: 28rpx;
							color: #444;
							line-height: 40rpx;
							text-align: left;
							font-style: normal;
						}
						.back {
							margin-top: 8rpx;
							width: 14rpx;
							height: 22rpx;
						}
					}

					.line {
						width: 634rpx;
						height: 1rpx;
						background: #D9E0E7;
						margin: 30rpx 0;
					}
				}
			}
		}
	}
</style>