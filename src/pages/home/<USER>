<template>
	<view class="cover-content">
		<view class="cover-view">
			<image class="cover-image" src="@/static/images/home/<USER>"></image>
			<image class="title" src="@/static/images/home/<USER>"></image>
			<image @click="$tab.navigateTo('/pages/map/index')" class="topic" src="@/static/images/home/<USER>"></image>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			};
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		},
	}
</script>

<style lang="scss">

	.cover-content {
		padding: 0 20rpx;
		margin-top: -40rpx;
	}

	.cover-view {
		position: relative;
	}

	.cover-image {
		width: 710rpx;
		height: 259rpx;
	}

	.title {
		position: absolute;
		width: 145rpx;
		height: 82rpx;
		top: 88rpx;
		left: 232rpx;
	}

	.topic {
		position: absolute;
		width: 182rpx;
		height: 48rpx;
		top: 104rpx;
		right: 73rpx;
	}
</style>