<template>
	<view class="hqfw">
		<image class="background" src="@/static/images/home/<USER>" mode=""></image>
		<image class="title" src="@/static/images/home/<USER>" mode=""></image>
		<!-- 宫格组件 -->
		<view class="grid-body" :style="{ fontSize: fontSizeMedium }">
			<uni-grid :column="4" :showBorder="false">
				<uni-grid-item v-for="(report, index) in CooperationList" :key="index">
					<view class="grid-item-box" @click="changeGrid(report.name)">
						<image class="image" :src="report.photoUrl" mode=""></image>
						<text :style="{ fontSize: fontSizeMedium }"  class="text">{{ report.title }}</text>
					</view>
				</uni-grid-item>
			</uni-grid>
		</view>
	</view>
</template>

<script>
	import {
		getCooperationList
	} from "@/api/home/<USER>"
	import config from '@/config'
	export default {
		data() {
			return {
				CooperationList: []
			};
		},
		methods: {
			changeGrid(e) {
				if (e == '惠企政策') {
					uni.navigateTo({
						url: '/pages/Preferential/index'
					});
				}
			},
			async getComplaintList() {
				const staticDomainURL = config.staticDomainURL
				const param = {

				}
				const res = await getCooperationList(param)
				this.CooperationList = res.result
				this.CooperationList.forEach((item, index) => {
					item.photoUrl = staticDomainURL + item.photoUrl
				})
			},
		},
		mounted() {
			this.getComplaintList()
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		},
	}
</script>

<style lang="scss">
	.hqfw {
		position: relative;
		margin: 0 22rpx;

		.background {
			width: 726rpx;
			height: 546rpx;
		}

		.title {
			position: absolute;
			width: 175rpx;
			height: 50rpx;
			left: 40rpx;
			top: 46rpx;
		}

		.grid-body {
			width: 632rpx;
			position: absolute;
			left: 40rpx;
			top: 134rpx;

			.grid-item-box {
				
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				.text {
					text-align: center;
					margin-top: 10rpx;
					font-weight: 400;
					font-size: 28rpx;
					color: #444;
					line-height: 40rpx;
					font-style: normal;
				}

				.image {
					width: 96rpx;
					height: 96rpx;
				}
			}
		}
	}
</style>