<template>
	<!-- 轮播图 -->
	<uni-swiper-dot class="uni-swiper-dot-box" :info="data" :current="current" field="content">
		<swiper class="swiper-box" autoplay :current="swiperDotIndex" @change="changeSwiper">
			<swiper-item v-for="(item, index) in data" :key="index">
				<view class="swiper-item" @click="clickBannerItem(item)">
					<image class="image" :src="item.photoUrl" mode="aspectFill" :draggable="false" />
				</view>
			</swiper-item>
		</swiper>
	</uni-swiper-dot>
</template>

<script>
	import config from '@/config'
	import {
		getSlideshowList
	} from "@/api/home/<USER>"

	export default {
		data() {
			return {
				current: 0,
				swiperDotIndex: 0,
				data: []
			};
		},
		methods: {
			clickBannerItem(item) {
				console.info(item)
			},
			changeSwiper(e) {
				this.current = e.detail.current
			},
			changeGrid(e) {
				this.$modal.showToast('模块建设中~')
			},
			async getComplaintList() {
				const staticDomainURL = config.staticDomainURL
				const param = {

				}
				const res = await getSlideshowList(param)

				this.data = res.result
				this.data.forEach((item, index) => {
					item.photoUrl = staticDomainURL + item.photoUrl
				})
				console.log(this.data, 'getComplaintList')
			},
		},
		mounted() {
			this.getComplaintList()
		}
	}
</script>

<style lang="scss">
	.swiper {
		height: 350rpx;
	}

	.swiper-box {
		height: 350rpx;
	}

	.swiper-item {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #fff;
		height: 350rpx;
		line-height: 350rpx;
	}

	@media screen and (min-width: 500px) {
		.uni-swiper-dot-box {
			border-radius: 5px;
			width: 400px;
			/* #ifndef APP-NVUE */
			margin: 0 auto;
			/* #endif */
			margin-top: 8px;
		}

		.image {
			width: 100%;
			height: 350rpx;
		}
	}
</style>