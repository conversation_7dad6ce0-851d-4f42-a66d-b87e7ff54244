<template>
  <view
    :style="{
      paddingTop: Props.statusBarHeight + 'px'
    }"
  >
    <navbar :Props="Props" :title="headtitle" @black="black"></navbar>
    <view :style="{ fontSize: fontSizeMedium }" class="main-box">
      <view class="row">
        <view @click="handleToEditInfo">
          <view>
            <image class="icon1" src="/static/images/mine/xg.png"></image>
            <text>个人信息修改</text>
          </view>
          <image class="icon2" src="/static/images/mine/right.png"></image>
        </view>
        <view @click="handleToEditPwd">
          <view>
            <image class="icon1" src="/static/images/mine/mm.png"></image>
            <text>登录密码</text>
          </view>
          <image class="icon2" src="/static/images/mine/right.png"></image>
        </view>
      </view>
      <view class="row">
        <view @click="handleToCare">
          <view>
            <image class="icon1" src="/static/images/mine/gh.png"></image>
            <text>关怀模式设置</text>
          </view>
          <image class="icon2" src="/static/images/mine/right.png"></image>
        </view>
        <view @click="$tab.navigateTo('/pages/privacy/index?type=1')">
          <view>
            <image class="icon1" src="/static/images/mine/ys.png"></image>
            <text>隐私政策</text>
          </view>
          <image class="icon2" src="/static/images/mine/right.png"></image>
        </view>
        <view @click="handleLogout">
          <view>
            <image class="icon1" src="/static/images/mine/tc.png"></image>
            <text>退出登录</text>
          </view>
          <image class="icon2" src="/static/images/mine/right.png"></image>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import navbar from '@/components/Navbar/index.vue'
export default {
  components: {
    navbar
  },
  data() {
    return {
      headtitle: '设置',
      Props: {
        imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: '', //导航高度(动态获取传参)
        bgColor: '', //导航栏背景色,不传参则默认#9CF
        capsuleTop: '', //胶囊顶部距离(动态获取传参)
        textColor: '', //导航标题字体颜色(不传默认#FFF)
        iconColor: '', //icon图标颜色(不传默认#FFF)
        blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
        backText: '' //默认字体(返回)
      }
    }
  },
  onLoad() {
    this.Props.statusBarHeight = getApp().globalData.statusBarHeight
    this.Props.capsuleTop = getApp().globalData.capsuleTop
  },
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall // 从Vuex获取小号字体大小
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium // 从Vuex获取中号字体大小
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge // 从Vuex获取大号字体大小
    }
  },
  methods: {
    handleToCare() {
      uni.showActionSheet({
        itemList: ['大号字体', '默认大小'],
        success: (res) => {
          if (res.tapIndex == 0) {
            this.$store.dispatch('updateLargeFontSize')
          }
          if (res.tapIndex == 1) {
            this.$store.dispatch('updateNormalFontSize')
          }
          uni.showToast({
            title: '设置成功',
            icon: 'none'
          })
        },
        fail: function (res) {}
      })
    },
    handleToEditPwd() {
      uni.navigateTo({
        url: '/pages/mine/editPwd'
      })
    },
    handleToEditInfo() {
      uni.navigateTo({
        url: '/pages/mine/editInfo'
      })
    },
    handleLogout() {
      this.$modal.confirm('确定注销并退出系统吗？').then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$tab.reLaunch('/pages/index')
        })
        global.tabBarCurrent = 0
      })
    }
  }
}
</script>
<style>
page {
  background-color: #f4f8fc;
}
</style>
<style lang="scss" scoped>
.main-box {
  padding: 20rpx;
  .row {
    width: 686rpx;
    margin: 30rpx auto;
    background: #ffffff;
    box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.06);
    border-radius: 32rpx;
    padding: 20rpx 30rpx;
    & > view {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 0rpx;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
      & > view:nth-child(1) {
        display: flex;
        align-items: center;
      }
      .icon1 {
        width: 56rpx;
        height: 58rpx;
        margin-right: 15rpx;
      }
      .icon2 {
        width: 22rpx;
        height: 22rpx;
      }
    }
    & > view:last-child {
      border-bottom: none;
    }
  }
}
</style>
