<template>
  <view
    :style="{
      paddingTop: Props.statusBarHeight + 'px'
    }"
  >
    <navbar :Props="Props" :title="headtitle" @black="black"></navbar>
    <view :style="{ fontSize: fontSizeMedium }" class="main-box">
      <image class="bg" src="/static/images/mine/info_bg.png"></image>
      <view class="info_box" v-if="userInfo">
        <view class="avatar">
          <text>头像</text>
          <button type="balanced" open-type="chooseAvatar" @chooseavatar="onChooseavatar">
            <image mode="aspectFill" :src="userInfo.avatar || '/static/images/mine/tx.jpg'"></image>
          </button>
        </view>
        <view>
          <text>姓名</text>
          <input
            :clearable="false"
            type="nickname"
            class="weui-input"
            :value="userInfo.nickName"
            @blur="bindblur"
            placeholder="请输入昵称"
            @input="bindinput"
          />
        </view>
        <view>
          <text>性别</text>
          <text @click="setSex">{{ userInfo.userSex == 1 ? '男' : '女' }}</text>
        </view>
        <view>
          <text>手机号</text>
          <text>{{ userInfo.mobilePhone }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { updateUser } from '@/api/mine'
import { checkToken } from '@/api/login'
import navbar from '@/components/Navbar/index.vue'
import config from '@/config'
export default {
  components: {
    navbar
  },
  data() {
    return {
      headtitle: '编辑资料',
      Props: {
        imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: '', //导航高度(动态获取传参)
        bgColor: '', //导航栏背景色,不传参则默认#9CF
        capsuleTop: '', //胶囊顶部距离(动态获取传参)
        textColor: '', //导航标题字体颜色(不传默认#FFF)
        iconColor: '', //icon图标颜色(不传默认#FFF)
        blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
        backText: '' //默认字体(返回)
      },
      userInfo: null,
      imgBaseUrl: config.baseUrl
    }
  },
  async onLoad() {
    this.Props.statusBarHeight = getApp().globalData.statusBarHeight
    this.Props.capsuleTop = getApp().globalData.capsuleTop
    const res2 = await checkToken({
      tokne: uni.getStorageSync('ACCESS_TOKEN')
    })
    if (res2.success) {
      this.userInfo = res2.result.userInfo
      this.$store.commit('SET_USERINFO', res2.result.userInfo)
    } else {
      uni.showToast({ title: res2.message, icon: 'none' })
      setTimeout(() => {
        uni.redirectTo({ url: '/pages/login/index' })
      }, 1000)
    }
  },
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall // 从Vuex获取小号字体大小
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium // 从Vuex获取中号字体大小
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge // 从Vuex获取大号字体大小
    }
  },
  methods: {
    onChooseavatar(e) {
      uni.uploadFile({
        header: {
          Authorization: uni.getStorageSync('ACCESS_TOKEN')
        },
        url: config.baseUrl + '/boot' + '/miniapp/api/assist/upload',
        filePath: e.detail.avatarUrl,
        name: 'file',
        formData: {
          biz: `img/${this.userInfo.id}/tx`
        },
        success: (uploadFileRes) => {
          this.userInfo.avatar = this.imgBaseUrl + '/boot/' + JSON.parse(uploadFileRes.data).message
          this.editUserInfo()
        }
      })
    },
    async editUserInfo() {
      const res = await updateUser(this.userInfo)
      if (res.success) {
        uni.showToast({
          title: '修改成功',
          icon: 'none'
        })
        const res2 = await checkToken({
          tokne: uni.getStorageSync('ACCESS_TOKEN')
        })
        this.userInfo = res2.result.userInfo
      }
    },
    bindinput(e) {
      this.userInfo.nickName = e.detail.value
    },
    bindblur(e) {
      this.userInfo.nickName = e.detail.value
      this.editUserInfo()
    },
    setSex() {
      uni.showActionSheet({
        itemList: ['男', '女'],
        success: (res) => {
          if (res.tapIndex == 0) {
            this.userInfo.userSex = 1
          } else {
            this.userInfo.userSex = 0
          }
          this.editUserInfo()
        },
        fail: function (res) {
          console.log(res.errMsg)
        }
      })
    }
  }
}
</script>

<style>
page {
  background: #f4f8fc;
}
</style>
<style lang="scss" scoped>
.main-box {
  width: 686rpx;
  min-height: 393rpx;
  margin: 60rpx auto;
  padding: 30rpx;
  background: #ffff;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.9);
  position: relative;
  border-radius: 32rpx;
  .bg {
    position: absolute;
    width: 100%;
    height: 180rpx;
    left: 0;
    top: 0;
  }
  .info_box {
    position: relative;
    z-index: 2;
    & > view {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx 20rpx;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
    }
  }
  .avatar {
    image {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
    }
    button {
      background: none;
      border: none;
      padding-top: 40rpx;
      margin: 0;
      padding: 0;
      &::after {
        border: none;
      }
    }
  }
  .weui-input {
    width: 260rpx;
    text-align: right;
  }
}
</style>
