<template>
  <view>
    <view class="nav-bar">
      <image class="bg" src="/static/images/mine/mine_bg.png"></image>
      <view class="title" :style="{ fontSize: fontSizeSuperLarge }">我的</view>
    </view>
    <view class="mine-container" v-if="loginType == 1" :style="{ fontSize: fontSizeMedium }">
      <!--顶部个人信息栏-->
      <view class="header-section">
        <image
          class="avatar"
          mode="aspectFill"
          :src="userInfo.avatar || '/static/images/mine/tx.jpg'"
        >
        </image>
        <view v-if="name" class="user-info">
          <view class="u_title">
            <text :style="{ fontSize: fontSizeSuperLarge }">{{ userInfo.nickName }}</text>
            <image
              v-if="userInfo.isAssist == 1"
              @click="$refs.popup.open('center')"
              class="edit"
              src="/static/images/mine/edit.png"
            ></image>
          </view>
          <view class="u_phone"> {{ userInfo.mobilePhone }} </view>
        </view>
        <view class="msg" @click="$tab.navigateTo('/pages/Realtime/index')">
          <image class="" src="/static/images/mine/msg.png"></image>
          <u-badge absolute :offset="[-5, -15]" :type="type" max="99" :value="msgCount"></u-badge>
        </view>
      </view>
      <view class="row row1">
        <view>
          <view class="title">
            <text :style="{ fontSize: fontSizeSuperLarge }">帮办记录</text>
            <image class="bg" src="/static/images/mine/text.png"></image>
          </view>
          <view @click="handleBuilding(0)">
            <text>全部</text>
            <image class="icon" src="/static/images/mine/right.png"></image>
          </view>
        </view>
        <view>
          <view v-for="(item, index) in list" @click="handleBuilding(index + 1)" :key="index">
            <image class="icon" :src="item.url"></image>
            <text>{{ item.text }}</text>
          </view>
        </view>
      </view>
      <!-- 设置 -->
      <view class="row row2" @click="handleToSetting">
        <view>
          <image class="icon1" src="/static/images/mine/sz.png"></image>
          <text>我的设置</text>
        </view>
        <image class="icon2" src="/static/images/mine/right.png"></image>
      </view>
    </view>
    <view class="admin-container" v-if="loginType == 0">
      <view class="avatar">
        <image mode="aspectFill" :src="userInfo.avatar || '/static/images/mine/tx.jpg'"> </image>
      </view>
      <view class="cell">
        <view>
          <image class="icon" src="/static/images/admin/user.png"></image>
          <text>用户名称</text>
        </view>
        <view @click="$tab.navigateTo('/pages/mine/editInfo')">
          <text>{{ userInfo.nickName }}</text>
          <image class="icon2" src="/static/images/admin/edit.png"></image>
        </view>
      </view>
      <view class="cell">
        <view>
          <image class="icon" src="/static/images/admin/phone.png"></image>
          <text>手机号</text>
        </view>
        <view @click="$tab.navigateTo('/pages/mine/editInfo')">
          <text>{{ userInfo.mobilePhone }}</text>
          <image class="icon2" src="/static/images/admin/edit.png"></image>
        </view>
      </view>
      <view class="cell">
        <view>
          <image class="icon" src="/static/images/admin/sex.png"></image>
          <text>性别</text>
        </view>
        <view @click="$tab.navigateTo('/pages/mine/editInfo')">
          <text>{{ userInfo.userSex == 1 ? '男' : '女' }}</text>
          <image class="icon2" src="/static/images/admin/edit.png"></image>
        </view>
      </view>
      <view class="cell">
        <view>
          <image class="icon" src="/static/images/admin/role.png"></image>
          <text>所属角色</text>
        </view>
        <view>
          <text>{{ loginType == 0 ? '帮办管理员' : '帮办员' }}</text>
        </view>
      </view>
      <view class="logout" @click="handleLogout">退出账号</view>
    </view>
    <TabBar :current="3" :tabBarList="tabBerLists" />

    <!-- 选择身份 -->
    <uni-popup ref="popup" border-radius="10px 10px 0 0" :style="{ fontSize: fontSizeMedium }">
      <view class="popup-content">
        <view class="popup-bg"> <image src="@/static/images/login/login-bg.png"></image></view>
        <view class="popup-title" :style="{ fontSize: fontSizeSuperLarge }">
          <image class="false" src="@/static/images/login/title.png"></image>
          嘉有小乔
        </view>
        <view class="popup-body">
          <view @click="handleLoginType(0)">
            <image src="@/static/images/login/bb.png"></image>
            帮办员登录</view
          >
          <view @click="handleLoginType(1)">
            <image src="@/static/images/login/yh.png"></image>
            普通用户登录</view
          >
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import global from '@/utils/global.js'
import TabBar from '@/components/TabBar/index.vue'
import { getNoticelistByUser } from '@/api/home/<USER>'
import { checkToken } from '@/api/login'
export default {
  components: {
    TabBar
  },
  data() {
    return {
      tabBerLists: [],
      isChecked: false,
      name: this.$store.state.user.name,
      version: getApp().globalData.config.appInfo.version,
      type: 'error',
      list: [
        { text: '待提交', url: '/static/images/mine/tj.png' },
        { text: '办理中', url: '/static/images/mine/bl.png' },
        { text: '已完成', url: '/static/images/mine/wc.png' },
        { text: '待评价', url: '/static/images/mine/pj.png' }
      ],
      userInfo: {},
      loginType: '',
      msgCount: 0
    }
  },
  async onShow() {
    this.tabBerLists = uni.getStorageSync('tabBarList')
    const res2 = await checkToken({
      tokne: uni.getStorageSync('ACCESS_TOKEN')
    })
    this.userInfo = res2.result.userInfo
    this.loginType = uni.getStorageSync('loginType')
    const res = await getNoticelistByUser()
    this.msgCount = res.result.sysNoticeTotal
  },
  onLoad() {},
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall // 从Vuex获取小号字体大小
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium // 从Vuex获取中号字体大小
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge // 从Vuex获取大号字体大小
    },
    fontSizeSuperLarge() {
      return this.$store.state.fontSizeSuperLarge // 从Vuex获取大号字体大小
    }
  },
  methods: {
    handleLoginType(type) {
      uni.setStorageSync('loginType', type)
      this.$store.commit('setRoleId', type)
      global.tabBarCurrent = 0
      if (type === 0) {
        uni.reLaunch({ url: '/pages/admin/index' })
      }
      if (type === 1) {
        uni.reLaunch({ url: '/pages/home/<USER>' })
      }
    },
    handleFontChange(event) {
      this.isChecked = event.detail.value
      // 使用三元运算符来简化条件判断
      if (this.isChecked) {
        this.$store.dispatch('updateLargeFontSize')
      } else {
        this.$store.dispatch('updateNormalFontSize')
      }
    },
    handleToInfo() {
      this.$tab.navigateTo('/pages/mine/info/index')
    },
    handleToEditInfo() {
      this.$tab.navigateTo('/pages/mine/info/edit')
    },
    handleToSetting() {
      this.$tab.navigateTo('/pages/mine/setting')
    },
    handleToLogin() {
      this.$tab.reLaunch('/pages/login/index')
    },
    handleToAvatar() {
      this.$tab.navigateTo('/pages/mine/avatar/index')
    },
    handleLogout() {
      this.$modal.confirm('确定注销并退出系统吗？').then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$tab.reLaunch('/pages/index')
        })
        global.tabBarCurrent = 0
      })
    },
    handleHelp() {
      this.$tab.navigateTo('/pages/mine/help/index')
    },
    handleAbout() {
      this.$tab.navigateTo('/pages/mine/about/index')
    },
    handleJiaoLiuQun() {
      this.$modal.showToast('模块建设中~')
    },
    handleBuilding(type) {
      this.$tab.navigateTo('/pages/mine/handlingRecord?type=' + type)
    }
  }
}
</script>

<style>
page {
  background-color: #f4f8fc;
}
</style>
<style lang="scss" scoped>
.right-align {
  position: absolute;
  right: 30rpx;
}
.nav-bar {
  position: relative;
  width: 100%;
  height: 457rpx;
  padding-top: 110rpx;

  .bg {
    position: absolute;
    width: 100%;
    height: 457rpx;
    top: 0;
    left: 0;
  }
  .title {
    position: relative;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;

    color: #000000;
    text-align: center;
    z-index: 2;
  }
}
.mine-container {
  position: relative;
  width: 100%;
  height: 100%;
  top: -220rpx;
  .header-section {
    position: relative;
    width: 686rpx;
    margin: 0 auto;
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;

    color: #444444;
    .avatar {
      width: 136rpx;
      height: 136rpx;
      border-radius: 16rpx;
      margin-right: 30rpx;
    }
    .user-info {
      .u_title {
        font-weight: 600;

        color: #141414;
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;
        .edit {
          width: 48rpx;
          height: 48rpx;
          margin-left: 20rpx;
        }
      }
    }
    .msg {
      position: absolute;
      top: 0%;
      right: 0%;
      image {
        width: 48rpx;
        height: 48rpx;
        margin-left: 20rpx;
      }
    }
  }
  .row {
    width: 686rpx;
    margin: 0 auto;
    background: #ffffff;
    box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.06);
    border-radius: 32rpx;
    margin-bottom: 30rpx;
    padding: 30rpx;
  }
  .row1 {
    min-height: 292rpx;
    & > view:nth-child(1) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;

      color: #666666;
      .icon {
        width: 22rpx;
        height: 22rpx;
      }
      .title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;

        color: #141414;
        position: relative;
        .bg {
          position: absolute;
          top: 20rpx;
          left: 0;
          width: 106rpx;
          height: 30rpx;
        }
      }
    }
    & > view:nth-child(2) {
      margin-top: 30rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;

      color: #444444;
      & > view {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      image {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 6rpx;
      }
    }
  }
  .row2 {
    min-height: 120rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    & > view:nth-child(1) {
      display: flex;
      align-items: center;
    }
    .icon1 {
      width: 56rpx;
      height: 58rpx;
      margin-right: 15rpx;
    }
    .icon2 {
      width: 22rpx;
      height: 22rpx;
    }
  }
}
.admin-container {
  position: relative;
  top: -220rpx;
  width: 686rpx;
  min-height: 300rpx;
  margin: 0 auto;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.06);
  border-radius: 32rpx;
  padding: 0 20rpx;
  .avatar {
    text-align: center;
    image {
      width: 136rpx;
      height: 136rpx;
      border-radius: 16rpx;
      margin-top: -50rpx;
    }
  }
  .logout {
    position: absolute;
    left: 50%;
    bottom: -50%;
    transform: translateX(-50%);
    width: 590rpx;
    height: 80rpx;
    background: #057ffe;
    border-radius: 8rpx;
    text-align: center;
    line-height: 80rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;

    color: #ffffff;
  }
  .cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #444444;
    padding: 20rpx 0rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
    & > view {
      display: flex;
      align-items: center;
    }
    .icon {
      width: 56rpx;
      height: 56rpx;
      margin-right: 10rpx;
    }
    .icon2 {
      width: 22rpx;
      height: 23rpx;
      margin-left: 10rpx;
    }
  }
}

.popup-content {
  // background: #fff;
  position: relative;
  width: 540rpx;
  height: 380rpx;
  padding: 30rpx;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  .popup-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .popup-title {
    color: #333;
    text-align: center;
    font-weight: 600;
    width: 194rpx;
    height: 64rpx;
    line-height: 64rpx;
    position: relative;
    z-index: 1;
    margin: 0 auto 30rpx;
    image {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: -1;
    }
  }
  .popup-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 1;
    image {
      width: 56rpx;
      height: 56rpx;
      margin-right: 20rpx;
    }
    & > view {
      width: 420rpx;
      height: 90rpx;
      margin: 0 auto 30rpx;
      background: rgba(5, 127, 254, 0.1);
      border-radius: 45rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;

      color: #333333;
      line-height: 90rpx;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
