<template>
  <view
    :style="{
      paddingTop: Props.statusBarHeight + 'px'
    }"
  >
    <navbar :Props="Props" :title="headtitle" @black="black"></navbar>
    <view :style="{ fontSize: fontSizeMedium }" class="main-box">
      <view>请输入新的密码</view>
      <view>密码为8-20位，至少包含字母、数字、符号2种组合</view>
      <view class="input-item">
        <input
          v-model="loginForm.password"
          :type="showPassword ? 'text' : 'password'"
          class="input"
          placeholder="请输入密码"
          maxlength="20"
          ref="password"
        />
        <image
          @click="handleEye"
          v-show="showPassword"
          class="eye"
          src="@/static/images/login/pwd.png"
        ></image>
        <image
          @click="handleEye"
          v-show="!showPassword"
          class="eye"
          src="@/static/images/login/pwd2.png"
        ></image>
      </view>
      <view>通过短信验证可以使用新密码</view>
      <view class="getCode" @tap="getCode">{{ tips }}</view>
    </view>

    <u-toast ref="uToast"></u-toast>
    <u-code :seconds="seconds" ref="uCode" @change="codeChange"></u-code>
    <uni-popup ref="popup" background-color="#fff">
      <view class="popup-content">
        <u-code-input v-model="loginForm.captcha" :focus="true" @finish="finish"></u-code-input>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { sendMsg, updatePasswd } from '@/api/login'
import navbar from '@/components/Navbar/index.vue'
import CryptoJS from 'crypto-js'
import global from '@/utils/global.js'
export default {
  components: {
    navbar
  },
  data() {
    return {
      headtitle: '密码修改',
      Props: {
        imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: '', //导航高度(动态获取传参)
        bgColor: '', //导航栏背景色,不传参则默认#9CF
        capsuleTop: '', //胶囊顶部距离(动态获取传参)
        textColor: '', //导航标题字体颜色(不传默认#FFF)
        iconColor: '', //icon图标颜色(不传默认#FFF)
        blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
        backText: '' //默认字体(返回)
      },
      loginForm: {
        username: '',
        password: '',
        captcha: '',
        checkKey: ''
      },
      key: '1234567890adbcde',
      iv: '1234567890hjlkew',
      showPassword: false,
      tips: '',
      seconds: 60
    }
  },
  async onLoad(options) {
    this.Props.statusBarHeight = getApp().globalData.statusBarHeight
    this.Props.capsuleTop = getApp().globalData.capsuleTop
    if (options.phone) {
      this.loginForm.username = options.phone
    } else {
      let userInfo =
        typeof this.$store.state.user.userInfo == 'string'
          ? JSON.parse(this.$store.state.user.userInfo)
          : this.$store.state.user.userInfo
      this.loginForm.username = userInfo.mobilePhone
    }
  },
  computed: {
    fontSizeSmall() {
      return this.$store.state.fontSizeSmall // 从Vuex获取小号字体大小
    },
    fontSizeMedium() {
      return this.$store.state.fontSizeMedium // 从Vuex获取中号字体大小
    },
    fontSizeLarge() {
      return this.$store.state.fontSizeLarge // 从Vuex获取大号字体大小
    }
  },
  methods: {
    handleEye() {
      this.showPassword = !this.showPassword
    },
    codeChange(text) {
      this.tips = text
    },
    async getCode() {
      if (!this.loginForm.password) {
        uni.$u.toast('请输入密码')
        return
      }
      if (this.$refs.uCode.canGetCode) {
        const res = await sendMsg({
          phone: this.encryptLoginData(this.loginForm.username),
          type: 'loginCode'
        })
        if (res.success) {
          uni.$u.toast('验证码已发送')
          this.$refs.uCode.start()
          setTimeout(() => {
            this.$refs.popup.open('bottom')
          }, 500)
        }
      } else {
        uni.$u.toast('倒计时结束后再发送')
      }
    },
    async finish() {
      const res = await updatePasswd({
        username: this.encryptLoginData(this.loginForm.username),
        password: this.encryptLoginData(this.loginForm.password),
        captcha: this.loginForm.captcha,
        checkKey: this.loginForm.checkKey
      })
      if (res.success) {
        uni.showToast({
          title: res.result,
          icon: 'none'
        })
        setTimeout(() => {
          this.$store.dispatch('LogOut').then(() => {
            this.$tab.reLaunch('/pages/index')
          })
          global.tabBarCurrent = 0
        }, 1000)
      } else {
        uni.showToast({ title: res.message, icon: 'none' })
      }
    },
    encryptLoginData(data) {
      if (typeof data !== 'string') {
        console.error('Input must be a string')
        return null
      }
      const key = CryptoJS.enc.Utf8.parse('1234567890adbcde')
      const iv = CryptoJS.enc.Utf8.parse('1234567890hjlkew')
      const blockSize = 16
      const paddingSize = blockSize - (data.length % blockSize)
      const paddedData = data + String.fromCharCode(paddingSize).repeat(paddingSize)
      try {
        const encrypted = CryptoJS.AES.encrypt(paddedData, key, {
          iv: iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.NoPadding
        })
        const result = encrypted.ciphertext.toString(CryptoJS.enc.Base64)
        console.log('Encrypted result:', result)
        return result
      } catch (error) {
        console.error('Encryption error:', error)
        return null
      }
    }
  }
}
</script>

<style>
page {
  background: #fff;
}
</style>
<style lang="scss" scoped>
.main-box {
  padding: 60rpx 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: rgba(102, 102, 102, 0.9);
  & > view:nth-child(1) {
    font-weight: 500;
    font-size: 30rpx;
    color: rgba(20, 20, 20, 0.9);
    margin-bottom: 20rpx;
  }
  .input-item {
    margin: 20px auto;
    background-color: #f5f7fb;
    width: 630rpx;
    height: 100rpx;
    background: #f5f7fb;
    border-radius: 49rpx;
    position: relative;
    .input {
      width: 100%;
      height: 100%;
      font-size: 14px;
      text-align: left;
      padding-left: 15px;
    }
    .eye {
      position: absolute;
      right: 6%;
      top: 50%;
      transform: translateY(-50%);
      width: 36rpx;
      height: 36rpx;
      z-index: 2;
    }
  }
  .getCode {
    width: 630rpx;
    height: 100rpx;
    margin: 30rpx auto;
    line-height: 100rpx;
    text-align: center;
    background: linear-gradient(310deg, #0cb6ff 0%, #057ffe 100%);
    border-radius: 49rpx;

    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32rpx;
    color: #ffffff;
  }
}
.popup-content {
  min-height: 300rpx;
  padding: 30rpx;
  ::v-deep .u-code-input {
    justify-content: space-evenly;
  }
}
</style>
