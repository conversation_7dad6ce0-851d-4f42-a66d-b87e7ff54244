<template>
	<view>
<!-- 		<view class="backboxs">
			<image class="back" @click="back()" src="@/static/images/map/back.png" mode=""></image>
		</view> -->
		<view class="search-box">
			<view class="search-input">
				<input type="text" placeholder="请输入关键字" v-model="keyword" />
				<image src="/static/images/mine/search.png"></image>
			</view>
			<u-tabs :list="tabList" @click="tabChange" :current="type"></u-tabs>
		</view>
		<view class="list-box">
			<div v-for="(list, index) in list" :key="index" class="list" @click="goto(list)">

				<div class="title_box">
					<span class="title">{{ list.itemName }}</span>
					<!-- <div class="time">{{ list.createTime }}</div> -->
				</div>
				<view class="status_box">
					<!-- <div class="time">{{ list.createTime }}</div> -->
					<view class="status" style="background: #fda600" v-show="list.handleStatus == 1">
						{{ list.handleStatus_dictText }}
					</view>
					<view class="status" style="background: #0ec060" v-show="list.handleStatus == 2">
						{{ list.handleStatus_dictText }}
					</view>
					<view class="status" style="background: #11a9ee" v-show="list.handleStatus == 3">
						{{ list.handleStatus_dictText }}
					</view>
					<div class="time">{{ list.createTime }}</div>
				</view>

			</div>
			<!-- 			
			<view v-for="(list, index) in list" :key="index" class="list" @click="goto(list)">
				<view>
					<text class="u-line-1 title">{{ list.itemName }}</text>
					<text>{{ list.businessId }}</text>
				</view>
				<view class="item">
					<view class="status" style="background: #fda600" v-show="list.handleStatus == 1">
						{{ list.handleStatus_dictText }}
					</view>
					<view class="status" style="background: #0ec060" v-show="list.handleStatus == 2">
						{{ list.handleStatus_dictText }}
					</view>
					<view class="status" style="background: #11a9ee" v-show="list.handleStatus == 3">
						{{ list.handleStatus_dictText }}
					</view>
				</view>
				<view class="date">
					{{ list.applyTime }}
				</view>
			</view> -->

			<view>
				<uni-load-more iconType="circle" :status="loadMoreStatus" />
			</view>
		</view>
	</view>
</template>

<script>
	import {
		appointMent
	} from '@/api/mine/index.js'
	import {
		debounce
	} from 'lodash'
	import {
		getRecordDetail
	} from '@/api/assistant/index.js'
	import navbar from '@/components/Navbar/index.vue'
	import waves from '@/components/waves/waves.vue'
	export default {
		components: {
			navbar,
			waves
		},
		data() {
			return {
				headtitle: '帮办记录',
				Props: {
					imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: '', //导航高度(动态获取传参)
					bgColor: '', //导航栏背景色,不传参则默认#9CF
					capsuleTop: '', //胶囊顶部距离(动态获取传参)
					textColor: '', //导航标题字体颜色(不传默认#FFF)
					iconColor: '', //icon图标颜色(不传默认#FFF)
					blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
					backText: '' //默认字体(返回)
				},
				tabList: [{
					name: '全部'
				}, {
					name: '预约中'
				}, {
					name: '预约成功'
				},	 {
					name: '已完成'
				}],
				currentIndex: 0,
				type: 0,
				pageNo: 1,
				pageSize: 10,
				keyword: '',
				list: [],
				moreNum: [],
				loadMoreStatus: 'more',
				count: 5,
				value: 1,
				currentList: {},
				rateForm: {
					businessLevel: '1',
					isAnonymous: 0,
					rateReason: '',
					assistantId: '',
					rateScore: 0,
					recordId: ''
				},
				imageStyles: {
					border: {
						color: 'transparent',
						width: 0
					}
				},
				isAgreement: false,
				handleStatus: ''
			}
		},
		watch: {
			keyword: {
				handler: debounce(function() {
					this.pageNo = 1
					this.list = []
					this.loadMoreStatus = 'more'
					this.getList()
				}, 300),
				immediate: false
			}
		},
		mounted() {
			this.getList()
		},
		onLoad(options) {
			this.type = options.type
		},
		methods: {
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
			goto(index) {
				console.log(index, '988888')
				uni.navigateTo({
					url: '/pages/map/orderDeta?id=' + index.id
				});
			},
			rateChange(e) {
				console.log(e)
				this.rateForm.rateScore = e.value
			},

			onReachBottom() {
				console.log('90----------------')
				console.log(this.loadMoreStatus, '766666')
				if (this.loadMoreStatus !== 'more') return
				this.loadMoreStatus = 'loading'
				this.pageNo++
				this.getList()
			},
			handleMore(length, index) {
				this.$set(this.moreNum, index, this.moreNum[index] === 2 ? length : 2)
			},
			tabChange(e) {
				this.tabList.forEach((item, index) => {
					// if (index !== this.type) {
					// this.$set(item, 'badge', null)
					delete item.badge
					// }
				})
				this.type = e.index
				this.pageNo = 1
				this.list = []
				this.loadMoreStatus = 'more'
				this.getList()
			},

			async getList() {
				this.handleStatus = this.type
				if (this.handleStatus == 0) {
					this.handleStatus = ''
				}
				const query = {
					pageNo: this.pageNo,
					pageSize: this.pageSize,
					itemName: this.keyword,
					handleStatus: this.handleStatus
				}

				const res = await appointMent(query)
				console.log(res, 22222)
				if (res.result.records.length < this.pageSize) {
					this.loadMoreStatus = 'noMore'
				} else {
					this.loadMoreStatus = 'more'
				}
				console.log(this.loadMoreStatus, 'more')
				this.list = this.list.concat(res.result.records)

				// 更新当前 tab 的徽标，仅在成功获取数据后调用
				this.updateBadge(res.result.total)
			},

			updateBadge(total) {
				// 更新当前激活 tab 的徽标
				if (total > 0) {
					this.$set(this.tabList[this.type], 'badge', {
						value: total
					})
				} else {
					this.$set(this.tabList[this.type], 'badge', null)
				}
				console.log('tabList', this.tabList)
			}
		}
	}
</script>

<style>
	page {
		background-color: #f4f8fc;
	}
</style>
<style lang="scss" scoped>
	.search-box {
		padding: 20rpx;
		height: 223rpx;
		background: #ffffff;
		box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.15);
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		margin-bottom: 50rpx;

		.search-input {
			position: relative;
			height: 60rpx;
			background: #ffffff;
			border-radius: 30rpx;
			border: 2rpx solid #dddddd;
			padding-left: 30rpx;
			margin-bottom: 30rpx;

			input {
				height: 60rpx;
			}

			image {
				position: absolute;
				right: 20rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 32rpx;
				height: 32rpx;
			}
		}
	}

	.list {
		background: linear-gradient(180deg, hsla(204, 100%, 95%, 0.5) 0%, #ffffff 50%, #ffffff 100%);
		border-radius: 32rpx;
		border: 2rpx solid rgba(255, 255, 255, 0.68);
		margin: 0 auto 50rpx auto;
		min-height: 190rpx;
		padding: 0rpx 20rpx;
		font-family: PingFangSC, PingFang SC;
		display: flex;
		flex-direction: column;
		position: relative;

		.rate-box {
			position: absolute;
			right: 10rpx;
			bottom: 10rpx;
			display: flex;
			justify-content: flex-end;
			margin: 10rpx 0;
		}

		.title_box {
			margin-bottom: 30rpx;
			display: flex;
			justify-content: space-between;
			border-bottom: 1rpx solid rgba(0, 0, 0, 0.2);
			padding: 20rpx 0;

			.title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 30rpx;
				color: #444444;
			}

			.status {
				min-width: 98rpx;
				height: 44rpx;
				line-height: 44rpx;
				background: #0ec060;
				border-radius: 4rpx 22rpx 4rpx 22rpx;
				font-weight: 500;
				font-size: 24rpx;
				color: #ffffff;
				text-align: center;
			}
		}

		.time {
			min-height: 60rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 24rpx;
			color: #acacac;
			line-height: 33rpx;
		}

		.item {
			// margin-bottom: 20rpx;
			padding: 30rpx 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			&>view:nth-child(1) {
				width: 76%;
			}

			.status {
				width: 98rpx;
				height: 44rpx;
				line-height: 44rpx;
				background: #0ec060;
				border-radius: 4rpx 22rpx 4rpx 22rpx;
				font-weight: 500;
				font-size: 24rpx;
				color: #ffffff;
				text-align: center;
			}
		}

		.more {
			position: relative;
			text-align: center;
			font-weight: 400;
			font-size: 24rpx;
			color: #acacac;

			image {
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 161rpx;
				height: 51rpx;
			}

			.info {
				position: relative;
				z-index: 2;
			}

			.time {
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);
				font-weight: 400;
				font-size: 24rpx;
				color: #acacac;
			}
		}

		.rate-box {
			display: flex;
			justify-content: flex-end;
			margin: 10rpx 0;
		}

		.rate {
			width: 114rpx;
			height: 56rpx;
			border-radius: 8rpx;
			border: 1rpx solid #057ffe;
			text-align: center;
			line-height: 56rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #057ffe;
		}
	}

	.popup-content {
		position: relative;
		width: 600rpx;
		min-height: 823rpx;
		padding: 30rpx;
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		background: #fff;

		.popup-bg {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			width: 100%;
			height: 174rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.popup-title {
			position: relative;
			z-index: 2;
			margin-bottom: 50rpx;

			image {
				width: 100rpx;
				height: 100rpx;
				margin-right: 20rpx;
			}

			display: flex;
			align-items: center;
			font-family: PingFangSC,
			PingFang SC;
			font-weight: 400;
			font-size: 28rpx;
			color: #666666;

			.name {
				font-size: 32rpx;
				color: #181818;
				margin-bottom: 10rpx;
			}
		}

		.popup-body {
			position: relative;
			z-index: 2;

			.rate-box {
				display: flex;
				align-items: center;

				.title {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #444444;
					margin-right: 10rpx;
				}
			}

			.textarea {
				width: 538rpx;
				height: 253rpx;
				margin: 30rpx auto;
				padding: 20rpx;
				background: #f5f7fb;
				border-radius: 20rpx;
			}

			.upload {
				margin: 30rpx 0;

				.icon {
					width: 74rpx;
					height: 63rpx;
					margin-bottom: 8rpx;
				}

				.upload-btn {
					width: 100%;
					height: 100%;
					background: #f5f7fb;
					border-radius: 20rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
				}
			}

			.submit-box {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.radio {
					display: flex;
					align-items: center;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #666666;

					image {
						width: 26rpx;
						height: 26rpx;
						margin-right: 10rpx;
					}

					text {
						color: #057ffe;
					}
				}

				.submit {
					width: 282rpx;
					height: 80rpx;
					line-height: 80rpx;
					background: #057ffe;
					border-radius: 8rpx;
					text-align: center;

					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #ffffff;
				}
			}
		}
	}

	.date {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 24rpx;
		color: #ACACAC;
		line-height: 33rpx;
		text-align: right;
		font-style: normal;
		padding: 40rpx;
	}

	.backboxs {

		background-color: #fff;
		padding: 10rpx 30rpx;
	}

	.back {
		width: 40rpx;
		height: 40rpx;
	}

	::v-deep .u-tabs__wrapper__nav__item {
		padding: 0 40rpx;
	}

	.list-box {
		padding: 0 20rpx;
	}

	.status_box {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		.time {
			min-height: 44rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 24rpx;
			color: #acacac;
			line-height: 44rpx;
		}
		.status {
			width: 98rpx;
			height: 44rpx;
			line-height: 44rpx;
			background: #0ec060;
			border-radius: 4rpx 22rpx 4rpx 22rpx;
			font-weight: 500;
			font-size: 24rpx;
			color: #ffffff;
			text-align: center;
		}

	}
</style>