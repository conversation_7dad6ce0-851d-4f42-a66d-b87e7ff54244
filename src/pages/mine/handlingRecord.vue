<template>
  <div class="container">
    <div class="search-box">
      <view class="search-input">
        <input type="text" placeholder="请输入关键字" v-model="keyword" />
        <image src="/static/images/mine/search.png"></image>
      </view>
      <u-tabs
        v-if="isShowTab"
        :activeStyle="{
          color: 'rgba(20,20,20,0.9)',
          fontWeight: '500',
          transform: 'scale(1.05)'
        }"
        :list="tabList"
        @click="tabChange"
        :current="type"
        :lineColor="`url(${lineBg}) 100% 100%`"
      ></u-tabs>
    </div>
    <div class="list-box">
      <div v-for="(list, index) in list" :key="index" class="list" @click="goDetail(list)">
        <div class="title_box">
          <span class="title">{{ list.itemName || list.helpContent || '-' }}</span>
        </div>
        <div class="time">
          <div>
            <span class="status" style="background: #999999" v-show="list.helpStatus === 'dks'">
              未开始
            </span>
            <!-- <span class="status" style="background: #fda600" v-show="list.helpStatus === 'dtj'"
              >待提交</span
            > -->
            <span class="status" style="background: #f24f4b" v-show="list.helpStatus === 'th'"
              >退回</span
            >
            <span
              class="status"
              style="background: #11a9ee"
              v-show="list.helpStatus === 'dcl' || list.helpStatus === 'blz' || list.helpStatus === 'dtj'"
              >办理中</span
            >
            <span class="status" style="background: #0ec060" v-show="list.helpStatus === 'tg'"
              >已完成</span
            >
          </div>
          <div
            v-if="list.helpStatus == 'tg' && !list.isRate"
            class="rate"
            @click.stop="goRate(list)"
          >
            评价
          </div>
          <div>{{ list.createTime }}</div>
        </div>
      </div>

      <div>
        <uni-load-more iconType="circle" :status="loadMoreStatus" />
      </div>
    </div>

    <!-- 悬浮按钮 -->
    <u-transition
      :show="showAnimation"
      mode="fade-right"
      :duration="500"
      timingFunction="ease-in-out"
    >
      <view class="suspension">
        <image
          @click="$tab.navigateTo('/pages/interaction/im/index')"
          src="/static/images/assistant/dh.png"
        ></image>
      </view>
    </u-transition>

    <uni-popup ref="popup" border-radius="10px 10px 0 0">
      <view class="popup-content">
        <view class="popup-bg"> <image src="@/static/images/mine/bg.png"></image></view>
        <view class="popup-title">
          <view>
            <view class="name">{{ currentList.assistantName }}</view>
            <view>{{ currentList.assistantTel }}</view>
          </view>
        </view>
        <view class="popup-body">
          <view class="rate-box">
            <text class="title">评分</text>
            <view>
              <uni-rate
                :value="rateForm.rateScore"
                @change="rateChange"
                color="#bbb"
                active-color="red"
              />
            </view>
          </view>
          <view class="textarea">
            <textarea placeholder="请输入评价内容" v-model="rateForm.rateReason"></textarea>
          </view>
          <view class="submit-box">
            <view class="radio">
              <image
                @click="rateForm.isAnonymous = 1"
                v-if="!rateForm.isAnonymous"
                src="@/static/images/login/radio.png"
              ></image>
              <image
                @click="rateForm.isAnonymous = 0"
                v-if="rateForm.isAnonymous"
                src="@/static/images/login/radio-active.png"
              ></image>
              <view>匿名评论</view>
            </view>
            <view class="submit" @click="submit"> 提交 </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </div>
</template>

<script>
import { rateRecord, getAssistantById } from '@/api/mine/index.js'
import { debounce } from 'lodash'
import { getRecordDetail, listRecordNew } from '@/api/assistant/index.js'
import navbar from '@/components/Navbar/index.vue'
import waves from '@/components/waves/waves.vue'
import config from '@/config'
export default {
  components: {
    navbar,
    waves
  },
  data() {
    return {
      tabList: [{ name: '全部' }, { name: '办理中' }, { name: '已完成' }],
      currentIndex: 0,
      type: 0,
      pageNo: 1,
      pageSize: 10,
      keyword: '',
      list: [],
      moreNum: [],
      loadMoreStatus: 'more',
      count: 5,
      value: 1,
      currentList: {},
      rateForm: {
        businessLevel: '1',
        isAnonymous: 0,
        rateReason: '',
        assistantId: '',
        rateScore: 0,
        recordId: ''
      },
      imageStyles: {
        border: {
          color: 'transparent',
          width: 0
        }
      },
      isAgreement: false,
      loginForm: {
        username: '***********',
        password: 'Hwj@***********',
        captcha: '',
        checkKey: ''
      },
      showAnimation: false,
      photoUrl: '',
      imgBaseUrl: config.baseUrl + '/boot/',
      isShowTab: false,
      lineBg: require('@/static/images/line.png')
    }
  },
  watch: {
    keyword: {
      handler: debounce(function () {
        this.pageNo = 1
        this.list = []
        this.loadMoreStatus = 'more'
        this.getList()
      }, 300),
      immediate: false
    }
  },
  onLoad(options) {
    this.type = options.type

    this.getList()
    this.showAnimation = true

    setTimeout(() => {
      this.isShowTab = true
    }, 50)
  },
  async onShow() {},
  methods: {
    rateChange(e) {
      console.log(e)
      this.rateForm.rateScore = e.value
    },
    async goRate(list) {
      this.businessLevel = list.businessLevel
      this.rateForm.assistantId = list.assistantId
      this.rateForm.recordId = list.recordId
      this.$refs.popup.open('center')
    },
    async goDetail(item) {
      try {
        if (item.helpStatus === 'dtj') {
          item['isDetail'] = 1
        } else {
          item['isDetail'] = 1
        }
        const encodedRecord = encodeURIComponent(JSON.stringify(item))
        uni.navigateTo({
          url: `/pages/assistant/team-info?record=${encodedRecord}&pageType=1&isUser=1`
        })
        console.log(item)
      } catch (error) {
        console.error('获取详情失败', error)
        uni.showToast({
          icon: 'none',
          title: '获取详情失败'
        })
      }
    },
    onReachBottom() {
      if (this.loadMoreStatus !== 'more') return
      this.loadMoreStatus = 'loading'
      this.pageNo++
      this.getList()
    },
    handleMore(length, index) {
      this.$set(this.moreNum, index, this.moreNum[index] === length ? 2 : length)
    },
    tabChange(e) {
      this.tabList.forEach((item, index) => {
        delete item.badge
      })
      this.type = e.index
      this.pageNo = 1
      this.list = []
      this.loadMoreStatus = 'more'
      this.getList()
    },
    async getList() {
      const query = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        keyword: this.keyword,
        status: this.getStatusByType(this.type)
      }
      try {
        const res = await listRecordNew(query)
        if (res.result.records.length < this.pageSize) {
          this.loadMoreStatus = 'noMore'
        } else {
          this.loadMoreStatus = 'more'
        }
        this.list = this.list.concat(res.result.records)
        // 初始化 moreNum 数组，默认显示 2 条
        this.list.forEach((item) => {
          this.moreNum.push(2)
        })
        // 更新当前 tab 的徽标，仅在成功获取数据后调用
        this.updateBadge(res.result.total)
      } catch (error) {
        this.loadMoreStatus = 'error'
      }
    },
    getStatusByType(type) {
      switch (type) {
        case 0:
          return 'all'
        case 1:
          return 'blz,dlc,dtj'
        case 2:
          return 'ywc'
        default:
          return ''
      }
    },
    updateBadge(total) {
      // 更新当前激活 tab 的徽标
      if (total > 0) {
        this.$set(this.tabList[this.type], 'badge', { value: total })
      } else {
        this.$set(this.tabList[this.type], 'badge', null)
      }
      console.log('tabList', this.tabList)
    },
    async submit() {
      //评分和内容为空判断
      if (this.rateForm.rateScore == '') {
        uni.showToast({
          icon: 'none',
          title: '请填写评分'
        })
        return
      }
      if (this.rateForm.content == '') {
        uni.showToast({
          icon: 'none',
          title: '请填写内容'
        })
        return
      }
      const res = await rateRecord(this.rateForm)
      if (res.success) {
        uni.showToast({
          icon: 'none',
          title: '评价成功'
        })
        setTimeout(() => {
          this.pageNo = 1
          this.list = []
          this.loadMoreStatus = 'more'
          this.getList()
          this.$refs.popup.close()
        }, 500)
      } else {
        uni.showToast({
          icon: 'none',
          title: '评价失败'
        })
      }
    }
  }
}
</script>

<style>
page {
  background-color: #f4f8fc;
  padding: 20rpx;
}
</style>
<style lang="scss" scoped>
.container {
  position: relative;
}

.search-box {
  padding: 20rpx;
  // height: 223rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.15);
  border-radius: 0rpx 0rpx 20rpx 20rpx;
  margin-bottom: 50rpx;
  .search-input {
    position: relative;
    height: 60rpx;
    background: #ffffff;
    border-radius: 30rpx;
    border: 2rpx solid #dddddd;
    padding-left: 30rpx;
    margin-bottom: 30rpx;
    input {
      height: 60rpx;
    }
    image {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 32rpx;
      height: 32rpx;
    }
  }
}
.list {
  background: linear-gradient(180deg, hsla(204, 100%, 95%, 0.5) 0%, #ffffff 50%, #ffffff 100%);
  border-radius: 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.68);
  margin: 0 auto 50rpx auto;
  min-height: 190rpx;
  padding: 0rpx 20rpx;
  font-family: PingFangSC, PingFang SC;
  display: flex;
  flex-direction: column;
  position: relative;
  .rate-box {
    position: absolute;
    right: 63%;
    bottom: 5%;
    display: flex;
    justify-content: flex-end;
    margin: 10rpx 0;
  }
  .title_box {
    margin-bottom: 20rpx;
    display: flex;
    justify-content: space-between;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.2);
    padding: 20rpx 0;
    .title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #444444;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .time {
    min-height: 60rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 26rpx;
    color: #acacac;
    line-height: 33rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 0;
    .status {
      display: inline-block;
      width: 98rpx;
      height: 44rpx;
      line-height: 44rpx;
      background: #0ec060;
      border-radius: 4rpx 22rpx 4rpx 22rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #ffffff;
      text-align: center;
    }
  }
  .item {
    // margin-bottom: 20rpx;
    padding: 30rpx 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    & > view:nth-child(1) {
      width: 76%;
    }
    .status {
      width: 98rpx;
      height: 44rpx;
      line-height: 44rpx;
      background: #0ec060;
      border-radius: 4rpx 22rpx 4rpx 22rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #ffffff;
      text-align: center;
    }
  }
  .more {
    position: relative;
    text-align: center;
    font-weight: 400;
    font-size: 24rpx;
    color: #acacac;
    image {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 161rpx;
      height: 51rpx;
    }
    .info {
      position: relative;
      z-index: 2;
    }
    .time {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      font-weight: 400;
      font-size: 24rpx;
      color: #acacac;
    }
  }
  .rate-box {
    display: flex;
    justify-content: flex-end;
    margin: 10rpx 0;
  }
  .rate {
    width: 114rpx;
    height: 56rpx;
    border-radius: 8rpx;
    border: 1rpx solid #057ffe;
    text-align: center;
    line-height: 56rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #057ffe;
  }
}
.popup-content {
  position: relative;
  width: 600rpx;
  // min-height: 823rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  background: #fff;
  .popup-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 174rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .popup-title {
    position: relative;
    z-index: 2;
    margin-bottom: 50rpx;
    .avatar {
      width: 100rpx;
      height: 100rpx;
      margin-right: 20rpx;
    }
    display: flex;
    align-items: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    .name {
      font-size: 32rpx;
      color: #181818;
      margin-bottom: 10rpx;
    }
  }
  .popup-body {
    position: relative;
    z-index: 2;
    .rate-box {
      display: flex;
      align-items: center;
      .title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28rpx;
        color: #444444;
        margin-right: 10rpx;
      }
    }
    .textarea {
      width: 538rpx;
      height: 253rpx;
      margin: 30rpx auto;
      padding: 20rpx;
      background: #f5f7fb;
      border-radius: 20rpx;
    }
    .upload {
      margin: 30rpx 0;
      .icon {
        width: 74rpx;
        height: 63rpx;
        margin-bottom: 8rpx;
      }
      .upload-btn {
        width: 100%;
        height: 100%;
        background: #f5f7fb;
        border-radius: 20rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
      }
    }
    .submit-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .radio {
        display: flex;
        align-items: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
        image {
          width: 26rpx;
          height: 26rpx;
          margin-right: 10rpx;
        }
        text {
          color: #057ffe;
        }
      }
      .submit {
        width: 282rpx;
        height: 80rpx;
        line-height: 80rpx;
        background: #057ffe;
        border-radius: 8rpx;
        text-align: center;

        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32rpx;
        color: #ffffff;
      }
    }
  }
}

.suspension {
  position: fixed;
  right: 0%;
  bottom: 20%;
  display: flex;
  flex-direction: column;
  image {
    width: 108rpx;
    height: 108rpx;
    margin-bottom: 10rpx;
  }
}

::v-deep {
  .u-tabs__wrapper__nav__item__text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 30rpx;
    color: rgba(86, 93, 110, 0.9);
    font-style: normal;
  }
}
</style>
