<template>
  <div class="mine-container">
    <!--顶部个人信息栏-->
    <div class="header-section">
      <div class="user">
        <image
          mode="aspectFill"
          class="avatar"
          :src="userInfo.avatar || '/static/images/mine/tx.png'"
        >
        </image>
        <div v-if="userInfo && userInfo.nickName" class="user-info">
          <div class="u_title">
            <span>{{ userInfo.nickName }}</span>
          </div>
          <div class="user_score">
            <span>综合评分</span>
            <div class="start_box">
              <uni-rate
                :value="rateScoreTotal ? Number(rateScoreTotal) : 5"
                color="#bbb"
                active-color="red"
              />
            </div>
            <span>{{ 5 }}</span>
          </div>
          <div class="u_phone">{{ formatPhone(userInfo.mobilePhone) }}</div>
        </div>
      </div>
    </div>

    <div class="content">
      <!-- 帮办管理员 -->
      <div v-if="userInfo.isAssistManger == 1">
        <!-- 帮办进度 -->
        <div class="row2">
          <div class="title">
            <div>帮代办进度</div>
            <div class="look-detail" @click="handleBuilding(0)">查看详情</div>
          </div>
          <div class="table">
            <div class="table_title cell">
              <span>小乔助手</span>
              <span>办理中</span>
              <span>滞留总时长(小时)</span>
            </div>
            <div class="table_body">
              <scroll-view
                scroll-y="true"
                class="scroll-Y"
                :lower-threshold="20"
                @scrolltolower="scrolltolower"
              >
                <div
                  @click="goDetail(item)"
                  class="cell"
                  v-for="(item, index) in bbyjdList"
                  :key="index"
                >
                  <span> {{ item.realName }}</span>
                  <span>
                    <u-count-to
                      fontSize="16"
                      :startVal="0"
                      :endVal="item.processingNum"
                    ></u-count-to>
                  </span>
                  <span>
                    <u-count-to fontSize="16" :startVal="0" :endVal="item.stayTimeSum"></u-count-to>
                  </span>
                </div>
              </scroll-view>
            </div>
          </div>
        </div>
        <!-- 预约进度 -->
        <div class="row2">
          <div class="title">
            <div>预约进度</div>
            <div class="look-detail" @click="handleBuildingOrder()">查看详情</div>
          </div>
          <div class="table">
            <div class="table_title cell">
              <span>办理事项</span>
              <span>预约状态</span>
              <span>姓名</span>
              <span>手机号</span>
            </div>
            <div class="table_body">
              <div class="table_body">
                <scroll-view
                  scroll-y="true"
                  class="scroll-Y"
                  :lower-threshold="20"
                  @scrolltolower="scrolltolowerOrder"
                >
                  <div class="cell" v-for="(item, index) in orderList" :key="index">
                    <span> {{ item.itemName }}</span>
                    <span> {{ item.transactStatus_dictText }}</span>
                    <span> {{ item.realName }}</span>
                    <span> {{ formatPhone(item.appointmentTel) }}</span>
                  </div>
                </scroll-view>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 帮办员 -->
      <div v-else>
        <!-- 帮办进度 -->
        <div class="row2">
          <div class="title">
            <div>帮代办进度</div>
            <div class="look-detail" @click="handleBuilding(0)">查看详情</div>
          </div>
          <div class="table">
            <div class="table_title cell">
              <span>办理事项</span>
              <span>帮代办状态</span>
              <span>姓名</span>
              <span>手机号</span>
            </div>
            <div class="table_body">
              <div
                @click="goDetail(item)"
                class="cell"
                v-for="(item, index) in bbyjdList"
                :key="index"
              >
                <span> {{ item.itemName }}</span>
                <span>
                  <span style="color: #e29708" v-show="item.helpStatus == 'dcl'">待处理</span>
                  <span style="color: #07b858" v-show="item.helpStatus == 'tg'">完成</span>
                  <span v-show="item.helpStatus == 'clz'">处理中</span>
                  <span v-show="item.helpStatus == 'th'">退回</span>
                  <span v-show="item.helpStatus == 'dtj'">待提交</span>
                  <span v-show="item.helpStatus == 'dks'">待开始</span>
                </span>
                <span> {{ item.contactName }} </span>
                <!-- 脱敏 -->
                <span> {{ formatPhone(item.contactPhone) }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 预约进度 -->
        <div class="row2">
          <div class="title">
            <div>预约进度</div>
            <div class="look-detail" @click="handleBuildingOrder()">查看详情</div>
          </div>
          <div class="table">
            <div class="table_title cell">
              <span>办理事项</span>
              <span>预约状态</span>
              <span>姓名</span>
              <span>手机号</span>
            </div>
            <div class="table_body">
              <div class="cell" v-for="(item, index) in orderList" :key="index">
                <span> {{ item.itemName }}</span>
                <span> {{ item.transactStatus_dictText }}</span>
                <span> {{ item.realName }}</span>
                <span> {{ formatPhone(item.appointmentTel) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 悬浮按钮 -->
    <u-transition
      :show="showAnimation"
      mode="fade-right"
      :duration="500"
      timingFunction="ease-in-out"
    >
      <view class="suspension">
        <!-- <image
          style="width: 100rpx; height: 160rpx"
          @click="$tab.navigateTo('/pages/ai/index')"
          src="/static/images/jyAi.png"
        ></image> -->
        <image
          @click="$tab.navigateTo('/pages/admin/adminCallCenter?type=0')"
          src="/static/images/admin/hjNew.png"
        ></image>
        <image
          @click="$tab.navigateTo('/pages/interaction/im/index')"
          src="/static/images/admin/dh.png"
        >
        </image>
      </view>
    </u-transition>
    <!-- 添加悬浮按钮 -->
    <uni-fab
      :pattern="fabPattern"
      :content="fabContent"
      horizontal="right"
      vertical="bottom"
      direction="vertical"
      @trigger="handleFabClick"
    />
  </div>
</template>

<script>
import {
  progressAssist,
  progressDetail,
  getUserInfo,
  getAppointment,
  loginByAppCode
} from '@/api/home/<USER>'
import { newSetToken, removeToken } from '@/utils/auth'

// 添加 Base64 工具函数
function base64Encode(str) {
  // 在浏览器环境中使用 btoa
  if (typeof btoa !== 'undefined') {
    return btoa(str)
  }
  // 在 Node.js 环境中使用 Buffer
  return Buffer.from(str).toString('base64')
}

export default {
  components: {},
  data() {
    return {
      tabBerLists: [],
      isChecked: false,
      name: this.$store.state.user.name,
      version: getApp().globalData.config.appInfo.version,
      type: 'error',
      value: 99999,
      list: [
        {
          text: '待分配',
          url: '/static/images/admin/dfp.png'
        },
        {
          text: '办理中',
          url: '/static/images/admin/blz.png'
        },
        {
          text: '已完成',
          url: '/static/images/admin/ywc.png'
        },
        {
          text: '未提交',
          url: '/static/images/admin/th.png'
        }
      ],
      pageNo: 1,
      pageSize: 10,
      total: 0,
      current: 0,
      bbyjdList: [],
      bbyjdList2: [],
      msgCount: 0,
      userInfo: {
        nickName: '',
        avatar: '',
        mobilePhone: '',
        isAssistManger: 0
      },
      progressList: [],
      showAnimation: false,
      rateScoreTotal: 0,
      orderPageSize: 10,
      orderPageNo: 1,
      orderList: [],
      username: '',
      // 悬浮按钮配置
      fabPattern: {
        // color: '#7A7E83',
        // backgroundColor: '#fff',
        // selectedColor: '#007AFF',
        // buttonColor: '#007AFF',
        // iconColor: '#fff'
      },
      fabContent: [
        {
          iconPath: '/static/images/tabbar/home_.png',
          selectedIconPath: '/static/images/tabbar/home_.png',
          text: '返回首页',
          active: false
        }
      ]
    }
  },
  async onShow() {},
  async onLoad(options) {
    if (options.username) {
      this.username = options.username
      try {
        const res = await loginByAppCode({ userId: options.username })
        if (res.success) {
          newSetToken(res.result.token)
          const avatar = res.result.userInfo.avatar
          const nickname = res.result.userInfo.nickName

          this.$store.commit('SET_NAME', nickname)
          this.$store.commit('SET_AVATAR', avatar)
          this.$store.commit('SET_USERINFO', res.result.userInfo)
          this.$store.commit('setRoleId', 1)
          uni.setStorageSync('loginType', 1)

          // 直接设置 userInfo，避免 JSON 解析
          this.userInfo = res.result.userInfo

          // 使用 nextTick 确保DOM更新
          this.$nextTick(() => {
            this.showAnimation = true
          })

          this.bbyjdList = []
          await this.getBdblist()
          await this.getOrderList()
        }
      } catch (error) {
        console.error('登录失败:', error)
      }
    } else {
      this.userInfo =
        typeof this.$store.state.user.userInfo == 'string'
          ? JSON.parse(this.$store.state.user.userInfo)
          : this.$store.state.user.userInfo
      this.showAnimation = true
      console.log(this.userInfo, '900')
      // 获取评分
      const res = await getUserInfo()
      this.rateScoreTotal = res?.result?.rateScoreTotal
      this.bbyjdList = []
      this.getBdblist()
      this.getOrderList()
      const res2 = await progressAssist()
      console.log(res2)
    }
    if (window.android) {
      window.android.tabGone()
    } else {
      console.warn('android is not defined')
    }
  },
  onHide() {
    uni.pageScrollTo({
      scrollTop: 0,
      duration: 0
    })
  },
  computed: {
    formatPhone() {
      return (phone) => {
        return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      }
    }
  },
  methods: {
    // callTabGone() {
    //   if (typeof window.Android !== 'undefined' && typeof window.Android.tabGone === 'function') {
    //     window.Android.tabGone()
    //   } else {
    //     console.warn('Android 方法 tabGone 不存在')
    //   }
    // },
    // 获取帮办列表
    async getBdblist() {
      const res2 = await progressDetail({
        pageNo: this.pageNo,
        pageSize: this.pageSize
      })
      if (this.userInfo.isAssistManger == 0) {
        this.bbyjdList = this.bbyjdList.concat(res2.result.bby.records)
      }
      if (this.userInfo.isAssistManger == 1) {
        this.bbyjdList = this.bbyjdList.concat(res2.result.bbAdmin.records)
      }
    },
    // 获取预约列表
    async getOrderList() {
      const res2 = await getAppointment({
        pageNo: this.orderPageNo,
        pageSize: this.orderPageSize
      })
      console.log(res2, '22222')
      this.orderList = this.orderList.concat(res2.result.records)
      console.log(this.orderList, '222211111')
    },
    scrolltolowerOrder() {
      console.log('下拉刷新')
      this.orderPageNo++
      this.getOrderList()
    },
    scrolltolower() {
      console.log('下拉刷新')
      this.pageNo++
      this.getBdblist()
    },
    goDetail(item) {
      uni.navigateTo({
        url: `/pages/admin/detail?record=${JSON.stringify(item)}`
      })
    },
    handleBuilding(type) {
      this.$tab.navigateTo(
        '/pages/admin/adminHandlingRecord?type=' + type + '&username=' + this.username
      )
    },
    handleBuildingOrder() {
      this.$tab.navigateTo('/pages/admin/adminOrderRecord')
    },
    // 生成 authcode
    generateAuthcode() {
      const loginName = this.userInfo.username
      const privateKey = 'woaijiayu'
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const dateStr = `${year}${month}${day}`
      const encodedLogin = base64Encode(loginName)
      const encodedDate = base64Encode(dateStr)
      const combinedStr = encodedLogin + privateKey + encodedDate
      return base64Encode(combinedStr)
    },
    // 处理悬浮按钮点击
    handleFabClick() {
      const authcode = this.generateAuthcode()
      const url = `http://*************:90/mobile/?authcode=${authcode}`
      window.location.href = url
    }
  }
}
</script>

<style>
page {
  background: #f7fbff;
}
</style>
<style lang="scss" scoped>
.mine-container {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0rpx 0;
  overflow: hidden;

  .header-section {
    position: relative;
    width: 100%;
    height: 623rpx;
    padding: 20rpx;
    background: url('/static/images/admin/new1.png') no-repeat bottom;
    background-size: 100% 100%;

    .user {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 30rpx;
      color: #444444;
    }

    .avatar {
      width: 136rpx;
      height: 136rpx;
      border-radius: 50%;
      margin-right: 30rpx;
    }

    .user-info {
      height: 180rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      font-family: SourceHanSerifCN, SourceHanSerifCN;
      font-weight: 400;
      font-size: 30rpx;
      color: #505c73;

      .u_title {
        font-weight: 600;
        font-size: 36rpx;
        color: #141414;
        display: flex;
        align-items: center;
      }

      .user_score {
        display: flex;
        align-items: center;

        .start_box {
          display: flex;
          align-items: center;
          margin: -10rpx 10rpx 0 10rpx;
        }

        .start {
          width: 33rpx;
          height: 32rpx;
          margin: 0 6rpx;
        }
      }
    }
  }

  .content {
    margin-top: -56%;
    width: 100%;
  }

  .row2 {
    min-height: 800rpx;
    margin: 10rpx 20rpx 30rpx 20rpx;
    position: relative;
    background: url('/static/images/admin/new2.png') no-repeat center center;
    background-size: 100% 100%;
    padding: 30rpx 30rpx 0rpx 30rpx;

    .table {
      margin-top: 20rpx;
      .cell {
        width: 100%;
        height: 100rpx;
        display: flex;
        align-items: center;
        border-bottom: 2rpx solid #e7f0ff;

        & > span {
          flex: 1;
          display: block;
          text-align: center;
          font-family: SourceHanSerifCN, SourceHanSerifCN;
          font-weight: 400;
          font-size: 26rpx;
          color: #505c73;
          text-wrap: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        & > span:nth-child(1) {
          flex: 1;
        }
      }
      .table_title {
        background: #e7eefd;
        border-radius: 29rpx;

        span {
          font-family: SourceHanSerifCN, SourceHanSerifCN;
          font-weight: 500;
          font-size: 28rpx;
          color: #4f94d4;
        }
      }
    }
  }

  .title {
    display: flex;
    justify-content: space-between;
    font-family: SourceHanSerifCN, SourceHanSerifCN;
    font-weight: 500;
    font-size: 34rpx;
    color: #2d3645;
    & > div:nth-child(1) {
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: inline-block;
        width: 47rpx;
        height: 31rpx;
        background: url('/static/images/admin/new3.png') no-repeat center center;
        background-size: 100% 100%;
        margin-right: 10rpx;
      }
    }
    .look-detail {
      font-family: SourceHanSerifCN, SourceHanSerifCN;
      font-weight: 400;
      font-size: 24rpx;
      color: #a0b1cb;
      text-stroke: 0px #8c9ebd;
      -webkit-text-stroke: 0px #8c9ebd;
      display: flex;
      align-items: center;
      &::after {
        content: '';
        display: inline-block;
        width: 17rpx;
        height: 23rpx;
        background: url('/static/images/admin/new4.png') no-repeat center center;
        background-size: 100% 100%;
        margin-left: 10rpx;
      }
    }
  }
}

.popup-content {
  position: relative;
  width: 540rpx;
  height: 380rpx;
  padding: 30rpx;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;

  .popup-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .popup-title {
    font-size: 36rpx;
    color: #333;
    text-align: center;
    font-weight: 600;
    width: 194rpx;
    height: 64rpx;
    line-height: 64rpx;
    position: relative;
    z-index: 1;
    margin: 0 auto 30rpx;

    image {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: -1;
    }
  }

  .popup-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 1;

    image {
      width: 56rpx;
      height: 56rpx;
      margin-right: 20rpx;
    }

    & > div {
      width: 420rpx;
      height: 90rpx;
      margin: 0 auto 30rpx;
      background: rgba(5, 127, 254, 0.1);
      border-radius: 45rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 30rpx;
      color: #333333;
      line-height: 90rpx;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.suspension {
  position: fixed;
  right: 0%;
  bottom: 20%;
  display: flex;
  flex-direction: column;
  z-index: 3;

  image {
    width: 108rpx;
    height: 108rpx;
    margin-bottom: 10rpx;
  }
}

.scroll-Y {
  height: 650rpx;
}
</style>
