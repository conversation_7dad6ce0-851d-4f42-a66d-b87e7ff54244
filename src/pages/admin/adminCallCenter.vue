<template>
  <div>
    <div class="search-box">
      <div class="back_box" @click="goBack">
        <img src="/static/images/admin/xq_back.png" alt="" />
        返回
      </div>
      <div class="search-input">
        <input type="text" placeholder="请输入关键字" v-model="keyword" />
        <image src="/static/images/mine/search.png"></image>
      </div>
      <!-- <div class="bg" @click="openPopup2">
        <img src="/static/images/admin/sj.png" alt="" />
      </div> -->
      <!-- <div class="sj-box" @click="openPopup2">
        <img src="/static/images/admin/sj_bg.png" alt="" />
      </div> -->
      <u-tabs
        v-if="isShowTab"
        :activeStyle="{
          color: 'rgba(20,20,20,0.9)',
          fontWeight: '500',
          transform: 'scale(1.05)'
        }"
        :list="tabList"
        @click="click"
        :current="type"
        :lineColor="`url(${lineBg}) 100% 100%`"
      ></u-tabs>
    </div>
    <div class="list-box">
      <div v-for="(list, index) in list" :key="index" class="list">
        <div class="title">
          {{ list.itemName }}
        </div>
        <div class="record-id">
          {{ list.recordId }}
        </div>
        <div class="footer">
          <div class="time">{{ list.createTime }}</div>
          <div class="btns">
            <div
              hover-class="hover_class"
              v-if="list.hasHandled == null"
              @click="openPopup(list, 0)"
            >
              分配
            </div>
            <div
              hover-class="hover_class"
              v-if="list.hasHandled == null"
              @click="openPopup(list, 1)"
            >
              拒接
            </div>
            <div hover-class="hover_class" @click="goDetail(list)">查看</div>
          </div>
        </div>
      </div>

      <div>
        <uni-load-more iconType="circle" :status="loadMoreStatus" />
      </div>
    </div>

    <!-- 退回 转派 -->
    <uni-popup ref="popup" border-radius="10px 10px 0 0">
      <div class="popup-content">
        <div class="popup-bg"><image src="@/static/images/admin/p-bg.png"></image></div>
        <div class="popup-title">
          {{ popupType === 0 ? '分配' : '拒接' }}
        </div>
        <div class="popup-body">
          <div>
            <div v-if="popupType == 0" style="margin-bottom: 20rpx" @click="openAreaPopup">
              <div class="label">地区:</div>
              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  padding: 10rpx 20rpx;
                  border: 1rpx solid #ddd;
                  border-radius: 8rpx;
                  background: #f9f9f9;
                "
              >
                <span>{{ popupForm.divisionFullName || '选择地区' }}</span>
                <img src="/static/images/assistant/down.png" style="width: 28rpx; height: 14rpx" />
              </div>
            </div>
            <div v-if="popupType == 0" style="margin-bottom: 20rpx">
              <div class="label">小乔助手:</div>
              <div style="width: 100%">
                <uni-data-select
                  v-model="popupForm.assistantId"
                  :localdata="assistantList"
                  placeholder="选择小乔助手"
                ></uni-data-select>
              </div>
            </div>
            <div v-if="popupType == 0">
              <div class="label">分配理由:</div>
              <div class="textarea">
                <textarea placeholder="(100字以内)" v-model="popupForm.assignReason"></textarea>
              </div>
            </div>
            <div v-if="popupType == 1">
              <div class="label">拒接说明</div>
              <div class="textarea">
                <textarea placeholder="(100字以内)" v-model="popupForm.refuseReason"></textarea>
              </div>
            </div>
          </div>
          <div class="btns">
            <div @click="closePopup">取消</div>
            <div @click="submit">确定</div>
          </div>
        </div>
      </div>
    </uni-popup>
    <!-- 收件中心 -->
    <uni-popup ref="popup2" border-radius="10px 10px 0 0">
      <div class="popup-content popup-content2">
        <div class="popup-title2">
          <div>收件列表</div>
          <img src="/static/images/admin/close.png" alt="" @click="closePopup2" />
        </div>
        <div class="table_box">
          <div class="table_head">
            <span>序号</span>
            <span>所在地区</span>
            <span>姓名</span>
            <span>操作</span>
          </div>
          <div class="table_body">
            <div v-for="(item, index) in sjList" :key="index">
              <span>{{ index + 1 }}</span>
              <span>{{ item.divisionFullName }}</span>
              <span>{{ item.contactName }}</span>
              <span>
                <span @click="handleReceive(item)">接受</span>
                <span @click="handleBack(item)">退回</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </uni-popup>

    <!-- 地区选择弹窗 -->
    <uni-popup ref="areaPopup" type="bottom">
      <div class="area-popup">
        <div class="popup-header">
          <div>选择地区</div>
          <img src="/static/images/admin/close.png" alt="" @click="closeAreaPopup" />
        </div>

        <div class="area-list">
          <!-- 省列表 -->
          <div class="city">
            <div :class="provinceIndex ? 'active' : ''">嘉鱼县</div>
          </div>
          <!-- 市区列表 -->
          <div class="city">
            <div
              :class="cityIndex == index ? 'active' : ''"
              v-for="(item, index) in columns[1]"
              :key="index"
              @click="selectCity(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
          <!-- 区列表 -->
          <div class="city">
            <div
              :class="areaIndex == index ? 'active' : ''"
              v-for="(item, index) in columns[2]"
              :key="index"
              @click="selectAreaItem(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>

        <div class="popup-footer">
          <div class="btn cancel" @click="closeAreaPopup">取消</div>
          <div class="btn confirm" @click="confirmArea">确定</div>
        </div>
      </div>
    </uni-popup>
  </div>
</template>

<script>
import {
  helpHandleTelRecordList,
  receiveList,
  reject,
  receiveAndBack,
  helpHandList
} from '@/api/home/<USER>'
import { telTransfer } from '@/api/admin.js'
import { administrativeDivisionRootList } from '@/api/assistant/index.js'
import { debounce } from 'lodash'
import waves from '@/components/waves/waves.vue'
export default {
  components: {
    waves
  },
  data() {
    return {
      tabList: [{ name: '全部' }, { name: '待处理' }, { name: '已完成' }],
      currentIndex: 0,
      type: 0,
      pageNo: 1,
      pageSize: 10,
      keyword: '',
      list: [
        // {
        //   recordName: '全流程落地',
        //   recordId: '***********',
        //   createTime: '2021-11-11 11:11:11'
        // }
      ],
      moreNum: [],
      loadMoreStatus: 'more',
      count: 5,
      value: 1,
      currentList: {},
      rateForm: {
        businessLevel: '1',
        isAnonymous: 0,
        rateReason: '',
        assistantId: '',
        rateScore: 0,
        recordId: ''
      },
      imageStyles: {
        border: {
          color: 'transparent',
          width: 0
        }
      },
      isAgreement: false,
      // 分配 拒接
      popupForm: {
        id: '',
        assistantId: '',
        assignReason: '',
        divisionId: '',
        divisionFullName: '',
        divisionLevel: '',
        refuseReason: ''
      },
      popupType: 0,
      userInfo: {},
      loginType: '',
      assistantList: [],
      // 地区选择相关
      columns: [[], [], []],
      provinceIndex: true,
      cityIndex: 0,
      areaIndex: null,
      query: {
        businessType: 0
      },
      isShowTab: false,
      lineBg: require('@/static/images/admin/xhx.png'),
      sjList: [
        // {
        //   id: 1,
        //   name: '张三',
        //   address: '浙江省杭州市'
        // }
      ]
    }
  },
  watch: {
    keyword: {
      handler: debounce(function () {
        this.pageNo = 1
        this.list = []
        this.loadMoreStatus = 'more'
        this.getList()
      }, 300),
      immediate: false
    }
  },
  async onLoad(options) {
    this.userInfo =
      typeof this.$store.state.user.userInfo == 'string'
        ? JSON.parse(this.$store.state.user.userInfo)
        : this.$store.state.user.userInfo
    this.loginType = uni.getStorageSync('loginType')
    this.type = options.type
    this.getList()

    // 加载地区数据
    await this.fetchAllRegions()
    // 加载帮办员列表
    await this.getAssistantList()

    setTimeout(() => {
      this.isShowTab = true
    }, 50)
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    closePopup2() {
      this.$refs.popup2.close()
    },
    // 获取收件列表
    async getReceiveList() {
      const res = await receiveList()
      if (res.success) {
        this.sjList = res.result.records
      }
    },
    // 接受
    async handleReceive(item) {
      let query = {
        id: item.id,
        isReceive: '1',
        recordId: item.recordId
      }
      const res = await receiveAndBack(query)
      if (res.success) {
        uni.showToast({
          title: '接受成功',
          icon: 'none'
        })
      } else {
        uni.showToast({
          title: res.message,
          icon: 'none'
        })
      }
      this.getReceiveList()
      this.$refs.popup2.close()
    },
    // 退回
    async handleBack(item) {
      let query = {
        id: item.id,
        isReceive: '0',
        recordId: item.recordId,
        refuseReason: ''
      }
      const res = await receiveAndBack(query)
      if (res.success) {
        uni.showToast({
          title: '退回成功',
          icon: 'none'
        })
      } else {
        uni.showToast({
          title: res.message,
          icon: 'none'
        })
      }
      this.getReceiveList()
      this.$refs.popup2.close()
    },
    // 获取帮办员列表
    async getAssistantList() {
      try {
        const res = await helpHandList()
        this.assistantList = res.result.map((item) => ({
          text: item.assistantName,
          value: item.assistantId
        }))
      } catch (error) {
        console.error('获取帮办员列表失败:', error)
      }
    },
    // 加载地区数据
    async fetchAllRegions() {
      try {
        const res = await administrativeDivisionRootList({ pid: 0 })
        // 省数据
        this.columns[0].push(res.result[0])
        this.provinceIndex = true
        // 显示所有二级地区
        this.columns[1] = res.result[0].children || []
        // 默认选中潘家湾镇
        const panjiaWan = res.result[0].children.find((item) => item.name === '潘家湾镇')
        if (panjiaWan) {
          this.cityIndex = this.columns[1].findIndex((item) => item.id === panjiaWan.id)
          // 三级默认选项 - 显示特定村庄
          let allowedVillages = [
            '头墩社区',
            '肖家洲村',
            '四邑村',
            '红光社区',
            '潘湾社区',
            '潘家湾村'
          ]
          this.columns[2] = panjiaWan.children.filter((item) => allowedVillages.includes(item.name))
        }
      } catch (error) {
        console.error('获取地区数据失败:', error)
      }
    },
    openPopup(list, type) {
      this.popupForm.id = list.id
      this.popupForm.divisionLevel = list.divisionLevel
      this.popupType = type
      // 重置表单
      if (type === 0) {
        this.popupForm.assistantId = ''
        this.popupForm.assignReason = ''
        this.popupForm.divisionId = ''
        this.popupForm.divisionFullName = ''
      } else {
        this.popupForm.refuseReason = ''
      }
      this.$refs.popup.open('center')
    },
    // 打开地区选择弹窗
    openAreaPopup() {
      this.$refs.areaPopup.open()
    },
    // 关闭地区选择弹窗
    closeAreaPopup() {
      this.$refs.areaPopup.close()
    },
    // 市点击
    selectCity(item, index) {
      this.$set(this.columns, 2, [])
      this.areaIndex = null
      this.cityIndex = index

      // 筛选子区域
      if (item.name === '潘家湾镇') {
        let allowedVillages = ['头墩社区', '肖家洲村', '四邑村', '红光社区', '潘湾社区', '潘家湾村']
        this.columns[2] = item.children.filter((child) => allowedVillages.includes(child.name))
      } else {
        this.columns[2] = item.children || []
      }
    },
    // 区点击
    selectAreaItem(item, index) {
      this.areaIndex = index
    },
    // 确认地区选择
    confirmArea() {
      if (this.cityIndex === null) {
        uni.showToast({
          icon: 'none',
          title: '请选择地区'
        })
        return
      }

      const selectedCity = this.columns[1][this.cityIndex]
      let fullName = '嘉鱼县' + selectedCity.name
      let divisionId = selectedCity.id
      let divisionLevel = 2

      if (this.areaIndex !== null) {
        const selectedArea = this.columns[2][this.areaIndex]
        fullName += selectedArea.name
        divisionId = selectedArea.id
        divisionLevel = 3
      }

      this.popupForm.divisionId = divisionId
      this.popupForm.divisionFullName = fullName
      this.popupForm.divisionLevel = divisionLevel

      this.closeAreaPopup()
    },
    openPopup2() {
      this.getReceiveList()
      this.$refs.popup2.open('center')
    },
    closePopup() {
      this.$refs.popup.close()
    },
    async submit() {
      let res = null
      if (this.popupType === 0) {
        // 分配逻辑
        if (!this.popupForm.assistantId) {
          uni.showToast({
            icon: 'none',
            title: '请选择帮办员'
          })
          return
        }

        const assignData = {
          id: this.popupForm.id,
          assistantId: this.popupForm.assistantId,
          assignReason: this.popupForm.assignReason || ''
        }

        res = await telTransfer(assignData)
      }
      if (this.popupType === 1) {
        // 拒接逻辑
        res = await reject(this.popupForm)
      }
      if (res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || (this.popupType === 0 ? '分配成功' : '拒接成功')
        })
        setTimeout(() => {
          this.list = []
          this.getList()
          this.$refs.popup.close()
        }, 500)
      } else {
        uni.showToast({
          icon: 'none',
          title: res.message || (this.popupType === 0 ? '分配失败' : '拒接失败')
        })
      }
    },
    async goDetail(item) {
      try {
        // if (item.helpStatus === 'dtj' || item.helpStatus === 'th') {
        if (item.helpStatus === 'dtj') {
          item['isDetail'] = 0
        } else {
          item['isDetail'] = 1
        }
        const encodedRecord = encodeURIComponent(JSON.stringify(item))
        uni.navigateTo({
          url: `/pages/assistant/team-info?record=${encodedRecord}&pageType=3`
        })
        console.log(item)
      } catch (error) {
        console.error('获取详情失败', error)
        uni.showToast({
          icon: 'none',
          title: '获取详情失败'
        })
      }
    },
    onReachBottom() {
      if (this.loadMoreStatus !== 'more') return
      this.loadMoreStatus = 'loading'
      this.pageNo++
      this.getList()
    },
    handleMore(length, index) {
      this.$set(this.moreNum, index, this.moreNum[index] === 2 ? length : 2)
    },
    click(e) {
      this.type = e.index
      this.pageNo = 1
      this.list = []
      this.loadMoreStatus = 'more'
      this.getList()
    },
    async getList() {
      const query = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        keyword: this.keyword
      }
      if (this.type == 0) {
        delete query.status
      }
      if (this.type == 1) {
        query.status = 'dfj'
      }
      if (this.type == 2) {
        query.status = 'ywc'
      }
      try {
        const res = await helpHandleTelRecordList(query)
        if (res.result.records.length < this.pageSize) {
          this.loadMoreStatus = 'noMore'
        } else {
          this.loadMoreStatus = 'more'
        }
        let arr = []
        if (this.type == 0) {
          arr = res.result.records
        }
        if (this.type == 1) {
          arr = res.result.records.filter((item) => {
            return item.hasHandled == null
          })
        }
        if (this.type == 2) {
          arr = res.result.records.filter((item) => {
            return item.hasHandled == '0' || item.hasHandled == '1'
          })
        }
        this.list = this.list.concat(arr)
        console.log('长度', this.list.length)

        if (this.type == 0 || this.type == 2) {
          this.updateBadge(res.result.total)
        } else {
          this.updateBadge(this.list.length)
        }
      } catch (error) {
        this.loadMoreStatus = 'error'
      }
    },
    updateBadge(total) {
      this.tabList.map((item) => {
        delete item.badge
      })
      this.tabList[this.type].badge = { value: total }
    }
  }
}
</script>

<style>
page {
  background-color: #f4f8fc;
}
</style>
<style lang="scss" scoped>
.back_box {
  height: 60rpx;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  img {
    width: 28rpx;
    height: 40rpx;
    transform: rotate(180deg);
    margin-right: 10rpx;
  }
}
.hover_class {
  opacity: 0.8;
}
.search-box {
  padding: 20rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.15);
  border-radius: 0rpx 0rpx 20rpx 20rpx;
  margin-bottom: 50rpx;
  .search-input {
    position: relative;
    height: 60rpx;
    background: #ffffff;
    border-radius: 30rpx;
    border: 2rpx solid #dddddd;
    padding-left: 30rpx;
    margin-bottom: 30rpx;
    input {
      height: 60rpx;
    }
    image {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 32rpx;
      height: 32rpx;
    }
  }
  .bg {
    width: 100%;
    height: 200rpx;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .sj-box {
    width: 100%;
    height: 200rpx;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.list-box {
  padding: 0 20rpx;
}
.list {
  background: url('/static/images/admin/new5.png') no-repeat center center;
  background-size: 100% 100%;
  margin: 0 auto 50rpx auto;
  padding: 20rpx;
  font-family: PingFangSC, PingFang SC;
  color: #666666;
  display: flex;
  flex-direction: column;
  .record-id {
    font-size: 24rpx;
    color: #666666;
    padding: 0 20rpx;
    margin: 20rpx 0;
  }
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
  }
  .title {
    display: flex;
    align-items: center;
    font-family: SourceHanSerifCN, SourceHanSerifCN;
    font-weight: 500;
    font-size: 34rpx;
    color: #2d3645;
    &::before {
      content: '';
      display: inline-block;
      width: 47rpx;
      height: 31rpx;
      background: url('/static/images/admin/new3.png') no-repeat center center;
      background-size: 100% 100%;
      margin-right: 10rpx;
    }
  }
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 20rpx;
    & > div:nth-child(1) {
      width: 76%;
    }
    .status {
      width: 98rpx;
      height: 44rpx;
      line-height: 44rpx;
      background: #0ec060;
      border-radius: 4rpx 22rpx 4rpx 22rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #ffffff;
      text-align: center;
    }
  }
  .btns {
    display: flex;
    justify-content: flex-end;
    & > div {
      width: 114rpx;
      height: 56rpx;
      background: url('/static/images/admin/ck.png') no-repeat center center;
      background-size: 100% 100%;
      line-height: 56rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 26rpx;
      color: #ffffff;
      text-align: center;
      margin-left: 10rpx;
    }
  }
  .time {
    font-weight: 400;
    font-size: 26rpx;
    color: #666666;
  }
  .more {
    position: relative;
    text-align: center;
    font-weight: 400;
    font-size: 24rpx;
    color: #acacac;
    // padding:  0  20rpx;
    image {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 161rpx;
      height: 51rpx;
    }
    .info {
      position: relative;
      z-index: 2;
    }
  }
  .rate-box {
    display: flex;
    justify-content: flex-end;
    margin: 10rpx 0;
  }
  .rate {
    width: 114rpx;
    height: 56rpx;
    border-radius: 8rpx;
    border: 1rpx solid #057ffe;
    text-align: center;
    line-height: 56rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #057ffe;
  }
}
.popup-content {
  position: relative;
  width: 660rpx;
  min-height: 300rpx;
  padding: 30rpx 15rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  background: #fff;
  // overflow-y: auto;

  .popup-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    image {
      width: 100%;
      height: 174rpx;
    }
  }
  .popup-title {
    font-size: 36rpx;
    color: #333;
    text-align: center;
    font-weight: 600;
    height: 64rpx;
    line-height: 64rpx;
    position: relative;
    z-index: 1;
    margin: 0 auto 30rpx;
  }
  .popup-body {
    position: relative;
    z-index: 1;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    .textarea {
      width: 600rpx;
      height: 253rpx;
      margin: 20rpx auto;
      background: #f5f7fb;
      border-radius: 20rpx;
      padding: 20rpx;
    }
    .label {
      font-weight: 400;
      font-size: 28rpx;
      color: #000000;
      width: 120rpx;
      margin-right: 10rpx;
    }
    .btns {
      display: flex;
      justify-content: space-around;
      & > div:nth-child(1) {
        width: 220rpx;
        height: 80rpx;
        border-radius: 8rpx;
        border: 2rpx solid #057ffe;
        line-height: 80rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #057ffe;
        text-align: center;
      }
      & > div:nth-child(2) {
        width: 220rpx;
        height: 80rpx;
        line-height: 80rpx;
        background: #057ffe;
        border-radius: 8rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #ffffff;
        text-align: center;
      }
    }
  }
  .table_box {
    position: relative;
    z-index: 3;
  }
  .table_head {
    height: 70rpx;
    line-height: 70rpx;
    background: #e7eefd;
    border-radius: 38rpx;
    font-family: SourceHanSerifCN, SourceHanSerifCN;
    font-weight: 400;
    font-size: 31rpx;
    color: #4f94d4;
    display: flex;
    & > span {
      flex: 1;
      text-align: center;
    }
    & > span:nth-child(2) {
      flex: 1.5;
    }
  }
  .table_body {
    height: 300px;
    overflow-y: auto;
    & > div {
      height: 100rpx;
      // background: #f6faff;
      border-radius: 38rpx;
      margin: 30rpx 0;
      display: flex;
      align-items: center;
      & > span {
        flex: 1;
        text-align: center;
        font-family: SourceHanSerifCN, SourceHanSerifCN;
        font-weight: 400;
        font-size: 31rpx;
        color: #505c73;
      }
      & > span:nth-child(2) {
        flex: 1.5;
      }
      & > span:nth-child(4) {
        display: inline-block;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        & > span {
          height: 52rpx;
          line-height: 52rpx;
          background: url('/static/images/admin/js_btn.png') no-repeat center center;
          background-size: 100% 100%;
          font-family: SourceHanSerifCN, SourceHanSerifCN;
          font-weight: 400;
          font-size: 26rpx;
          color: #2e8aff;
          margin: 5rpx 0;
        }
      }
    }
  }
}
.popup-content2 {
  background: url('/static/images/admin/new2.png') no-repeat center center !important;
  background-size: 100% 100% !important;
  .popup-title2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: SourceHanSerifCN, SourceHanSerifCN;
    font-weight: 500;
    font-size: 34rpx;
    color: #2d3645;
    margin: 20rpx 0;
    padding: 0 20rpx;
    & > div {
      &::before {
        content: '';
        display: inline-block;
        width: 47rpx;
        height: 31rpx;
        background: url('/static/images/admin/new3.png') no-repeat center center;
        background-size: 100% 100%;
        margin-right: 10rpx;
      }
    }
    img {
      width: 41rpx;
      height: 41rpx;
    }
  }
}
::v-deep .uni-select__input-placeholder {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx !important;
}
::v-deep .uni-select__input-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx !important;
}
::v-deep .uni-select {
  height: auto !important;
}
::v-deep .uni-select__input-text {
  white-space: wrap !important;
  text-overflow: inherit !important;
}
::v-deep .u-tabs__wrapper__nav__line {
  height: 14rpx !important;
}

/* 地区选择弹窗样式 */
.area-popup {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
  max-height: 80vh;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.popup-header img {
  width: 40rpx;
  height: 40rpx;
}

.area-list {
  display: flex;
  height: 400rpx;
  // border: 1rpx solid #eee;
  border-radius: 10rpx;
  overflow: hidden;
}

.city {
  flex: 1;
  // border-right: 1rpx solid #eee;
  overflow-y: auto;
}

.city:last-child {
  border-right: none;
}

.city > div {
  padding: 20rpx;
  font-size: 28rpx;
  color: #666;
  // border-bottom: 1rpx solid #f5f5f5;
  cursor: pointer;
}

.city > div:last-child {
  border-bottom: none;
}

.city > div.active {
  background: #e7f3ff;
  color: #007aff;
  font-weight: 500;
}

.popup-footer {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
}

.popup-footer .btn {
  width: 200rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 10rpx;
  font-size: 30rpx;
}

.popup-footer .cancel {
  border: 2rpx solid #007aff;
  color: #007aff;
  background: #fff;
}

.popup-footer .confirm {
  background: #007aff;
  color: #fff;
}
</style>
