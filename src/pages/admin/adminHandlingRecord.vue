<template>
  <div>
    <div class="search-box">
      <div class="back_box" @click="goBack">
        <img src="/static/images/admin/xq_back.png" alt="" />
        <div>返回</div>
      </div>
      <div class="search-input">
        <input type="text" placeholder="请输入关键字" v-model="keyword" />
        <img src="/static/images/admin/ss_btn.png" />
      </div>
      <!-- <div class="sj-box" @click="goSj">
        <img src="/static/images/admin/sj_bg.png" alt="" />
      </div> -->
      <u-tabs
        v-if="isShowTab"
        :activeStyle="{
          color: 'rgba(20,20,20,0.9)',
          fontWeight: '500',
          transform: 'scale(1.05)'
        }"
        :list="tabList"
        @click="click"
        :current="type"
        :lineColor="`url(${lineBg}) 100% 100%`"
      ></u-tabs>
    </div>
    <div class="list-box">
      <div v-for="(list, index) in list" :key="index" class="list">
        <div class="title">
          <text>{{ list.itemName || list.helpContent }}</text>
        </div>
        <div class="status-box">
          <div class="status" v-show="list.helpStatus == 'dcl' && list.isTransfer == 0">办理中</div>
          <div
            class="status"
            style="width: 160rpx"
            v-show="list.helpStatus == 'dcl' && list.isTransfer == 1"
          >
            办理中(转派)
          </div>
          <div class="status" style="background: #f24f4b" v-show="list.helpStatus == 'th'">
            退回
          </div>
          <div class="status" style="background: #0ec060" v-show="list.helpStatus == 'tg'">
            完成
          </div>
          <text>{{ list.recordId }}</text>
        </div>
        <div class="footer">
          <div class="btns">
            <div
              hover-class="hover_class"
              v-if="list.helpStatus == 'dcl'"
              @click="openPopup(list, 3)"
            >
              办事
            </div>
            <div
              hover-class="hover_class"
              v-if="list.helpStatus == 'dcl'"
              @click="openPopup(list, 1)"
              style="width: 150rpx"
            >
              转派
            </div>
            <div
              hover-class="hover_class"
              v-if="list.helpStatus == 'dcl'"
              @click="openPopup(list, 2)"
            >
              退回
            </div>
            <div
              hover-class="hover_class"
              v-if="list.helpStatus == 'dcl' && userInfo.isAssistManger == 1"
              @click="openPopup(list, 0)"
            >
              分配
            </div>
            <!-- <div
              hover-class="hover_class"
              v-if="
                list.helpStatus == 'dcl' && userInfo.isAssist == 1 && !userInfo.isAssistManger == 1
              "
              @click="openPopup(list, 1)"
            >
              转派
            </div> -->
            <div hover-class="hover_class" @click="goDetail(list, 3)">查看</div>
          </div>
          <div class="time">{{ list.createTime }}</div>
        </div>
      </div>

      <div>
        <uni-load-more iconType="circle" :status="loadMoreStatus" />
      </div>
    </div>

    <!-- 退回 转派 办事 -->
    <uni-popup ref="popup" border-radius="10px 10px 0 0">
      <div class="popup-content">
        <div class="popup-bg"><image src="@/static/images/admin/p-bg.png"></image></div>
        <div class="popup-title" v-show="popupType === 0">分配</div>
        <div class="popup-title" v-show="popupType === 1">转派</div>
        <div class="popup-title" v-show="popupType === 2">退回</div>
        <div class="popup-title" v-show="popupType === 3">办事</div>
        <div class="popup-body">
          <div>
            <div
              v-if="popupType !== 2 && popupType !== 3"
              style="display: flex; align-items: center; margin-bottom: 20rpx"
            >
              <div class="label">小乔助手</div>
              <div style="flex: 1">
                <uni-data-select
                  v-model="popupForm.assistantId"
                  :localdata="userList"
                ></uni-data-select>
              </div>
            </div>
            <div class="label" v-show="popupType === 0">分配申请</div>
            <div class="label" v-show="popupType === 1">转派申请</div>
            <div class="label" v-show="popupType === 2">退回申请</div>
            <div class="textarea">
              <textarea placeholder="(100字以内)" v-model="popupForm.reason"></textarea>
            </div>
          </div>
          <div class="btns">
            <div @click="closePopup">取消</div>
            <div @click="submit">确定</div>
          </div>
        </div>
      </div>
    </uni-popup>

    <!-- 收件中心 -->
    <uni-popup ref="popup2" border-radius="10px 10px 0 0">
      <div class="popup-content popup-content2">
        <div class="popup-title2">
          <div>收件列表</div>
          <img src="/static/images/admin/close.png" alt="" @click="closePopup2" />
        </div>
        <div class="table_box">
          <div class="table_head">
            <span>序号</span>
            <span>所在地区</span>
            <span>姓名</span>
            <span>操作</span>
          </div>
          <div class="table_body">
            <div v-for="(item, index) in sjList" :key="index">
              <span>{{ index + 1 }}</span>
              <span>{{ item.divisionFullName }}</span>
              <span>{{ item.contactName }}</span>
              <span>
                <span @click="handleReceive(item)">接受</span>
                <span @click="handleBack(item)">退回</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </uni-popup>
  </div>
</template>

<script>
import {
  helpHandAdminList,
  transfer,
  helpHandList,
  helpHandListNew,
  helpBack,
  finish,
  receiveList,
  receiveAndBack,
  loginByAppCode
} from '@/api/home/<USER>'
import { debounce } from 'lodash'
import waves from '@/components/waves/waves.vue'
import { newSetToken } from '@/utils/auth'
export default {
  components: {
    waves
  },
  data() {
    return {
      tabList: [{ name: '全部' }, { name: '办理中' }, { name: '已完成' }],
      currentIndex: 0,
      type: 0,
      pageNo: 1,
      pageSize: 10,
      keyword: '',
      list: [],
      moreNum: [],
      loadMoreStatus: 'more',
      count: 5,
      value: 1,
      currentList: {},
      rateForm: {
        businessLevel: '1',
        isAnonymous: 0,
        rateReason: '',
        assistantId: '',
        rateScore: 0,
        recordId: ''
      },
      imageStyles: {
        border: {
          color: 'transparent',
          width: 0
        }
      },
      isAgreement: false,
      // 转派
      popupForm: {
        // businessLevel: '',
        // reason: '',
        // recordId: '',
        // assistantId: '',
        // isTransfer: 1
      },
      popupType: 0,
      userInfo: {},
      loginType: '',
      userList: [],
      query: {
        businessType: 0
      },
      isShowTab: false,
      lineBg: require('@/static/images/admin/xhx.png'),
      sjList: []
    }
  },
  watch: {
    keyword: {
      handler: debounce(function () {
        this.pageNo = 1
        this.list = []
        this.loadMoreStatus = 'more'
        this.getList()
      }, 300),
      immediate: false
    }
  },
  async onLoad(options) {
    if (options.username) {
      try {
        const res = await loginByAppCode({ userId: options.username })
        if (res.success) {
          newSetToken(res.result.token)
          const avatar = res.result.userInfo.avatar
          const nickname = res.result.userInfo.nickName
          this.$store.commit('SET_NAME', nickname)
          this.$store.commit('SET_AVATAR', avatar)
          this.$store.commit('SET_USERINFO', res.result.userInfo)
          this.$store.commit('setRoleId', 1)
          uni.setStorageSync('loginType', 1)

          this.userInfo =
            typeof this.$store.state.user.userInfo == 'string'
              ? JSON.parse(this.$store.state.user.userInfo)
              : this.$store.state.user.userInfo
          this.loginType = uni.getStorageSync('loginType')
          this.type = options.type
          this.getList()
          this.getTeamList()

          setTimeout(() => {
            this.isShowTab = true
          }, 50)
        }
      } catch (error) {
        console.error('登录失败:', error)
      }
    } else {
      this.userInfo =
        typeof this.$store.state.user.userInfo == 'string'
          ? JSON.parse(this.$store.state.user.userInfo)
          : this.$store.state.user.userInfo
      this.loginType = uni.getStorageSync('loginType')
      this.type = options.type
      this.getList()
      this.getTeamList()

      setTimeout(() => {
        this.isShowTab = true
      }, 50)
    }
  },
  methods: {
    goBack() {
      // uni.navigateBack()
      // /pages/admin/index
      uni.redirectTo({
        url: '/pages/admin/index'
      })
    },
    // 获取团队成员
    async getTeamList() {
      const res = await helpHandList()
      res.result.map((item) => {
        this.userList.push({
          text: item.assistantName,
          value: item.assistantId
        })
      })
    },
    openPopup(list, type) {
      this.popupType = type
      if (type === 0 || type === 1) {
        // this.popupForm.businessLevel = list.businessLevel
        this.popupForm.recordId = list.recordId
        // this.popupForm = list
        this.popupForm.reason = ''
        this.popupForm.assistantId = ''
        this.popupForm.isTransfer = '1'
      }
      if (type === 2 || type === 3) {
        this.popupForm.recordId = list.recordId
        this.popupForm.reason = ''
      }
      this.$refs.popup.open('center')
    },
    closePopup() {
      this.$refs.popup.close()
    },
    closePopup2() {
      this.$refs.popup2.close()
    },
    async submit() {
      let res = null
      if (this.popupType === 0 || this.popupType === 1) {
        if (this.popupForm.assistantId == '') {
          uni.showToast({
            icon: 'none',
            title: '请选择小乔助手'
          })
          return
        }
        res = await transfer(this.popupForm)
      }
      if (this.popupType === 2) {
        // 退回理由必填
        if (this.popupForm.reason == '') {
          uni.showToast({
            icon: 'none',
            title: '请输入退回理由'
          })
          return
        }
        res = await helpBack(this.popupForm)
      }
      if (this.popupType === 3) {
        if (this.popupForm.reason == '') {
          uni.showToast({
            icon: 'none',
            title: '请输入办事理由'
          })
          return
        }
        this.popupForm['handleResult'] = this.popupForm.reason
        delete this.popupForm.reason
        res = await finish(this.popupForm)
      }
      if (res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message
        })
        setTimeout(() => {
          this.list = []
          this.getList()
          this.$refs.popup.close()
        }, 500)
      } else {
        uni.showToast({
          icon: 'none',
          title: res.message
        })
      }
    },
    // async goDetail(item) {
    //   const res = await getRecordDetail({
    //     recordId: item.recordId,
    //     itemId: item.itemId
    //   })
    //   uni.navigateTo({
    //     url: `/pages/assistant/team-manage?record=${JSON.stringify(res.result)}`
    //   })
    // },
    async goDetail(item, editType = 0) {
      try {
        // if (item.helpStatus === 'dtj' || item.helpStatus === 'th') {
        if (item.helpStatus === 'dtj') {
          item['isDetail'] = 0
        } else {
          item['isDetail'] = 1
        }
        const encodedRecord = encodeURIComponent(JSON.stringify(item))
        uni.navigateTo({
          url: `/pages/assistant/team-info?record=${encodedRecord}&editType=${editType}&pageType=1`
        })
        console.log(item)
      } catch (error) {
        console.error('获取详情失败', error)
        uni.showToast({
          icon: 'none',
          title: '获取详情失败'
        })
      }
    },
    goSj() {
      this.getReceiveList()
      this.$refs.popup2.open('center')
    },
    onReachBottom() {
      if (this.loadMoreStatus !== 'more') return
      this.loadMoreStatus = 'loading'
      this.pageNo++
      this.getList()
    },
    handleMore(length, index) {
      this.$set(this.moreNum, index, this.moreNum[index] === 2 ? length : 2)
    },
    click(e) {
      this.tabList.forEach((item, index) => {
        delete item.badge
      })
      this.type = e.index
      this.pageNo = 1
      this.list = []
      this.loadMoreStatus = 'more'
      this.getList()
    },
    async getList() {
      const query = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        keyword: this.keyword
        // recordId: '202504010000002'
      }
      if (this.type == 1) query.status = 'blz'
      if (this.type == 2) query.status = 'ywc'
      if (this.type == 3) query.status = 'ywc'
      try {
        let res = null
        if (this.userInfo.isAssistManger == 0) {
          res = await helpHandListNew(query)
        } else {
          res = await helpHandAdminList(query)
        }
        if (res.result.records.length < this.pageSize) {
          this.loadMoreStatus = 'noMore'
        } else {
          this.loadMoreStatus = 'more'
        }
        this.list = this.list.concat(res.result.records)
        console.log('------------', this.list)

        this.updateBadge(res.result.total)
      } catch (error) {
        this.loadMoreStatus = 'error'
      }
    },
    updateBadge(total) {
      if (total > 0) {
        this.$set(this.tabList[this.type], 'badge', { value: total })
      } else {
        this.$set(this.tabList[this.type], 'badge', null)
      }
    },
    // 获取收件列表
    async getReceiveList() {
      const res = await receiveList()
      if (res.success) {
        this.sjList = res.result.records
      }
    },
    // 接受
    async handleReceive(item) {
      let query = {
        id: item.id,
        isReceive: '1',
        recordId: item.recordId
      }
      const res = await receiveAndBack(query)
      if (res.success) {
        uni.showToast({
          title: '接受成功',
          icon: 'none'
        })
      } else {
        uni.showToast({
          title: res.message,
          icon: 'none'
        })
      }
      this.getReceiveList()
      this.$refs.popup2.close()
    },
    // 退回
    async handleBack(item) {
      let query = {
        id: item.id,
        isReceive: '0',
        recordId: item.recordId,
        refuseReason: ''
      }
      const res = await receiveAndBack(query)
      if (res.success) {
        uni.showToast({
          title: '退回成功',
          icon: 'none'
        })
      } else {
        uni.showToast({
          title: res.message,
          icon: 'none'
        })
      }
      this.getReceiveList()
      this.$refs.popup2.close()
    }
  }
}
</script>

<style>
page {
  background: #f7fbff;
}
</style>
<style lang="scss" scoped>
.back_box {
  height: 60rpx;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  img {
    width: 28rpx;
    height: 40rpx;
    transform: rotate(180deg);
    margin-right: 10rpx;
  }
}
.hover_class {
  opacity: 0.8;
}
.search-box {
  padding: 10rpx 20rpx 20rpx 20rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.15);
  border-radius: 0rpx 0rpx 20rpx 20rpx;
  margin-bottom: 50rpx;
  .search-input {
    position: relative;
    height: 60rpx;
    background: url('/static/images/admin/ss_bg.png') no-repeat;
    background-size: 100% 100%;
    padding-left: 30rpx;
    margin-bottom: 30rpx;
    input {
      height: 60rpx;
    }
    img {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 32rpx;
      height: 32rpx;
    }
  }
  .bg {
    width: 100%;
    height: 200rpx;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .sj-box {
    width: 100%;
    height: 200rpx;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.list-box {
  padding: 0 20rpx;
}
.list {
  background: url('/static/images/admin/new5.png') no-repeat center center;
  background-size: 100% 100%;
  margin: 0 auto 50rpx auto;
  padding: 20rpx;
  font-family: PingFangSC, PingFang SC;
  display: flex;
  flex-direction: column;
  .title {
    display: flex;
    align-items: center;
    font-family: SourceHanSerifCN, SourceHanSerifCN;
    font-weight: 500;
    font-size: 34rpx;
    color: #2d3645;
    &::before {
      content: '';
      display: inline-block;
      width: 47rpx;
      height: 31rpx;
      background: url('/static/images/admin/new3.png') no-repeat center center;
      background-size: 100% 100%;
      margin-right: 10rpx;
    }
  }
  .status-box {
    font-family: SourceHanSerifCN, SourceHanSerifCN;
    font-weight: 400;
    font-size: 26rpx;
    color: #666666;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 2rpx solid #e7f0ff;
    margin-bottom: 20rpx;
  }
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 20rpx;
    & > div:nth-child(1) {
      width: 76%;
    }
  }
  .status {
    width: 98rpx;
    height: 44rpx;
    line-height: 44rpx;
    background: #0ec060;
    border-radius: 4rpx 22rpx 4rpx 22rpx;
    font-weight: 500;
    font-size: 24rpx;
    color: #ffffff;
    text-align: center;
  }
  .btns {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 10rpx;
    & > div {
      width: 120rpx;
      height: 51rpx;
      background: url('/static/images/admin/ck.png') no-repeat center center;
      background-size: 100% 100%;
      line-height: 51rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 26rpx;
      color: #ffffff;
      text-align: center;
      margin-left: 8rpx;
    }
  }
  .time {
    // width: 230rpx;
    font-family: SourceHanSerifCN, SourceHanSerifCN;
    font-weight: 400;
    font-size: 26rpx;
    color: #666666;
  }
  .rate-box {
    display: flex;
    justify-content: flex-end;
    margin: 10rpx 0;
  }
  .rate {
    width: 114rpx;
    height: 56rpx;
    border-radius: 8rpx;
    border: 1rpx solid #057ffe;
    text-align: center;
    line-height: 56rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #057ffe;
  }
}
.popup-content {
  position: relative;
  width: 660rpx;
  min-height: 300rpx;
  padding: 30rpx 15rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  background: #fff;

  .popup-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    image {
      width: 100%;
      height: 174rpx;
    }
  }
  .popup-title {
    font-size: 36rpx;
    color: #333;
    text-align: center;
    font-weight: 600;
    width: 194rpx;
    height: 64rpx;
    line-height: 64rpx;
    position: relative;
    z-index: 1;
    margin: 0 auto 30rpx;
  }
  .popup-body {
    position: relative;
    z-index: 1;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    .textarea {
      width: 600rpx;
      height: 253rpx;
      margin: 20rpx auto;
      background: #f5f7fb;
      border-radius: 20rpx;
      padding: 20rpx;
    }
    .label {
      font-weight: 400;
      font-size: 28rpx;
      color: #000000;
      width: 120rpx;
    }
    .btns {
      display: flex;
      justify-content: space-around;
      & > div:nth-child(1) {
        width: 220rpx;
        height: 80rpx;
        border-radius: 8rpx;
        border: 2rpx solid #057ffe;
        line-height: 80rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #057ffe;
        text-align: center;
      }
      & > div:nth-child(2) {
        width: 220rpx;
        height: 80rpx;
        line-height: 80rpx;
        background: #057ffe;
        border-radius: 8rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #ffffff;
        text-align: center;
      }
    }
  }
  .table_box {
    position: relative;
    z-index: 3;
  }
  .table_head {
    height: 70rpx;
    line-height: 70rpx;
    background: #e7eefd;
    border-radius: 38rpx;
    font-family: SourceHanSerifCN, SourceHanSerifCN;
    font-weight: 400;
    font-size: 31rpx;
    color: #4f94d4;
    display: flex;
    & > span {
      flex: 1;
      text-align: center;
    }
    & > span:nth-child(2) {
      flex: 1.5;
    }
  }
  .table_body {
    height: 300px;
    overflow-y: auto;
    & > div {
      height: 100rpx;
      // background: #f6faff;
      border-radius: 38rpx;
      margin: 30rpx 0;
      display: flex;
      align-items: center;
      & > span {
        flex: 1;
        text-align: center;
        font-family: SourceHanSerifCN, SourceHanSerifCN;
        font-weight: 400;
        font-size: 31rpx;
        color: #505c73;
      }
      & > span:nth-child(2) {
        flex: 1.5;
      }
      & > span:nth-child(4) {
        display: inline-block;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        & > span {
          height: 52rpx;
          line-height: 52rpx;
          background: url('/static/images/admin/js_btn.png') no-repeat center center;
          background-size: 100% 100%;
          font-family: SourceHanSerifCN, SourceHanSerifCN;
          font-weight: 400;
          font-size: 26rpx;
          color: #2e8aff;
          margin: 5rpx 0;
        }
      }
    }
  }
}
.popup-content2 {
  background: url('/static/images/admin/new2.png') no-repeat center center !important;
  background-size: 100% 100% !important;
  .popup-title2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: SourceHanSerifCN, SourceHanSerifCN;
    font-weight: 500;
    font-size: 34rpx;
    color: #2d3645;
    margin: 20rpx 0;
    padding: 0 20rpx;
    & > div {
      &::before {
        content: '';
        display: inline-block;
        width: 47rpx;
        height: 31rpx;
        background: url('/static/images/admin/new3.png') no-repeat center center;
        background-size: 100% 100%;
        margin-right: 10rpx;
      }
    }
    img {
      width: 41rpx;
      height: 41rpx;
    }
  }
}
::v-deep .uni-select__input-placeholder {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx !important;
}
::v-deep .uni-select__input-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx !important;
}
::v-deep .u-tabs__wrapper__nav__line {
  height: 14rpx !important;
}
</style>
