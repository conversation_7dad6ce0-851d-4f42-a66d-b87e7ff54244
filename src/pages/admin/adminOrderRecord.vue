<template>
  <view>
    <!-- 		<view class="backboxs">
			<image class="back" @click="back()" src="@/static/images/map/back.png" mode=""></image>
		</view> -->
    <view class="search-box">
      <div class="back_box" @click="goBack">
        <img src="/static/images/admin/xq_back.png" alt="" />
        <div>返回</div>
      </div>
      <view class="tip">
        <!-- 				<input type="text" placeholder="请输入关键字" v-model="keyword" />
				<image src="/static/images/mine/search.png"></image> -->
        <input
          class="uni-input"
          v-model="keyword"
          focus
          @input="clearInput"
          placeholder="请输入关键字"
        />
        <view class="cacel_button" v-if="showClearIcon" @click="clearIcon">
          <image
            class="cacel"
            src="@/static/images/map/cancel.png"
            mode=""
          ></image>
        </view>
        <view class="search_boxs" @click="nearby_search">
          <image
            style="z-index: 99999"
            class="search"
            src="@/static/images/map/search.png"
            mode=""
          >
          </image>
        </view>
      </view>
      <u-tabs
        :activeStyle="{
          color: 'rgba(20,20,20,0.9)',
          fontWeight: '500',
          transform: 'scale(1.05)',
        }"
        :list="tabList"
        @click="tabChange"
        :current="type"
        :lineColor="`url(${lineBg}) 100% 100%`"
      ></u-tabs>
    </view>
    <view class="list-box">
      <div v-for="(list, index) in list" :key="index" class="list">
        <div class="title_box">
          <span class="title">{{ list.itemName }}</span>
        </div>
        <view class="status_box">
          <view
            class="status"
            style="background: #fda600"
            v-show="list.transactStatus == 1"
          >
            {{ list.transactStatus_dictText }}
          </view>
          <view
            class="status"
            style="background: #0ec060"
            v-show="list.transactStatus == 2"
          >
            {{ list.transactStatus_dictText }}
          </view>
          <view
            class="status"
            style="background: #11a9ee"
            v-show="list.transactStatus == 3"
          >
            {{ list.transactStatus_dictText }}
          </view>
          <view class="time">
            {{ list.businessId }}
          </view>
        </view>
        <view class="function_box">
          <view class="function">
            <view
              class="button"
              @click="distribute(list.id)"
              v-show="list.transactStatus == 1"
            >
              分配
            </view>
            <view
              class="button"
              @click="handleOrder(list.id)"
              v-show="list.handleStatus == 1 && list.transactStatus != 1"
            >
              预约
            </view>
            <view
              class="button"
              @click="handleConduct(list.id)"
              v-show="list.handleStatus == 2 && list.transactStatus != 1"
            >
              办理
            </view>
            <view class="button" @click="see(list.id)"> 查看 </view>
          </view>
          <div class="time">{{ list.createTime }}</div>
        </view>
      </div>
      <view>
        <uni-load-more iconType="circle" :status="loadMoreStatus" />
      </view>
    </view>
    <!-- 分配-start -->
    <uni-popup
      class="answerBoxs"
      @maskClick="assistantClick"
      ref="allocate"
      type="center"
      mask-background-color="rgba(0,0,0,0.1)"
    >
      <view class="content">
        <image
          class="report_title"
          src="@/static/images/interaction/report_title.png"
          mode=""
        ></image>
        <image
          class="cross"
          @click="allocateBack()"
          src="@/static/images/cross.png"
          mode=""
        ></image>
        <view class="business b34"> 分配 </view>
        <div class="cell top80">
          <div class="label label2 f30">地区:</div>
          <div
            @click="openAreaPopup"
            class="value2"
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 10rpx 20rpx;
              border: 1rpx solid #ddd;
              border-radius: 8rpx;
              background: #f9f9f9;
            "
          >
            <span>{{ divisionFullName || "选择地区" }}</span>
            <img
              src="/static/images/assistant/down.png"
              style="width: 28rpx; height: 14rpx"
            />
          </div>
        </div>
        <div class="cell">
          <div class="label label2 f30">小乔助手:</div>
          <div class="value2">
            <e-select
              v-model="assistantId"
              clearable
              :options="AssistantList"
              :props="props2"
            ></e-select>
          </div>
        </div>
        <view class="">
          <view class="f30"> 分配理由: </view>
          <textarea
            v-model="assignReason"
            class="text"
            name="rich"
            cols="30"
            rows="10"
            placeholder="请输入分配理由"
          ></textarea>
        </view>
        <div class="btns">
          <div class="btn" @click="allocateDefine()">确定</div>
        </div>
      </view>
    </uni-popup>
    <!-- 分配-end -->
    <!-- 预约-start -->
    <uni-popup
      class="answerBoxs"
      @maskClick="appointmentClick"
      ref="appointment"
      type="center"
      mask-background-color="rgba(0,0,0,0.1)"
    >
      <view class="content">
        <image
          class="report_title"
          src="@/static/images/interaction/report_title.png"
          mode=""
        ></image>
        <image
          class="cross"
          @click="appointmentBack()"
          src="@/static/images/cross.png"
          mode=""
        ></image>
        <view class="business b34"> 预约 </view>
        <view class="entering">
          <view class="f30"> 预约结果: </view>
          <textarea
            v-model="appointmentResult"
            class="text"
            name="rich"
            cols="30"
            rows="10"
            placeholder="请输入预约结果"
          ></textarea>
        </view>
        <div class="btns">
          <div class="btn" @click="appointmentDefine()">确定</div>
        </div>
      </view>
    </uni-popup>
    <!-- 预约-end -->
    <!-- 办理-start -->
    <uni-popup
      class="answerBoxs"
      @maskClick="handleClick"
      ref="handle"
      type="center"
      mask-background-color="rgba(0,0,0,0.1)"
    >
      <view class="content">
        <image
          class="report_title"
          src="@/static/images/interaction/report_title.png"
          mode=""
        ></image>
        <image
          class="cross"
          @click="handleBack()"
          src="@/static/images/cross.png"
          mode=""
        ></image>
        <view class="business b34"> 办理 </view>
        <view class="entering">
          <view class="f30"> 办理结果: </view>
          <textarea
            v-model="handleResult"
            class="text"
            name="rich"
            cols="30"
            rows="10"
            placeholder="请输入办理结果"
          ></textarea>
        </view>
        <div class="btns">
          <div class="btn" @click="handleDefine()">确定</div>
        </div>
      </view>
    </uni-popup>
    <!-- 办理-end -->
    <!-- 地区选择弹窗 -->
    <uni-popup ref="areaPopup" type="bottom">
      <div class="area-popup">
        <div class="popup-header">
          <div>选择地区</div>
          <img
            src="/static/images/admin/close.png"
            alt=""
            @click="closeAreaPopup"
          />
        </div>

        <div class="area-list">
          <!-- 省列表 -->
          <div class="city">
            <div :class="provinceIndex ? 'active' : ''">嘉鱼县</div>
          </div>
          <!-- 市区列表 -->
          <div class="city">
            <div
              :class="cityIndex == index ? 'active' : ''"
              v-for="(item, index) in columns[1]"
              :key="index"
              @click="selectCity(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
          <!-- 区列表 -->
          <div class="city">
            <div
              :class="areaIndex == index ? 'active' : ''"
              v-for="(item, index) in columns[2]"
              :key="index"
              @click="selectAreaItem(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>

        <div class="popup-footer">
          <div class="btn cancel" @click="closeAreaPopup">取消</div>
          <div class="btn confirm" @click="confirmArea">确定</div>
        </div>
      </div>
    </uni-popup>
  </view>
</template>

<script>
import { appointMent } from "@/api/mine/index.js";
import { debounce } from "lodash";
import {
  getAssistantList,
  getAppointment,
  appointmentAssign,
  appointmentProcess,
  appointmentFinish,
} from "@/api/home/<USER>";
import navbar from "@/components/Navbar/index.vue";
import { administrativeDivisionRootList } from "@/api/assistant/index.js";
import waves from "@/components/waves/waves.vue";
export default {
  components: {
    navbar,
    waves,
  },
  data() {
    return {
      lineBg: require("@/static/images/admin/xhx.png"),
      headtitle: "",
      Props: {
        imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: "", //导航高度(动态获取传参)
        bgColor: "", //导航栏背景色,不传参则默认#9CF
        capsuleTop: "", //胶囊顶部距离(动态获取传参)
        textColor: "", //导航标题字体颜色(不传默认#FFF)
        iconColor: "", //icon图标颜色(不传默认#FFF)
        blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
        backText: "", //默认字体(返回)
      },
      tabList: [
        {
          name: "全部",
        },
        {
          name: "待分配",
        },
        {
          name: "办理中",
        },
        {
          name: "已完成",
        },
      ],
      currentIndex: 0,
      type: 0,
      pageNo: 1,
      pageSize: 10,
      keyword: "",
      list: [],
      moreNum: [],
      loadMoreStatus: "more",
      count: 5,
      value: 1,
      currentList: {},
      rateForm: {
        businessLevel: "1",
        isAnonymous: 0,
        rateReason: "",
        assistantId: "",
        rateScore: 0,
        recordId: "",
      },
      imageStyles: {
        border: {
          color: "transparent",
          width: 0,
        },
      },
      isAgreement: false,
      transactStatus: "",
      query: {},
      itemTypeName: "",
      AssistantList: [],
      value1: "",
      props2: {
        text: "assistantName",
        value: "assistantId",
        disabled: "noallowed",
      },
      assistantId: "",
      divisionFullName: "",
      assignReason: "",
      appointmentResult: "",
      handleResult: "",
      showClearIcon: false,
      textareaValue: "",
      // 地区选择相关
      columns: [[], [], []],
      provinceIndex: true,
      cityIndex: 0,
      areaIndex: null,
      query: {
        businessType: 0,
      },
      isShowTab: false,
      lineBg: require("@/static/images/admin/xhx.png"),
      sjList: [
        // {
        //   id: 1,
        //   name: '张三',
        //   address: '浙江省杭州市'
        // }
      ],
    };
  },
  // watch: {
  // 	keyword: {
  // 		handler: debounce(function() {
  // 			this.pageNo = 1
  // 			this.list = []
  // 			this.loadMoreStatus = 'more'
  // 			this.getList()
  // 		}, 300),
  // 		immediate: false
  // 	}
  // },
  mounted() {
    this.getList();
    this.getAssistList();
  },
  async onLoad(options) {
    this.type = options.type;
    // 加载地区数据
    await this.fetchAllRegions();
  },
  methods: {
    goBack() {
      // uni.navigateBack()
      // /pages/admin/index
      uni.redirectTo({
        url: "/pages/admin/index",
      });
    },
    async nearby_search() {
      const that = this;
      that.pageNo = 1;
      that.list = [];
      const query = {
        pageNo: that.pageNo,
        pageSize: that.pageSize,
        itemName: that.keyword,
        transactStatus: that.transactStatus,
      };

      const res = await getAppointment(query);
      console.log(res, 22222);
      if (res.result.records.length < that.pageSize) {
        that.loadMoreStatus = "noMore";
      } else {
        that.loadMoreStatus = "more";
      }
      console.log(that.loadMoreStatus, "more");
      that.list = that.list.concat(res.result.records);
    },
    clearIcon: function () {
      this.keyword = "";
      this.showClearIcon = false;
      this.nearby_search();
    },
    clearInput: function (event) {
      console.log(event, "11111");
      this.keyword = event.detail.value;
      this.nearby_search();
      if (event.detail.value.length > 0) {
        this.showClearIcon = true;
      } else {
        this.showClearIcon = false;
      }
    },
    // 加载地区数据
    async fetchAllRegions() {
      try {
        const res = await administrativeDivisionRootList({ pid: 0 });
        // 省数据
        this.columns[0].push(res.result[0]);
        this.provinceIndex = true;
        // 显示所有二级地区
        this.columns[1] = res.result[0].children || [];
        // 默认选中潘家湾镇
        const panjiaWan = res.result[0].children.find(
          (item) => item.name === "潘家湾镇"
        );
        if (panjiaWan) {
          this.cityIndex = this.columns[1].findIndex(
            (item) => item.id === panjiaWan.id
          );
          // 三级默认选项 - 显示特定村庄
          let allowedVillages = [
            "头墩社区",
            "肖家洲村",
            "四邑村",
            "红光社区",
            "潘湾社区",
            "潘家湾村",
          ];
          this.columns[2] = panjiaWan.children.filter((item) =>
            allowedVillages.includes(item.name)
          );
        }
      } catch (error) {
        console.error("获取地区数据失败:", error);
      }
    },
    // 预约返回
    appointmentBack() {
      this.$refs.appointment.close();
    },
    // 办理返回
    handleBack() {
      this.$refs.handle.close();
    },
    // 办理确定
    async handleDefine() {
      console.log(this.id, "1111");
      console.log(this.handleResult, "2222");
      const popupForm = {
        id: this.id,
        handleResult: this.handleResult,
      };
      const res = await appointmentFinish(popupForm);
      if (res.success) {
        uni.showToast({
          title: "办理成功!",
          icon: "none",
        });
        this.$refs.handle.close();
        this.pageNo = 1;
        this.list = [];
        this.loadMoreStatus = "more";
        this.id = "";
        this.handleResult = "";
        this.getList();
      }
    },
    // 预约确定
    async appointmentDefine() {
      console.log(this.id, "1111");
      console.log(this.appointmentResult, "2222");
      const popupForm = {
        id: this.id,
        appointmentResult: this.appointmentResult,
      };
      const res = await appointmentProcess(popupForm);
      if (res.success) {
        uni.showToast({
          title: "预约成功!",
          icon: "none",
        });
        this.$refs.appointment.close();
        this.pageNo = 1;
        this.list = [];
        this.loadMoreStatus = "more";
        this.id = "";
        this.appointmentResult = "";
        this.getList();
      }
    },
    // 打开地区选择弹窗
    openAreaPopup() {
      this.$refs.areaPopup.open();
    },
    // 关闭地区选择弹窗
    closeAreaPopup() {
      this.$refs.areaPopup.close();
    },
    // 关闭地区选择弹窗
    closeAreaPopup() {
      this.$refs.areaPopup.close();
    },
    // 市点击
    selectCity(item, index) {
      this.$set(this.columns, 2, []);
      this.areaIndex = null;
      this.cityIndex = index;

      // 筛选子区域
      if (item.name === "潘家湾镇") {
        let allowedVillages = [
          "头墩社区",
          "肖家洲村",
          "四邑村",
          "红光社区",
          "潘湾社区",
          "潘家湾村",
        ];
        this.columns[2] = item.children.filter((child) =>
          allowedVillages.includes(child.name)
        );
      } else {
        this.columns[2] = item.children || [];
      }
    },
    // 区点击
    selectAreaItem(item, index) {
      this.areaIndex = index;
    },
    // 确认地区选择
    confirmArea() {
      if (this.cityIndex === null) {
        uni.showToast({
          icon: "none",
          title: "请选择地区",
        });
        return;
      }

      const selectedCity = this.columns[1][this.cityIndex];
      let fullName = "嘉鱼县" + selectedCity.name;
      let divisionId = selectedCity.id;
      let divisionLevel = 2;

      if (this.areaIndex !== null) {
        const selectedArea = this.columns[2][this.areaIndex];
        fullName += selectedArea.name;
        divisionId = selectedArea.id;
        divisionLevel = 3;
      }

      this.divisionId = divisionId;
      this.divisionFullName = fullName;
      this.divisionLevel = divisionLevel;

      this.closeAreaPopup();
    },
    // 预约点击遮盖
    appointmentClick() {
      this.appointmentResult = "";
    },
    // 办理点击遮盖
    handleClick() {
      this.handleResult = "";
    },
    // 分配点击遮盖
    assistantClick() {
      console.log(11111);
      this.assistantId = "";
      this.divisionFullName = "";
      this.assignReason = "";
    },
    // 获取帮办员
    async getAssistList() {
      const res = await getAssistantList();
      console.log(res, "2222");
      this.AssistantList = res.result;
    },
    // 分配返回
    allocateBack() {
      this.$refs.allocate.close();
    },
    // 分配确定
    async allocateDefine() {
      console.log(this.assistantId, "22222");
      console.log(this.assignReason, "3333");
      if (!this.assistantId) {
        uni.showToast({
          icon: "none",
          title: "请选择小乔助手",
        });
      }
      const popupForm = {
        id: this.id,
        assistantId: this.assistantId,
        assignReason: this.assignReason,
        area: this.divisionFullName,
      };
      const res = await appointmentAssign(popupForm);
      if (res.success) {
        uni.showToast({
          title: "分配成功!",
          icon: "none",
        });
        this.$refs.allocate.close();
        this.pageNo = 1;
        this.list = [];
        this.loadMoreStatus = "more";
        this.id = "";
        this.assistantId = "";
        this.assignReason = "";
        this.getList();
      }
    },
    distribute(id) {
      console.log(id, "2222");
      this.id = id;
      this.$refs.allocate.open();
    },
    handleOrder(id) {
      this.id = id;
      this.$refs.appointment.open();
    },
    handleConduct(id) {
      this.id = id;
      this.$refs.handle.open();
    },
    back() {
      uni.navigateBack({
        delta: 1,
      });
    },
    see(id) {
      console.log(id, "988888");
      uni.navigateTo({
        url: "/pages/admin/orderDetail?id=" + id,
      });
    },
    rateChange(e) {
      console.log(e);
      this.rateForm.rateScore = e.value;
    },

    onReachBottom() {
      console.log("90----------------");
      console.log(this.loadMoreStatus, "766666");
      if (this.loadMoreStatus !== "more") return;
      this.loadMoreStatus = "loading";
      this.pageNo++;
      this.getList();
    },
    handleMore(length, index) {
      this.$set(this.moreNum, index, this.moreNum[index] === 2 ? length : 2);
    },
    tabChange(e) {
      this.tabList.forEach((item, index) => {
        // if (index !== this.type) {
        // this.$set(item, 'badge', null)
        delete item.badge;
        // }
      });
      this.type = e.index;
      this.pageNo = 1;
      this.list = [];
      this.loadMoreStatus = "more";
      this.getList();
    },

    async getList() {
      this.transactStatus = this.type;
      if (this.transactStatus == 0) {
        this.transactStatus = "";
      }
      const query = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        itemName: this.keyword,
        transactStatus: this.transactStatus,
      };

      const res = await getAppointment(query);
      console.log(res, 22222);
      if (res.result.records.length < this.pageSize) {
        this.loadMoreStatus = "noMore";
      } else {
        this.loadMoreStatus = "more";
      }
      console.log(this.loadMoreStatus, "more");
      this.list = this.list.concat(res.result.records);

      // 更新当前 tab 的徽标，仅在成功获取数据后调用
      // this.updateBadge(res.result.total)
    },

    updateBadge(total) {
      // 更新当前激活 tab 的徽标
      if (total > 0) {
        this.$set(this.tabList[this.type], "badge", {
          value: total,
        });
      } else {
        this.$set(this.tabList[this.type], "badge", null);
      }
      console.log("tabList", this.tabList);
    },
  },
};
</script>

<style>
page {
  background-color: #f4f8fc;
}
</style>
<style lang="scss" scoped>
.search-box {
  padding: 20rpx;
  height: 223rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.15);
  border-radius: 0rpx 0rpx 20rpx 20rpx;
  margin-bottom: 50rpx;

  .search-input {
    position: relative;
    height: 60rpx;
    background: #ffffff;
    border-radius: 30rpx;
    border: 2rpx solid #dddddd;
    padding-left: 30rpx;
    margin-bottom: 30rpx;

    input {
      height: 60rpx;
    }

    // image {
    // 	position: absolute;
    // 	right: 20rpx;
    // 	top: 50%;
    // 	transform: translateY(-50%);
    // 	width: 32rpx;
    // 	height: 32rpx;
    // }
  }
}

.list {
  // background: linear-gradient(180deg, hsla(204, 100%, 95%, 0.5) 0%, #ffffff 50%, #ffffff 100%);
  background: url("/static/images/admin/new5.png") no-repeat center center;
  background-size: 100% 100%;
  // border: 2rpx solid rgba(255, 255, 255, 0.68);
  margin: 0 auto 30rpx auto;
  min-height: 190rpx;
  padding: 0rpx 20rpx;
  font-family: PingFangSC, PingFang SC;
  display: flex;
  flex-direction: column;
  position: relative;

  .rate-box {
    position: absolute;
    right: 10rpx;
    bottom: 10rpx;
    display: flex;
    justify-content: flex-end;
    margin: 10rpx 0;
  }

  .title_box {
    margin-bottom: 30rpx;
    display: flex;
    justify-content: space-between;
    border-bottom: 2rpx solid #e7f0ff;
    padding: 20rpx 0;

    .title {
      display: flex;
      align-items: center;
      font-family: SourceHanSerifCN, SourceHanSerifCN;
      font-weight: 500;
      font-size: 34rpx;
      color: #2d3645;
      &::before {
        content: "";
        display: inline-block;
        width: 47rpx;
        height: 31rpx;
        background: url("/static/images/admin/new3.png") no-repeat center center;
        background-size: 100% 100%;
        margin-right: 10rpx;
      }
    }

    .status {
      min-width: 98rpx;
      height: 44rpx;
      line-height: 44rpx;
      background: #0ec060;
      border-radius: 4rpx 22rpx 4rpx 22rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #ffffff;
      text-align: center;
    }
  }

  .time {
    min-height: 60rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #acacac;
    line-height: 33rpx;
  }

  .item {
    // margin-bottom: 20rpx;
    padding: 30rpx 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    & > view:nth-child(1) {
      width: 76%;
    }

    .status {
      width: 98rpx;
      height: 44rpx;
      line-height: 44rpx;
      background: #0ec060;
      border-radius: 4rpx 22rpx 4rpx 22rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #ffffff;
      text-align: center;
    }
  }

  .more {
    position: relative;
    text-align: center;
    font-weight: 400;
    font-size: 24rpx;
    color: #acacac;

    image {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 161rpx;
      height: 51rpx;
    }

    .info {
      position: relative;
      z-index: 2;
    }

    .time {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      font-weight: 400;
      font-size: 24rpx;
      color: #acacac;
    }
  }

  .rate-box {
    display: flex;
    justify-content: flex-end;
    margin: 10rpx 0;
  }

  .rate {
    width: 114rpx;
    height: 56rpx;
    border-radius: 8rpx;
    border: 1rpx solid #057ffe;
    text-align: center;
    line-height: 56rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #057ffe;
  }
}

.popup-content {
  position: relative;
  width: 600rpx;
  min-height: 823rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  background: #fff;

  .popup-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 174rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .popup-title {
    position: relative;
    z-index: 2;
    margin-bottom: 50rpx;

    image {
      width: 100rpx;
      height: 100rpx;
      margin-right: 20rpx;
    }

    display: flex;
    align-items: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;

    .name {
      font-size: 32rpx;
      color: #181818;
      margin-bottom: 10rpx;
    }
  }

  .popup-body {
    position: relative;
    z-index: 2;

    .rate-box {
      display: flex;
      align-items: center;

      .title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28rpx;
        color: #444444;
        margin-right: 10rpx;
      }
    }

    .textarea {
      width: 538rpx;
      height: 253rpx;
      margin: 30rpx auto;
      padding: 20rpx;
      background: #f5f7fb;
      border-radius: 20rpx;
    }

    .upload {
      margin: 30rpx 0;

      .icon {
        width: 74rpx;
        height: 63rpx;
        margin-bottom: 8rpx;
      }

      .upload-btn {
        width: 100%;
        height: 100%;
        background: #f5f7fb;
        border-radius: 20rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
      }
    }

    .submit-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .radio {
        display: flex;
        align-items: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;

        image {
          width: 26rpx;
          height: 26rpx;
          margin-right: 10rpx;
        }

        text {
          color: #057ffe;
        }
      }

      .submit {
        width: 282rpx;
        height: 80rpx;
        line-height: 80rpx;
        background: #057ffe;
        border-radius: 8rpx;
        text-align: center;

        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32rpx;
        color: #ffffff;
      }
    }
  }
}

.date {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #acacac;
  line-height: 33rpx;
  text-align: right;
  font-style: normal;
  padding: 40rpx;
}

.backboxs {
  background-color: #fff;
  padding: 10rpx 30rpx;
}

.back {
  width: 40rpx;
  height: 40rpx;
}

::v-deep .u-tabs__wrapper__nav__item {
  padding: 0 40rpx;
}

.list-box {
  padding: 0 20rpx;
}

.function {
  display: flex;
  align-items: center;
  padding: 20rpx 0;

  .button {
    width: 102rpx;
    height: 52rpx;
    background: url("/static/images/admin/ck.png") no-repeat center center;
    background-size: 100% 100%;
    line-height: 52rpx;
    font-weight: 500;
    font-size: 26rpx;
    color: #ffffff;
    margin-right: 22rpx;
    text-align: center;
  }
}

.function_box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20rpx;

  .time {
    min-height: 50rpx;
    font-weight: 400;
    font-size: 26rpx;
    color: #acacac;
    line-height: 50rpx;
  }
}

.status_box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 2rpx solid #e7f0ff;
  padding-bottom: 20rpx;

  .time {
    min-height: 50rpx;
    font-weight: 400;
    font-size: 26rpx;
    color: #acacac;
    line-height: 50rpx;
  }

  .status {
    width: 113rpx;
    height: 50rpx;
    line-height: 50rpx;
    background: #0ec060;
    border-radius: 4rpx 22rpx 4rpx 22rpx;
    font-weight: 500;
    font-size: 24rpx;
    color: #ffffff;
    text-align: center;
  }
}

.answerBoxs {
  height: 500rpx;

  .content {
    margin: 32rpx;
    padding: 55rpx 34rpx 30rpx 34rpx;
    display: flex;
    gap: 30rpx;
    flex-direction: column;
    // align-items: center;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
    border-radius: 32rpx;
    position: relative;

    .btns {
      .btn {
        width: 474rpx;
        height: 76rpx;
        background: linear-gradient(310deg, #0cb6ff 0%, #057ffe 100%);
        border-radius: 36rpx;
        font-weight: 500;
        font-size: 30rpx;
        color: #ffffff;
        line-height: 76rpx;
        text-align: center;
        font-style: normal;
        margin: 0 auto;
      }
    }

    .report_title {
      width: 686rpx;
      height: 180rpx;
      position: absolute;
      top: 0;
      left: 0;
    }

    .entering {
      padding-top: 80rpx;
    }

    .cross {
      width: 36rpx;
      height: 36rpx;
      position: absolute;
      top: 42rpx;
      right: 35rpx;
    }

    .intro {
      position: relative;
      width: 618rpx;
      height: 200rpx;
      padding: 0 5rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #444444;
      line-height: 44rpx;
      font-style: normal;

      .intro_text {
        position: absolute;
      }
    }

    .cell {
      // height: 180rpx;
      // margin-bottom: 30rpx;
      // display: flex;
      align-items: center;
      // padding-top: 80rpx;
    }

    .text {
      width: 610rpx;
      height: 590rpx;
      background: #f5f7fb;
      border-radius: 20rpx;
      padding: 30rpx;

      &:hover {
        border: solid 1rpx rgba(200, 200, 200, 0.7);
      }

      &.row {
        display: flex;
        height: 100%;
      }
    }
  }

  .answer-content {
    min-height: 400rpx;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    background: #fff;
    border-radius: 35rpx;
    background-color: #fff;
    width: 600rpx;
    position: relative;
    padding: 40rpx 20rpx;

    .business {
      font-weight: 600;
      font-size: 36rpx;
      text-align: left;
      float: left;
    }

    .substance {
      width: 600rpx;
      margin-bottom: 40rpx;
      padding: 0 20rpx;
    }

    .query {
      width: 530rpx;
      height: 86rpx;
      background: #137aff;
      border-radius: 50rpx;
      font-weight: 400;
      font-size: 38rpx;
      color: #ffffff;
      line-height: 86rpx;
      text-align: center;
      font-style: normal;
      margin-top: 20rpx;
    }
  }
}

.main-box {
  padding: 30rpx;
  margin: 30rpx auto;
  min-height: 300rpx;
  background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
  border-radius: 32rpx;

  .title {
    width: 180rpx;
    height: 50rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 36rpx;
    color: #141414;
    line-height: 50rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 30rpx;
  }

  .cell {
    // height: 180rpx;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
  }

  .cell2 {
    min-height: 80rpx;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
  }

  .label {
    height: 80rpx;
    line-height: 80rpx;
    width: 160rpx;
    text-align: right;
    margin-right: 15rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 30rpx;
    color: #333333;
  }

  .label2 {
    position: relative;

    &::after {
      content: "*";
      position: absolute;
      left: 40rpx;
      top: 5rpx;
      color: #f24f4b;
    }
  }

  .label3 {
    position: relative;

    &::after {
      content: "*";
      position: absolute;
      left: 10rpx;
      top: 5rpx;
      color: #f24f4b;
    }
  }

  .label5 {
    position: relative;

    &::after {
      content: "*";
      position: absolute;
      left: -10rpx;
      top: 5rpx;
      color: #f24f4b;
    }
  }

  .label4 {
    position: relative;

    &::after {
      content: "*";
      position: absolute;
      left: 50rpx;
      top: 5rpx;
      color: #f24f4b;
    }
  }

  .value {
    height: 80rpx;
    line-height: 80rpx;
    flex: 1;
    background: #f6f6f6;
    border-radius: 6rpx;
    padding-left: 20rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 30rpx;
    color: #444444;
    font-style: normal;

    input {
      height: 80rpx;
      font-size: 30rpx;
    }
  }

  .value2 {
    min-height: 60rpx;
    // line-height: 60rpx;
    flex: 1;
    background: #ffffff;
    border-radius: 6rpx;
    border: 2rpx solid #dddddd;
    padding: 0 20rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 30rpx;
    color: #444444;
    font-style: normal;
    display: flex;
    align-items: center;
    justify-content: space-between;

    img {
      width: 28rpx;
      height: 14rpx;
    }
  }

  .value3 {
    position: relative;

    .pwd {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 36rpx;
      height: 32rpx;
    }
  }

  .btns {
    margin-top: 80rpx;
    display: flex;
    justify-content: space-around;

    .btn {
      width: 142rpx;
      height: 60rpx;
      line-height: 60rpx;
      border-radius: 8rpx;
      border: 2rpx solid #057ffe;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 34rpx;
      color: #057ffe;
      text-align: center;
    }

    .btn2 {
      background: #057ffe;
      border-radius: 8rpx;
      color: #fff;
    }
  }
}

.cacel_button {
  width: 50rpx;
  height: 50rpx;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  /* 图片在垂直方向上居中 */
  justify-content: center;
  /* 图片在水平方向上居中 */
}

.cacel {
  width: 32rpx;
  height: 32rpx;
}

.tip {
  width: 710rpx;
  height: 60rpx;
  margin: 0 20rpx;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 3px;
  z-index: 200;
  border-radius: 30rpx;
  border: 2rpx solid #dddddd;

  .uni-input {
    flex: 1;
    height: 20rpx;
    padding-left: 40rpx;
  }

  span {
    width: 40px;
    color: #8b8c8f;
  }

  .search_boxs {
    width: 50rpx;
    height: 50rpx;
    display: flex;
    align-items: center;
    /* 图片在垂直方向上居中 */
    justify-content: center;
    /* 图片在水平方向上居中 */
    margin-right: 32rpx;
  }

  .search {
    width: 32rpx;
    height: 32rpx;
    z-index: 99999;
  }
}

.f30 {
  font-weight: 400;
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.b34 {
  font-weight: 600;
  font-size: 34rpx;
  color: #444444;
  position: absolute;
}
::v-deep .u-tabs__wrapper__nav__line {
  height: 14rpx !important;
}
.back_box {
  height: 60rpx;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  img {
    width: 28rpx;
    height: 40rpx;
    transform: rotate(180deg);
    margin-right: 10rpx;
  }
}
.top80 {
  padding-top: 80px;
}

/* 地区选择弹窗样式 */
.area-popup {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
  max-height: 80vh;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.popup-header img {
  width: 40rpx;
  height: 40rpx;
}

.area-list {
  display: flex;
  height: 400rpx;
  // border: 1rpx solid #eee;
  border-radius: 10rpx;
  overflow: hidden;
}

.city {
  flex: 1;
  // border-right: 1rpx solid #eee;
  overflow-y: auto;
}

.city:last-child {
  border-right: none;
}

.city > div {
  padding: 20rpx;
  font-size: 28rpx;
  color: #666;
  // border-bottom: 1rpx solid #f5f5f5;
  cursor: pointer;
}

.city > div:last-child {
  border-bottom: none;
}

.city > div.active {
  background: #e7f3ff;
  color: #007aff;
  font-weight: 500;
}

.popup-footer {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
}

.popup-footer .btn {
  width: 200rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 10rpx;
  font-size: 30rpx;
}

.popup-footer .cancel {
  border: 2rpx solid #007aff;
  color: #007aff;
  background: #fff;
}

.popup-footer .confirm {
  background: #007aff;
  color: #fff;
}
</style>
