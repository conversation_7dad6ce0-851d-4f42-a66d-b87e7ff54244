<template>
	<div>
	  <button @click="playAudio">播放音频</button>
	  <button @click="pauseAudio">暂停音频</button>
	  <button @click="stopAudio">停止音频</button>
	</div>
  </template>
  
  <script>
  export default {
	data() {
	  return {
		audioContext: null
	  };
	},
	mounted() {
	  // 创建内部音频上下文对象
	  this.audioContext = uni.createInnerAudioContext();
	  // 设置音频的播放源
	  this.audioContext.src = '../../static/video/mengnan_v2.WAV'
	  // 监听音频播放完成事件
	  this.audioContext.onEnded(() => {
		console.log('音频播放完成');
	  });
  
	  // 监听音频播放错误事件
	  this.audioContext.onError((err) => {
		console.error('音频播放出错:', err);
	  });
	},
	methods: {
	  playAudio() {
		// 播放音频
		this.audioContext.play();
	  },
	  pauseAudio() {
		// 暂停音频
		this.audioContext.pause();
	  },
	  stopAudio() {
		// 停止音频
		this.audioContext.stop();
	  }
	},
	beforeDestroy() {
	  // 在组件销毁前销毁音频上下文对象，释放资源
	  this.audioContext.destroy();
	}
  };
  </script>