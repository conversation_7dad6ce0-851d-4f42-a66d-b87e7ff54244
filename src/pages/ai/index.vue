<template>
  <view class="wrapper">
    <scroll-view
      :scroll-into-view="scrollIntoId"
      class="messages-container"
      scroll-y
      scroll-with-animation
      ref="chatContainer"
      :scroll-top="scrollTop"
      @scroll="handleScroll"
    >
      <image class="bg-image" :src="bgSrc" mode="aspectFill"></image>
      <!-- 			<view class="top-content">
				<view class="title-box">
					<image class="icon" :src="iconSrc" mode='aspectFit'></image>
					<image class="robot" :src="robotSrc" mode='aspectFit'></image>
					<image class="title" :src="titleSrc" mode='aspectFit'></image>
				</view>
			</view> -->
      <button style="display: none" id="myButton" @click="handleButtonClick">
        点击我
      </button>
      <!-- <view class="text-content" :class="showInput ? 'text-content_show' : ''"> -->
      <view class="text-content text-content_show">
        <image class="text-bg" :src="textBgSrc" mode="aspectFit"></image>
        <view class="text-box">
          <view class="title">Hi, 我是小乔智问大模型</view>
          <view class="text"
            >作为你的智能助手，我既能写文案，帮你想点子，也能陪你聊天、解答疑问。</view
          >
        </view>
      </view>
      <view class="grid-body" v-if="!showAsk && showInput">
        <view class="example">
          <view class="left-text">猜你想问：</view>
          <view class="refresh" @click="refreshSrc()">
            <image
              src="@/static/images/refreshSrc.png"
              mode="aspectFit"
            ></image>
            <text class="right-text">换一换</text>
          </view>
        </view>
        <view
          @click="information(item.questionDetail)"
          v-for="(item, index) in exampleList"
          :key="index"
          class="grid-item-box"
        >
          <view class="grid-item">
            <text class="text2">{{ item.questionDetail }}</text>
          </view>
        </view>
      </view>

      <view class="time" v-if="showAsk">{{ currentTime }}</view>
      <view v-for="(item, index) in quesAndAns.data" style="padding: 0 20rpx">
        <view style="display: flex; justify-content: flex-end">
          <!-- <view class="askoranswer ask" :key="index + 'ask'"> -->
          <view class="askoranswer ask" :key="index">
            <view class="askoranswer-c" v-html="item.chatDetail.question.text">
            </view>
            <image
              class="img"
              v-show="item.chatDetail.question.imageUrl"
              :src="item.chatDetail.question.imageUrl"
              mode=""
            ></image>
          </view>
          <image
            class="robot-avatar"
            src="@/static/images/mine/pj.png"
            mode=""
          ></image>
        </view>
        <view
          class=""
          style="display: flex"
          v-show="item.chatDetail.answer.isShow != 1"
        >
          <image
            class="robot-avatar"
            src="@/static/images/xq_header.jpg"
            mode=""
          ></image>
          <view class="askoranswer answer" :key="index + 'answer'">
            <view
              v-if="item.chatDetail.answer.thinkText == ''"
              class="askoranswer-c"
            >
              <text v-show="!item.chatDetail.answer.produceing"
                >暂时无法回答</text
              >
              <text v-show="item.chatDetail.answer.produceing" class="flicker"
                >......</text
              >
            </view>
            <view v-else>
              <view v-show="item.chatDetail.answer.think">
                <button
                  class="think-btn"
                  @click="close(item.chatDetail.answer.thinkshow, index)"
                >
                  <i class="el-icon-cpu"></i>
                  <span style="margin: 0 2px">已深度思考</span>
                  <u-icon
                    name="arrow-up"
                    v-show="item.chatDetail.answer.thinkshow"
                  />
                  <u-icon
                    name="arrow-down"
                    v-show="!item.chatDetail.answer.thinkshow"
                  />
                  <!-- 									<i class="el-icon-arrow-up" v-show="item.chatDetail.answer.thinkshow"></i>
									<i class="el-icon-arrow-down" v-show="!item.chatDetail.answer.thinkshow"></i> -->
                </button>
                <vue-markdown
                  v-show="item.chatDetail.answer.thinkshow"
                  style="color: #8b8b8b; font-size: 14px; padding: 0 10px"
                  :source="item.chatDetail.answer.thinkText"
                ></vue-markdown>
              </view>
              <!-- <view class="askoranswer-c" v-html="item.chatDetail.answer.text"></view> -->
              <vue-markdown
                :source="item.chatDetail.answer.text"
              ></vue-markdown>
              <view class="icon-group">
                <!-- <view>
                  <image
                    @click="changeType(index, item.chatDetail.answer.assist)"
                    v-show="item.chatDetail.answer.assist && typeShow"
                    class="answer-icon"
                    :src="assist"
                    mode="aspectFit"
                  ></image>
                  <image
                    class="answer-icon"
                    :src="step"
                    mode="aspectFit"
                  ></image>
                </view> -->
                <view></view>
                <view>
                  <image
                    @click="likeAnswer(index)"
                    v-show="!item.isLiked"
                    class="share-icon"
                    :src="assist"
                    mode="aspectFit"
                  ></image>
                  <image
                    @click="likeAnswer(index)"
                    v-show="item.isLiked"
                    class="share-icon"
                    :src="assisting"
                    mode="aspectFit"
                  ></image>
                  <image
                    @click="dislikeAnswer(index)"
                    v-show="!item.isDisliked"
                    class="share-icon"
                    :src="step"
                    mode="aspectFit"
                  ></image>
                  <image
                    @click="dislikeAnswer(index)"
                    v-show="item.isDisliked"
                    class="share-icon"
                    :src="steping"
                    mode="aspectFit"
                  ></image>
                  <image
                    v-show="!item.chatDetail.answer.assist && typeShow"
                    class="share-icon"
                    :src="copySrc"
                    mode="aspectFit"
                    @click="copyAnswer(item.chatDetail.answer.text)"
                  ></image>
                  <image
                    v-show="isSSEActive == false"
                    class="share-icon"
                    :src="rebuildSrc"
                    mode="aspectFit"
                    @click="reanswer"
                  ></image>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view
        class="scroll-wrapper-bottom"
        id="scroll-wrapper-bot"
        style="height: 20rpx"
      ></view>
    </scroll-view>
    <view v-show="showInput">
      <view class="networkBox">
        <textarea
          auto-height
          v-model="newMessage"
          placeholder="输入你的问题或需求"
          placeholder-style="color: #C6C6C6;"
          maxlength="100"
          confirm-type="send"
          @confirm="sendChange"
          :show-confirm-bar="false"
          style="
            width: 100%;
            margin: 0 10rpx;
            height: 40rpx;
            padding: 20rpx;
            border-radius: 20rpx;
            background-color: #f3f4f6;
          "
        ></textarea>
      </view>
      <view class="bottom-area">
        <!-- <image class="chat" :src="chatSrc" mode='aspectFit'></image> -->
        <view
          @click="change"
          :class="networkChange == true ? 'networking' : 'network'"
        >
          <img
            v-show="!networkChange"
            class="icon"
            src="@/static/video/network.png"
            alt=""
          />
          <img
            v-show="networkChange"
            class="icon"
            src="@/static/video/networking.png"
            alt=""
          />
          <span>联网搜索</span>
        </view>
        <view
          style="margin-left: 20px"
          @click="changeMute"
          :class="muteChange == true ? 'networking' : 'network'"
        >
          <img
            v-show="!muteChange"
            class="icon"
            src="@/static/video/nomute.png"
            alt=""
          />
          <img
            v-show="muteChange"
            class="icon"
            src="@/static/video/mute.png"
            alt=""
          />
          <span>静音</span>
        </view>
        <button
          class="send"
          type="primary"
          size="mini"
          @keyup.enter="sendChange"
          @click="sendChange"
        >
          发送
        </button>
        <view v-show="isSSEActive == true" class="stop_box">
          <view class="stop" @click="stop">
            <image
              class="stop_icon"
              src="@/static/images/tingzhi.png"
              mode=""
            ></image>
            <text>停止生成</text>
          </view>
        </view>
        <image
          @click="chooseFile()"
          class="addImg"
          src="@/static/images/addImg.png"
          mode="aspectFit"
        ></image>
      </view>
    </view>
    <view v-show="!showInput">
      <image
        style="
          width: 100%;
          height: 200rpx;
          position: absolute;
          bottom: -5px;
          z-index: 9999;
        "
        :src="bottomSrc"
        mode="aspectFit"
        @click="changeType"
      ></image>
    </view>

    <uni-popup ref="message" type="message">
      <uni-popup-message
        :type="msgType"
        :message="messageText"
        :duration="2000"
      ></uni-popup-message>
    </uni-popup>
    <!-- 真实渲染到页面视频 -->
    <!-- <view class="my-canvas" @click="aiPlay">

    </view> -->
    <img
      v-if="showInput"
      @click="aiPlay"
      class="HumanImg"
      width="100px"
      :style="{ left: left + 'px', bottom: bottom + 'px' }"
      @touchstart="onTouchStart"
      @touchmove="onTouchMove"
      @touchend="onTouchEnd"
      :src="digitalHumanImg"
      mode="aspectFit"
      alt=""
    />
    <audio src="" style="display: none"></audio>
  </view>
</template>
<script>
// import WebSocketService from '@/utils/websocketHuman'
import {
  SrsRtcPublisherAsync,
  SrsRtcPlayerAsync,
  SrsRtcWhipWhepAsync,
  SrsRtcFormatSenders,
} from "@/utils/srs.sdk.js";
import config from "@/config";
import VueMarkdown from "vue-markdown";
import { uploadQuestionImage } from "../../api/ai/ai.js";
import { connectToSSE, getAudio } from "../../api/ai/sse.js";
import { onReady } from "@dcloudio/uni-app";
import { debounce, throttle } from "lodash";
export default {
  data() {
    return {
      muteChange: false,
      networkChange: false,
      // GIF 图片的初始位置
      left: 10,
      bottom: 120,
      // 记录触摸开始时的坐标
      startX: 0,
      startY: 0,
      // 记录图片的初始位置
      initialLeft: 0,
      initialTop: 0,
      videoHtml: "",
      bgSrc: require("../../static/images/aiPage/bg.png"),
      iconSrc: require("../../static/images/aiPage/icon3.png"),
      robotSrc: require("../../static/images/aiPage/robot.png"),
      titleSrc: require("../../static/images/aiPage/wyws.png"),
      textBgSrc: require("../../static/images/aiPage/text-bg.png"),
      copySrc: require("../../static/images/aiPage/copy.png"),
      chatSrc: require("../../static/images/xq_header.jpg"),
      bottomSrc: require("../../static/images/aiPage/bottom.png"),
      rebuildSrc: require("../../static/images/aiPage/rebuild.png"),
      step: require("../../static/images/aiPage/step.png"),
      steping: require("../../static/images/aiPage/steping.png"),
      assist: require("../../static/images/aiPage/assist.png"),
      assisting: require("../../static/images/aiPage/assisting.png"),
      exampleList: [],
      typeShow: true,
      showAsk: false, // 是否提问
      msgType: "",
      messageText: "",
      newMessage: "",
      scrollIntoId: "",
      controller: null,
      showOptions: false,
      hasEcharts: true,
      conversationId: "",
      isSSEActive: false,
      produceing: false, // 是否正在生成
      currentSend: {}, // 当前被记录到对话接口的数据
      quesAndAns: {
        hisChatId: null,
        data: [
          // {
          //   isLiked: false,
          //   isDisliked: false,
          //   chatDetail: {
          //     question: {
          //       text: "这是一个非常长的句子，完整阅读这个句子会得到一些启示，具体来说这个启示是，时间会被浪费，但是请你给我一个柱状图。",
          //       file_url: "",
          //     },
          //     answer: {
          //       text: "这也是一个非常长的句子，完整阅读这个句子会得到一些启示，具体来说这个启示是，时间会被浪费，这是你的一个柱状图。",
          //       file_url: "",
          //       chartData: {
          //         type: "barAndCube",
          //       },
          //       produceing: false,
          //     },
          //   },
          // },
        ],
      },
      token: "",
      extensions: ["jpg", "png", "HEIC", "heic"],
      randomTenDigitNumber: 0,
      imgBaseUrl: config.baseUrl + "/boot/",
      allText: "",
      thinkshow: true,
      Obj: "",
      // afterVideo: require("../../static/video/after.mp4"),
      // startVideo: require("../../static/video/start.mp4"),
      defaultImg: require("../../static/video/default.gif"),
      audioStartImg: require("../../static/video/audio-start.gif"),
      welcomeImg: require("../../static/video/welcome.gif"),
      digitalHumanImg: "",
      videoSrc: "",
      audioStart: true,
      audioUrls: [],
      stepNow: 1,
      text: "",
      hyText: "你好，我是嘉有小乔，有什么可以帮助你的吗",
      index: 0,
      control: true,
      stepData: 0,
      sentences: [],
      hyType: true,
      showInput: false,
      isAutoScrolling: true,
      scrollTop: 0,
      autoScrollInterval: null,
      lastScrollTop: 0,
      //   04-21更新
      welcomeAudioUrl: "",
      audioEnded: false, // 正常播放完毕
    };
  },
  components: {
    VueMarkdown,
  },
  mounted() {
    this.gettoken();
    this.currentTime = this.getCurrentTime();
    // 04-21更新
    this.getWelcomeAudioUrl();
    // this.videoPlayFn(this.afterVideo);
    this.digitalHumanImg = this.defaultImg;
    this.audioContext = uni.createInnerAudioContext();
    this.audioContext.autoplay = true;
    this.audioContext.volume = 1;
    // 监听音频播放完成事件
    this.audioContext.onEnded(() => {
      console.log(this.audioUrls, "audioUrls");
      console.log(this.stepNow, "stepnow");
      if (this.hyType) {
        console.log("播放完成3");
        // URL.revokeObjectURL(audioUrl);
        // this.videoPlayFn(this.afterVideo);
        this.digitalHumanImg = this.defaultImg;
        this.audioUrls = [];
        this.hyType = false;
        return;
      } else if (!this.audioUrls[this.stepNow - 1]) {
        this.isSSEActive = false;
        console.log("播放完成2");
        // URL.revokeObjectURL(audioUrl);
        // this.videoPlayFn(this.afterVideo);
        this.digitalHumanImg = this.defaultImg;
        //   04-21更新
        this.audioEnded = true;
      } else {
        console.log("播放完成1");
        // 否则当上一条播放完毕后，继续执行playaudio方法播放下一条
        this.playaudio();
      }
    });
    // this.getAudioAnswer(this.hyText, 1);
  },
  onReady() {
    // 页面初次渲染完成后执行模拟点击操作
    this.simulateButtonClick();
  },
  watch: {
    quesAndAns() {
      this.startAutoScroll();
    },
    //   04-21更新
    audioUrls() {
      if (this.audioEnded) {
        // 考虑极限状态，上一条播完，下一条音频还未返回
        this.playaudio();
        this.isSSEActive = true; //不清楚这个状态是干嘛的，为真，才继续回调音频接口
        this.filterAnswer(this.stepNow);
        // this.digitalHumanImg = this.audioStartImg; //换图
      }
    },
  },
  methods: {
    change() {
      this.networkChange = !this.networkChange;
    },
    changeMute() {
      this.muteChange = !this.muteChange;
      if (this.audioContext.volume === 0) {
        this.audioContext.volume = 1;
      } else {
        this.audioContext.volume = 0;
      }
      console.log(this.audioContext.volume);
    },
    // 触摸开始事件处理函数
    onTouchStart(e) {
      // 记录触摸点的初始坐标
      this.startX = e.changedTouches[0].clientX;
      this.startY = e.changedTouches[0].clientY;
      // 记录图片的初始位置
      this.initialLeft = this.left;
      this.initialBottom = this.bottom; // 修改这里
    },
    // 触摸移动事件处理函数
    onTouchMove(e) {
      // 计算触摸点的偏移量
      const offsetX = e.changedTouches[0].clientX - this.startX;
      const offsetY = e.changedTouches[0].clientY - this.startY;
      // 更新图片的位置
      this.left = this.initialLeft + offsetX;
      this.bottom = this.initialBottom - offsetY; // 修改这里
    }, // 触摸结束事件处理函数
    onTouchEnd() {
      // 触摸结束，不做额外处理
    },
    //   04-21更新
    aiPlay() {
      if (this.digitalHumanImg == this.welcomeImg) {
        return;
      }
      if (this.isSSEActive) {
        this.msgType = "error";
        this.messageText = "当前对话生成中，请等待！";
        this.$refs.message.open();
        return;
      }
      if (this.welcomeAudioUrl) {
        this.digitalHumanImg = this.welcomeImg;
        this.audioContext.src = this.welcomeAudioUrl;
        this.audioContext.play();
      } else {
        setTimeout(() => {
          this.aiPlay();
        }, 100);
      }
    },
    //   04-21更新
    // 获取欢迎语音
    async getWelcomeAudioUrl() {
      const msg = this.hyText;
      const response = await fetch("/tts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          msg,
        }),
      });
      // 获取音频数据并转为 Blob
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      this.welcomeAudioUrl = audioUrl;
    },
    handleButtonClick() {
      console.log("按钮被点击了");
    },
    simulateButtonClick() {
      // 获取按钮元素
      const button = document.getElementById("myButton");
      if (button) {
        // 创建一个点击事件
        const clickEvent = new Event("click");
        // 触发按钮的点击事件
        button.dispatchEvent(clickEvent);
      }
    },
    likeAnswer(index) {
      const item = this.quesAndAns.data[index];
      if (!item.isLiked) {
        this.$nextTick(() => {
          item.isLiked = true;
          item.isDisliked = false;
        });
      }
    },
    dislikeAnswer(index) {
      const item = this.quesAndAns.data[index];
      if (!item.isDisliked) {
        this.$nextTick(() => {
          item.isDisliked = true;
          item.isLiked = false;
        });
      }
    },
    // 重新生成
    reanswer() {
      // 记录上次提问的问题
      let l = this.quesAndAns.data.length;
      this.newMessage = this.quesAndAns.data[l - 1].chatDetail.question.text;
      this.quesAndAns.data.pop();
      if (this.audioContext) {
        this.audioContext.pause();
        this.audioContext.currentTime = 0; // 可选：重置播放位置到开始
      }
      this.sendChange();
    },
    // 兜底方案
    // videoPlayFn(src) {
    //   setTimeout(() => {
    //     let videoPlayer = document.getElementsByTagName("video")[0];
    //     videoPlayer.src = src;
    //     videoPlayer.play();
    //     this.removeParams();
    //   }, 1000);
    // },
    // 请求视频流
    videoPlay() {
      setTimeout(() => {
        let videoPlayer = document.getElementsByTagName("video")[0];
        // let videoPlayer = document.getElementById("videoId");
        this.rtcPlayer = SrsRtcWhipWhepAsync();
        this.rtcPlayer.play(
          "http://************:1985/rtc/v1/whep/?app=live&stream=livestream&eip=************"
        );
        videoPlayer.srcObject = this.rtcPlayer.stream;
        this.removeParams();
      }, 1000);
    },
    removeParams() {
      const video = document.getElementsByTagName("video")[0]; //this.$refs.video;
      const canvas = document.getElementsByTagName("canvas")[0]; //this.$refs.canvas;
      video.addEventListener("loadeddata", () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
      });
      video.addEventListener("play", () => {
        setTimeout(() => {
          this.removeGreenStains(video, canvas, canvas.getContext("2d"));
        }, 100);
      });
    },
    // 移除绿幕
    removeGreenStains(video, canvas, context) {
      const draw = () => {
        let tWid = window.devicePixelRatio; //获取设备像素比
        context.drawImage(
          video,
          0,
          0,
          canvas.width / tWid,
          canvas.height / tWid
        );
        const imageData = context.getImageData(
          0,
          0,
          canvas.width,
          canvas.height
        );
        const data = imageData.data;
        for (let i = 0; i < data.length; i += 4) {
          if (
            data[i + 1] > data[i + 2] &&
            data[i + 1] > data[i + 0] &&
            data[i + 1] > 60
          ) {
            data[i + 3] = 0;
          }
        }
        context.putImageData(imageData, 0, 0);
        requestAnimationFrame(draw);
      };
      draw();
    },
    remove() {
      const video = this.$refs.video;
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      video.addEventListener("loadeddata", () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        this.drawFrame(video, ctx, canvas);
      });
      // 如果视频已经加载完成，直接开始处理
      if (video.readyState >= 2) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        this.drawFrame(video, ctx, canvas);
      }
    },
    drawFrame(video, ctx, canvas) {
      if (video.paused || video.ended) return;
      // 绘制当前帧到Canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      // 获取图像数据
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;
      // 遍历每个像素
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const a = data[i + 3];
        // 检查是否为绿色（根据实际情况调整阈值）
        if (g > 100 && r < 100 && b < 100) {
          data[i + 3] = 0; // 设置Alpha为0，使像素透明
        }
      }
      // 将处理后的图像数据放回Canvas
      ctx.putImageData(imageData, 0, 0);
      // 递归调用，实现实时处理
      requestAnimationFrame(() => this.drawFrame(video, ctx, canvas));
    },
    // 文件上传
    chooseFile() {
      // if (this.newMessage === '') {
      // 	this.msgType = 'error';
      // 	this.messageText = '请输入内容后，再上传图片！';
      // 	this.$refs.message.open();
      // 	return;
      // }
      uni.chooseFile({
        count: 1,
        type: "file",
        extensions: this.extensions,
        success: (res) => {
          const str = res.tempFiles[0].name.substring(
            0,
            res.tempFiles[0].name.lastIndexOf(".")
          );
          const fileType = res.tempFiles[0].name.substring(
            res.tempFiles[0].name.lastIndexOf(".") + 1
          );
          const min = 1000000000; // 10^10
          const max = 9999999999; // 10^10 - 1
          this.randomTenDigitNumber =
            Math.floor(Math.random() * (max - min + 1)) + min;
          if (!this.extensions.includes(fileType)) {
            uni.showToast({
              icon: "none",
              title: "文件格式不正确",
            });
            return;
          }
          uni.uploadFile({
            header: {
              Authorization: uni.getStorageSync("ACCESS_TOKEN"),
            },
            url:
              config.baseUrl + "/boot" + "/common/miniapp/uploadQuestionImage",
            filePath: res.tempFiles[0].path,
            name: "file",
            formData: {
              biz: `record/${this.randomTenDigitNumber}/chat`,
              fileFormat: fileType,
              fileName: str,
              fileSize: res.tempFiles[0].size,
            },
            success: (uploadFileRes) => {
              if (uploadFileRes.statusCode !== 413) {
                this.filePath = JSON.parse(uploadFileRes.data).result.filePath;
                this.SSEImage = this.imgBaseUrl + this.filePath;
                // this.sendingQuestionsByImg()
                this.currentSend = {}; // 清空上一次的会话
                this.showAsk = true;
                this.currentSend = {
                  hisChatId: this.quesAndAns.hisChatId,
                  chatType: "0",
                  chatMode: "0",
                  isLiked: false,
                  isDisliked: false,
                  chatDetail: {
                    question: {
                      imageUrl: this.SSEImage,
                      file_url: "",
                    },
                    answer: {
                      text: "",
                      file_url: "",
                      produceing: true,
                      isShow: 1,
                    },
                  },
                };
                this.quesAndAns.data.push(this.currentSend);
              } else {
                uni.showToast({
                  icon: "none",
                  title: "上传文件过大",
                });
              }
            },
            fail: (err) => {
              uni.showToast({
                icon: "none",
                title: "上传失败",
              });
            },
          });
        },
      });
    },
    getCurrentTime() {
      const now = new Date();
      const hours = now.getHours();
      const minutes = now.getMinutes();
      return `${hours < 10 ? `0${hours}` : hours}:${
        minutes < 10 ? `0${minutes}` : minutes
      }`;
    },
    startAutoScroll() {
      this.stopAutoScroll();
      this.autoScrollInterval = setInterval(() => {
        if (this.isAutoScrolling) {
          this.scrollTop += 70; // 每次滚动 50px
        }
      }, 1000); // 每秒滚动一次
    },
    stopAutoScroll() {
      // console.log("stopAutoScroll");
      if (this.autoScrollInterval) {
        clearInterval(this.autoScrollInterval);
        this.autoScrollInterval = null;
      }
    },
    handleScroll(e) {
      const currentScrollTop = e.detail.scrollTop;
      if (currentScrollTop < this.lastScrollTop) {
        // 用户往上滑动，取消自动滚动
        this.isAutoScrolling = false;
        this.stopAutoScroll();
        // console.log("stopAutoScroll");
      }
      if (currentScrollTop >= this.lastScrollTop) {
        // 滑动到底部，继续滚动
        this.isAutoScrolling = true;
        this.startAutoScroll();
        // console.log("startAutoScroll");
      }
      this.lastScrollTop = currentScrollTop;
      this.scrollTop = currentScrollTop;
    },
    newController() {
      if (this.controller) {
        this.controller.abort();
      }
      this.controller = new AbortController();
    },
    refreshSrc() {
      this.getExample();
    },
    getExample() {
      uni.request({
        url: "/knowledgeBase/frequentQuestion/list", // 注意这里使用了代理前缀 /chat
        method: "GET",
        header: {
          Authorization: "Bearer " + this.token,
          "content-type": "application/json", // 请求头
        },
        success: (res) => {
          this.exampleList = res.data.rows;
        },
        fail: (err) => {
          console.error(err);
        },
      });
    },
    gettoken() {
      const baseUrl = config.baseUrl;
      const url = baseUrl + "/boot/common/miniapp/getInspurToken";
      uni.request({
        url: url, // 你的API地址
        method: "GET",
        header: {
          "content-type": "application/json", // 请求头
        },
        success: (res) => {
          let mainToken = res.data.result;
          const token = mainToken || "";
          this.token = token;
          this.getExample();
        },
        fail: (err) => {
          console.error(err); // 请求失败的错误信息
        },
      });
    },
    startSSEImg(data, message) {
      if (this.isSSEActive) return;
      this.isSSEActive = true;
      this.produceing = true;
      const requestData = {
        query: message,
        image_url: data,
        knowledge_base_name: "admin嘉鱼知识库",
        model_name: "openai-api",
        stream: true,
        search_doc: false,
      };
      this.newController();
      // let url =  "/chat/llm_open_chat"
      let url = "llm-api/chat/knowledge_base_chat";
      // let url = "http://60.165.149.155:30089/llm-api/chat/knowledge_base_chat"
      connectToSSE(
        this.controller,
        url,
        this.token,
        requestData,
        this.handleMessage,
        this.handleError,
        this.handleComplete,
        this.handleOpen
      );
    },
    startSSE(data) {
      if (this.isSSEActive) return;
      this.isSSEActive = true;
      this.produceing = true;
      const requestData = {
        query: data,
        knowledge_base_name: "admin嘉鱼知识库",
        model_name: "Deep-Seek",
        stream: "true",
        search_doc: "false",
        temperature: 0.5,
        top_k: 3,
        prompt_name: "default",
        web_search: this.networkChange,
        history: [],
      };
      this.newController();
      // let url =  "/chat/llm_open_chat"
      let url = "llm-api/chat/knowledge_base_chat";
      // let url = "http://60.165.149.155:30089/llm-api/chat/knowledge_base_chat"
      connectToSSE(
        this.controller,
        url,
        this.token,
        requestData,
        this.handleMessage,
        this.handleError,
        this.handleComplete,
        this.handleOpen
      );
    },
    ok() {
      console.log(1111);
    },
    handleMessage(data) {
      let l = this.quesAndAns.data.length;
      let an = "\n";
      if (data !== "") {
        an = JSON.parse(data).answer;
        this.allText += an;
        let arr = this.allText.split("</think>");
        if (arr.length > 1) {
          this.quesAndAns.data[l - 1].chatDetail.answer.think = true;
          // this.quesAndAns.data[l - 1].chatDetail.answer.thinkshow = false;
          this.quesAndAns.data[l - 1].chatDetail.answer.thinkText = arr[0];
          this.text = arr[1];
          // let realAnswerLength = this.keepChineseCharacters(this.text).length;
          // if (realAnswerLength >= 50 && this.audioStart) {
          //   this.filterAnswer(1);
          //   this.audioStart = false;
          // }

          // 分割句子
          this.sentences = this.text.split("。");
          // 遍历每个句子并计算长度
          if (this.sentences.length > 1) {
            if (this.audioStart) {
              this.filterAnswer(1);
              this.audioStart = false;
            }
          }
          setTimeout(() => {
            if (this.control) {
              this.$nextTick(() => {
                this.typingInterval = setInterval(this.type, 150);
                this.control = false;
              });
            }
            // console.log("开始播放正式答案");
            //语音接口相关
          }, 4000);
        } else {
          //   this.quesAndAns.data[l - 1].chatDetail.answer.think = false;
          //   this.quesAndAns.data[l - 1].chatDetail.answer.text = arr[0];
          this.quesAndAns.data[l - 1].chatDetail.answer.think = true;
          this.quesAndAns.data[l - 1].chatDetail.answer.thinkshow = false;
          this.quesAndAns.data[l - 1].chatDetail.answer.thinkText = arr[0];
        }
      } else {
        console.log("出现空字符串", data);
      }
      // this.closeCallback();
    },
    type() {
      let l = this.quesAndAns.data.length;
      if (this.index < this.text.length) {
        this.quesAndAns.data[l - 1].chatDetail.answer.text +=
          this.text[this.index++];
      } else {
        this.index = 0;
        this.text = "";
        clearInterval(this.typingInterval);
      }
    },
    handleError(err) {
      console.error("Error:", err);
      this.isSSEActive = false;

      if (err.status >= 500) {
        this.controller.abort();
      }
    },
    stop() {
      if (this.controller) {
        this.controller.abort();
      }
      this.isSSEActive = false;
      clearInterval(this.typingInterval);
      console.log("停止");
      console.log(this.typingInterval, "98888");
      this.digitalHumanImg = this.defaultImg;
      this.index = 0;
      this.text = "";
      this.audioUrls = [];
      this.closeCallback();
      if (this.audioContext) {
        this.audioContext.pause();
        this.audioContext.currentTime = 0; // 可选：重置播放位置到开始
      }
    },
    handleComplete() {
      console.log("SSE connection completed");
      this.closeCallback();
      let l = this.quesAndAns.data.length;
      this.control = true;
    },
    handleOpen(response) {
      if (!response.ok) {
        this.handleError({
          status: response.status,
          statusText: response.statusText,
        });
      } else {
        console.log("SSE connection opened with status:", response.status);
      }
    },
    sendChange() {
      if (this.digitalHumanImg == this.welcomeImg) {
        this.msgType = "error";
        this.messageText = "当前正在对话，请等待！";
        this.$refs.message.open();
        return;
      }
      // produceing为判断是否正在生成答案标志，如为true，则阻断操作，重新生成除外。
      if (this.produceing || this.isSSEActive) {
        this.msgType = "error";
        this.messageText = "当前对话生成中，请等待！";
        this.$refs.message.open();
        return;
      }
      if (!this.SSEImage) {
        if (this.newMessage === "") {
          this.msgType = "error";
          this.messageText = "请输入内容后，再次发送！";
          this.$refs.message.open();
          return;
        }
      }
      this.allText = "";

      this.audioUrls = [];
      this.stepNow = 1;
      //   04-21更新
      this.sentences = [];
      if (this.SSEImage) {
        this.sendingQuestionsByImg();
      } else {
        this.sendingQuestions();
      }
    },
    async sendingQuestionsByImg() {
      // produceing为判断是否正在生成答案标志，如为true，则阻断操作，重新生成除外。
      // if (this.produceing || this.isSSEActive) {
      //   this.msgType = "error";
      //   this.messageText = "当前对话生成中，请等待！";
      //   this.$refs.message.open();
      //   return;
      // }

      this.newMessage = this.newMessage.trim(); // 去掉首尾换行符
      this.currentSend = {}; // 清空上一次的会话
      this.showAsk = true;
      this.produceing = true;
      this.currentSend = {
        hisChatId: this.quesAndAns.hisChatId,
        chatType: "0",
        chatMode: "0",
        chatDetail: {
          question: {
            text: this.newMessage.replace(/\n/g, "<br>"),
            file_url: "",
          },
          answer: {
            text: "",
            file_url: "",
            produceing: true,
          },
        },
      };
      this.quesAndAns.data.push(this.currentSend);
      const data = this.newMessage;
      const dataImg = this.SSEImage;
      this.newMessage = "";
      this.SSEImage = "";
      this.startSSEImg(dataImg, data);
      this.audioStart = true;
    },
    async sendingQuestions() {
      this.newMessage = this.newMessage.trim(); // 去掉首尾换行符

      this.currentSend = {}; // 清空上一次的会话
      this.showAsk = true;
      this.produceing = true;
      this.currentSend = {
        hisChatId: this.quesAndAns.hisChatId,
        chatType: "0",
        chatMode: "0",
        isDisliked: false,
        isLiked: false,
        chatDetail: {
          question: {
            text: this.newMessage.replace(/\n/g, "<br>"),
            file_url: "",
          },
          answer: {
            text: "",
            file_url: "",
            produceing: true,
            thinkText: "",
          },
        },
      };
      this.quesAndAns.data.push(this.currentSend);
      const data = this.newMessage;
      this.startAutoScroll();
      this.newMessage = "";
      this.startSSE(data);
      this.audioStart = true;
    },
    handleClick() {},
    goBack() {
      uni.navigateBack();
    },
    toggleOptions() {
      this.showOptions = !this.showOptions;
    },
    //提问
    information(data) {
      if (this.digitalHumanImg == this.welcomeImg) {
        this.msgType = "error";
        this.messageText = "当前正在对话，请等待！";
        this.$refs.message.open();
        return;
      }
      this.newMessage = data;
      this.sendingQuestions();
    },
    copyAnswer(value) {
      const textToCopy = value;
      uni.setClipboardData({
        data: textToCopy,
        success: () => {
          uni.showToast({
            title: "复制成功",
            icon: "success",
          });
        },
        fail: (err) => {
          uni.showToast({
            title: "复制失败",
            icon: "none",
          });
          console.error(err);
        },
      });
    },
    // 停止回答时触发
    async closeCallback() {
      const _this = this;
      this.produceing = false;
      let l = this.quesAndAns.data.length;
      this.quesAndAns.data[l - 1].chatDetail.answer.produceing = false;
    },
    close(show, index) {
      this.quesAndAns.data[index].chatDetail.answer.thinkshow =
        !this.quesAndAns.data[index].chatDetail.answer.thinkshow;
      this.$forceUpdate(); // 强制组件重新渲染
    },
    // 请求音频
    async getAudioAnswer(msg, step) {
      if (msg.length > 0) {
        const response = await fetch("/tts", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            msg,
          }),
        });
        if (response.ok) {
          // 状态码在 200-299 之间时为 true
          // 获取音频数据并转为 Blob
          const audioBlob = await response.blob();
          const audioUrl = URL.createObjectURL(audioBlob);
          this.audioUrls.push(audioUrl);
          // 设置音频源并播放 同时切换视频
          if (step == 1) {
            // this.videoPlayFn(this.startVideo);
            // this.digitalHumanImg = this.audioStartImg;
            this.playaudio();
          }
          if (msg.length > 0 && this.isSSEActive) {
            step += 1;
            this.stepData + 1;
            this.filterAnswer(step);
          }
          // 继续处理音频 Blob
        } else {
          console.error(`请求失败，状态码: ${response.status}`);
          // 处理错误，例如显示错误信息给用户
        }
      }
    },
    // 播放语音
    playaudio() {
      console.log(this.stepNow, "11111111");
      console.log(this.audioUrls[this.stepNow - 1], "!!!!!!!!!!!!");
      if (this.audioUrls[this.stepNow - 1]) {
        this.audioContext.src = this.audioUrls[this.stepNow - 1];
        this.audioContext.play();
        this.stepNow += 1;
        this.digitalHumanImg = this.audioStartImg;
        // 04-21更新
        this.audioEnded = false;
      }
    },
    // 截取答案请求语音接口
    filterAnswer(step) {
      // let l = this.quesAndAns.data.length;
      // let msg = this.keepChineseCharacters(this.text);
      // let nowStepMsg = msg.slice((step - 1) * 50, step * 50);
      if (step <= this.sentences.length) {
        let nowStepMsg = this.keepChineseCharacters(this.sentences[step - 1]);
        console.log(nowStepMsg, "nowStepMsg");
        this.getAudioAnswer(nowStepMsg, step);
      } else {
        return;
      }
    },
    //过滤
    keepChineseCharacters(str) {
      return str.replace(/[\n#*|-]/g, "  ");
    },
    // 切换
    async changeType() {
      this.showInput = true;
      this.aiPlay();
    },
  },
};
</script>

<style lang="scss">
uni-page-body {
  height: 50%;
}

page {
  // height: 90%;
  height: 100%;
}

.wrapper {
  display: flex;
  // background-color: #FFF;
  background-color: rgba(244, 245, 251, 1);
  flex-direction: column;
  width: 100%;
  height: 100%;
  // background-color: red;
  overflow: hidden;
  // padding-bottom: 50rpx;
  position: relative;
  .messages-container {
    // padding: 0rpx 30rpx;
    background-color: rgba(244, 245, 251, 1);
    overflow-y: auto;
    // height: 50rpx;
    flex: 1;
    /* 让.messages-container占据剩余空间 */
    overflow-y: auto;
    /* 允许垂直滚动 */

    .bg-image {
      width: 750rpx;
      height: 408rpx;
      position: absolute;
      top: 0;
      left: 0;
    }

    .top-content {
      display: flex;
      align-items: center;
      height: 146rpx;

      .title-box {
        margin-top: 117rpx;

        .icon {
          width: 35rpx;
          // height: 35rpx;
          margin-left: 33rpx;
        }

        .robot {
          width: 149rpx;
          // height: 146rpx;
          margin-left: 157rpx;
        }

        .title {
          width: 139rpx;
          // height: 35rpx;
          margin-left: -36rpx;
        }
      }
    }
    .text-content_show {
      margin-top: 45rpx !important;
      transition: all 0.8s linear 0s;
    }
    .text-content {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 229rpx;
      overflow: hidden;
      margin: 345rpx 0 0 16rpx;

      .text-bg {
        width: 719rpx;
        height: 229rpx;
        position: absolute;
        top: 0;
        left: 0;
      }

      .text-box {
        position: relative;
        margin: 0 56rpx 0 48rpx;

        .title {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 32rpx;
          color: #5671f4;
          margin-bottom: 4rpx;
        }

        .text {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #666666;
          line-height: 1.8;
          margin-top: 18rpx;
        }
      }
    }

    .grid-body {
      .example {
        display: flex;
        flex-direction: row;
        margin: 10rpx 10rpx 15rpx 14rpx;

        .left-text {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 30rpx;
          color: #333333;
          padding: 20rpx 20rpx;
        }

        .refresh {
          margin-left: 389rpx;
          padding: 20rpx 20rpx;

          & > image {
            width: 21rpx;
            height: 21rpx;
            margin-right: 13rpx;
          }

          .right-text {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #999999;
          }
        }
      }

      .grid-item-box {
        display: flex;
        align-items: center;
        width: 690rpx;
        // height: 140rpx;
        margin: 0 30rpx 25rpx 30rpx;
        padding: 20rpx 30rpx;
        line-height: 45rpx;
        background-color: white;
        border-radius: 10px;

        .grid-item {
          .icon {
            margin: 0 11rpx -6rpx 0;

            &:nth-of-type(1) {
              width: 35rpx;
              height: 35rpx;
            }

            &:nth-of-type(2) {
              width: 32rpx;
              height: 35rpx;
            }

            &:nth-of-type(3) {
              width: 32rpx;
              height: 35rpx;
            }
          }

          .text1 {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 26rpx;
            color: #333333;
          }

          .text2 {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 26rpx;
            color: #666666;
          }
        }
      }
    }

    .time {
      width: 750rpx;
      height: 18rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 18rpx;
      text-align: center;
      margin: 20rpx 0;
    }

    .askoranswer {
      margin: 0 auto;
      margin-top: 10rpx;
      margin-bottom: 10rpx;
      max-width: 77vw;
      padding: 10rpx 20rpx;
      position: relative;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 58rpx;
      display: inline-block;
      word-break: break-word;
      /* 使长文本自动换行 */
    }

    .ask {
      color: #fff;
      background-color: #5671f4;
      border-radius: 20rpx 0rpx 20rpx 20rpx;
      margin-left: 60rpx;
      margin-right: 28rpx;
    }

    .answer {
      background: #fff;
      border-radius: 0rpx 20rpx 20rpx 20rpx;
      margin-left: 28rpx;
      // margin-right: 60rpx;
    }

    .askoranswer-c {
      display: flex;
      align-items: center;
      // padding: 3rpx 15rpx;
      line-height: 53rpx;
    }

    .img {
      width: 120rpx;
      height: 120rpx;
    }

    .icon-group {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      margin: 0rpx 2rpan 3rpx 2rpx;
      width: 100%;

      .answer-icon {
        width: 30rpx;
        height: 29rpx;
        margin-right: 46rpx;
      }

      .copy-icon {
        width: 28rpx;
        height: 31rpx;
        margin-right: 46rpx;
      }

      .share-icon {
        width: 34rpx;
        height: 30rpx;
        margin-left: 10rpx;
      }
    }

    .message {
      margin-top: 20rpx;
      display: flex;
      justify-content: flex-end;
      width: 100%;
      /* 限制气泡最大宽度 */
      gap: 20rpx;

      image {
        height: 75rpx;
        width: 75rpx;
        border-radius: 75rpx;
        overflow-clip-margin: content-box;
        overflow: clip;
      }

      &.server {
        flex-direction: row-reverse;

        .echarts-message {
          display: flex;
          flex-direction: column;
          transition: all 0.3s ease-in-out;
          width: 73%;
          max-width: 73%;
          padding: 20rpx;
          color: #333;
          font-size: 30rpx;
          background-color: rgba(255, 255, 255, 0.9);
          border-radius: 0rpx 20rpx 20rpx 20rpx;
          margin-left: 28rpx;
          margin-right: 45rpx;
          gap: 20rpx;

          .message-style {
            width: 100%;
            word-break: break-word;
            /* 使长文本自动换行 */
          }

          .echarts {
            width: 100%;
            border-bottom: 2rpx solid #f5f5f5;
          }
        }
      }

      &.client {
        .message-style {
          transition: all 0.3s ease-in-out;
          max-width: 73%;
          padding: 20rpx;
          // color: #333;
          color: #fff;
          font-size: 30rpx;
          // background-color: rgba(40, 107, 255, 0.2);
          border-radius: 20rpx 0rpx 20rpx 20rpx;
          margin-left: 45rpx;
          margin-right: 28rpx;
          background-color: #2d65f7;
          word-break: break-word;
          /* 使长文本自动换行 */
        }
      }

      .icon-group {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        margin: 0rpx 2rpan 3rpx 2rpx;
        width: 100%;

        .answer-icon {
          width: 30rpx;
          height: 29rpx;
          margin-right: 46rpx;
        }

        .copy-icon {
          width: 28rpx;
          height: 31rpx;
          margin-right: 46rpx;
        }

        .share-icon {
          width: 34rpx;
          height: 30rpx;
        }
      }
    }
  }

  .bottom-area {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
    padding: 10rpx 30rpx;
    // .chat {
    // 	width: 50rpx;
    // 	height: 50rpx;
    // 	margin-left: 100rpx;
    // }

    .input-area {
      display: flex;
      padding: 0rpx 30rpx;
      align-items: center;
      // gap: 20rpx;
      // background-color: #ffffff;
      background-color: transparent;
      margin: 20rpx 0;
      // border-top: 1rpx solid #e0e0e0;

      textarea {
        flex: 1;
        // padding: 8px;
        border-radius: 30rpx;
        padding: 40rpx 40rpx;
        border: 2rpx solid #e5e5e5;
        font-size: 32rpx;
        // min-height: 70rpx;
        line-height: 32rpx;
        background-color: #fff;

        // box-shadow: rgba(0, 0, 0, 0.04) 0px 3px 5px;
        // box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px;
        // box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
        &:hover {
          // box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
          // box-shadow: rgba(0, 0, 0, 0.2) 0px 18px 50px -10px;
        }
      }

      .plus-button {
        background-color: white;
        height: 70rpx;
        width: 70rpx;
      }
    }

    .addImg {
      width: 50rpx;
      height: 50rpx;
      position: absolute;
      right: 6px;
    }

    .send {
      background: #3153f5;
      width: 102rpx;
      height: 59rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26rpx;
      color: #ffffff;
      line-height: 59rpx;
      text-align: center;
      padding: 0;
      position: absolute;
      right: 40px;
    }

    .stop_box {
      width: 100%;
      position: absolute;
      bottom: 200rpx;

      .stop {
        width: 218rpx;
        height: 64rpx;
        background: #ffffff;
        box-shadow: 0rpx 0rpx 9rpx 0rpx rgba(0, 0, 0, 0.1);
        border-radius: 32rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #444444;
        // line-height: 64rpx;
        display: flex;
        align-items: center;
        margin: 0 auto;
        padding: 0 30rpx 0 27rpx;
        justify-content: space-between;

        .stop_icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }

    .options {
      display: flex;
      height: 300rpx;
      background-color: #fff;
      padding: 10rpx;
      // box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
      transition: all 0.5s ease-in-out;
    }
  }
}

.robot-avatar {
  width: 66rpx;
  height: 66rpx;
  border-radius: 100%;
}

.think-btn {
  background: #e0e0e0;
  cursor: pointer;
  font-size: 13px;
  user-select: none;
  border-radius: 10px;
  justify-content: space-between;
  align-items: center;
  width: 125px;
  padding: 1px 14px;
  margin: 5px;
  display: flex;
}

.my-canvas {
  position: relative;
  width: 100%;
  height: 100vh;
}

.answer-icon {
  width: 30rpx;
  height: 29rpx;
  margin-right: 46rpx;
}

.HumanImg {
  position: absolute;
  width: 100px;
  height: 177px;
}
.networking {
  width: 200rpx;
  padding: 5px 10px;
  border-radius: 50px;
  border: 1px solid #e0e4ed;
  background: #dbeafe;
  color: #4d6bfe;
  display: flex;
  font-size: 22rpx;
  align-items: center;
}
.network {
  font-size: 22rpx;
  width: 200rpx;
  padding: 5px 10px;
  border-radius: 50px;
  border: 1px solid #e0e4ed;
  background: #fff;
  display: flex;
  align-items: center;
  color: #999;
}

.icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
}
.networkBox {
  padding: 10rpx 20rpx;
  background-color: #fff;
}
</style>
