<template>
	<view class="content" :style="{
      paddingTop:  Props.statusBarHeight + 'px'
    }">
		<navbar :Props="Props" :title='headtitle' @black="black"></navbar>
		<view class="top">
			<view class="Province">
				<view class="padding-bottom" v-for="(index, i) in province" :key="i" @click="changeProvince(i)">
					<view :class="nowProvince == i ? 'line-bottom' : ''" class="text">{{ index.producingName }}
					</view>
					<image v-show="nowProvince == i" class="line" src="@/static/images/map/line.png" mode="">
					</image>
				</view>
			</view>
			<view class="tip">
				<!-- <input class="uni-input" v-model="changeValue" focus placeholder="请输入关键字" /> -->
				<input class="uni-input" v-model="changeValue" focus @input="clearInput" placeholder="请输入关键字" />
				<view class="cacel_button" v-if="showClearIcon" @click="clearIcon">
					<image class="cacel" src="@/static/images/map/cancel.png" mode=""></image>
				</view>
				<view class="search_boxs" @click="nearby_search">
					<image style="z-index: 99999;" class="search" src="@/static/images/map/search.png" mode="">
					</image>
				</view>
			</view>
		</view>
		<view class="boxs">
			<view class="reports" v-for="(report, index) in reportList" :key="index" @click="detail(report)">
				<image class="record_bg" src="@/static/images/interaction/record_bg.png" mode=""></image>
				<view class="date">{{report.createTime}}</view>
				<view v-if="nowProvince == 0" class="text">{{report.title}}</view>
				<view v-if="nowProvince == 1" class="text">{{report.titile}}</view>
			</view>
			<uni-load-more iconType="circle" :status="loadMoreStatus" />
		</view>
		<uni-popup class="answerBoxs" ref="answer" type="center" mask-background-color="rgba(0,0,0,0.1)">
			<view class="answer-content">
				<scroll-view class="substance" scroll-y="true" style="height: 300rpx;">
					<rich-text :nodes="answer"></rich-text>
				</scroll-view>
				<view class="query" @click="aware">我知道了</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import navbar from '@/components/Navbar/index.vue'
	import {
		getRealTimeInfo
	} from "@/api/home/<USER>"
	import {
		getNoticelistByUser
	} from "@/api/home/<USER>"
	import {
		getDictData
	} from '@/api/system/dist.js'
	import {
		getMyNotifyMessagePage
	} from "@/api/system/message.js"
	export default {
		components: {
			navbar
		},
		options: {
			styleIsolation: 'shared'
		},
		data() {
			return {
				headtitle: '实时消息',
				Props: {
					imgShow: "", //不传参则默认隐藏状态(false),且使用默认背景色
					statusBarHeight: "", //导航高度(动态获取传参)
					bgColor: "", //导航栏背景色,不传参则默认#9CF
					capsuleTop: "", //胶囊顶部距离(动态获取传参)
					textColor: "", //导航标题字体颜色(不传默认#FFF)
					iconColor: "", //icon图标颜色(不传默认#FFF)
					blackShow: "", //是否显示返回字体及icon图标(不传默认显示true)
					backText: "", //默认字体(返回)
				},
				province: [{
						producingLocation: null,
						producingName: '通知公告',
						provinceCode: 37
					},
					{
						producingLocation: null,
						producingName: '消息提醒',
						provinceCode: 32
					}
				],
				nowProvince: 0,
				segmentedControlItems: [],
				currentsegmentedControl: 0,
				params: {
					pageNo: 1,
					pageSize: 2,
					total: 0
				},
				reportList: [],
				realTimeDictData: {},
				realTimeInfo: [],
				messageInfo: [],
				loadMoreStatus: 'more',
				changeValue: '',
				showClearIcon: false,
				answer: ''
			};
		},
		computed: {
			fontSizeSmall() {
				return this.$store.state.fontSizeSmall; // 从Vuex获取小号字体大小
			},
			fontSizeMedium() {
				return this.$store.state.fontSizeMedium; // 从Vuex获取中号字体大小
			},
			fontSizeLarge() {
				return this.$store.state.fontSizeLarge; // 从Vuex获取大号字体大小
			}
		},
		methods: {
			clearIcon: function() {
				this.changeValue = '';
				this.showClearIcon = false;
			},
			clearInput: function(event) {
				console.log(event, '11111')
				this.changeValue = event.detail.value;
				if (event.detail.value.length > 0) {
					this.showClearIcon = true;
				} else {
					this.showClearIcon = false;
				}
			},
			nearby_search() {
				this.getComplaintList()
			},
			aware() {
				this.$refs.answer.close();
			},
			detail(report) {
				if (this.nowProvince == 0) {
					uni.navigateTo({
						url: '/pages/Realtime/detail?content=' + report.content + '&wordUrl=' + report.wordUrl +
							'&title=' + report.title + '&createTime=' + report.createTime + '&releaseSource=' +
							report
							.releaseSource
					});
				} else {
					this.answer = report.msgContent
					this.$refs.answer.open();
				}
			},
			// 模块切换
			changeProvince(e) {
				this.nowProvince = e;
				this.changeValue = ''
				this.showClearIcon = false
				this.getComplaintList()
			},
			//点击切换实时信息tab
			onClickItem(e) {
				if (this.current !== e.currentIndex) {
					this.currentsegmentedControl = e.currentIndex
					this.params.pageNo = 1
					this.getCurrentRealTimeInfo()
				}
			},
			async getComplaintList() {
				const param = {
					keyWord: this.changeValue
				}
				const res = await getNoticelistByUser(param)
				console.log(res, '2222')
				if (this.nowProvince == 0) {
					this.reportList = res.result.sysNoticeList
					this.reportList.forEach((item, index) => {
						item.createTime = item.createTime.substring(0, 10);
					})
				} else {
					this.reportList = res.result.sysMsgList
					this.reportList.forEach((item, index) => {
						item.createTime = item.createTime.substring(0, 10);
					})
				}

			},
			//获取选中的实时信息
			async getCurrentRealTimeInfo() {
				const params = {
					pageNo: this.params.pageNo,
					pageSize: this.params.pageSize,
					contentType: this.realTimeDictData[this.segmentedControlItems[this.currentsegmentedControl]]
				}
				// 获取的实时信息字典数据
				if (params.contentType) {
					const res = await getRealTimeInfo(params)
					this.params.total = res?.data?.total
					//加载更多数据处理
					if (params.pageNo == 1) {
						this.realTimeInfo = res?.data?.list
					} else {
						this.realTimeInfo = this.realTimeInfo.concat(res?.data?.list)
					}
					//加载更多状态处理
					this.setloadMoreStatus()
				} else {
					let res
					// 通知公告
					if (this.segmentedControlItems[this.currentsegmentedControl] == '通知公告') {
						params.templateType = 4
						res = await getMyNotifyMessagePage(params)
					} else if (this.segmentedControlItems[this.currentsegmentedControl] == '消息提醒') {
						params.templateType = 3
						res = await getMyNotifyMessagePage(params)
					}
					//加载更多数据处理
					this.params.total = res?.data?.total
					if (params.pageNo == 1) {
						this.messageInfo = res?.data?.list
					} else {
						this.messageInfo = this.messageInfo.concat(res?.data?.list)
					}
					//加载更多状态处理
					this.setloadMoreStatus()
				}
			},
			//加载更多状态处理
			setloadMoreStatus() {
				if (this.params.total / this.params.pageSize > this.params.pageNo) {
					this.loadMoreStatus = 'more'
				} else {
					this.loadMoreStatus = 'noMore'
				}
			},
			//获取实时信息字典
			async getRealTimeDictData() {
				const res = await getDictData({
					type: 'jy_real_time_info_type'
				})
				res?.data?.forEach(item => {
					this.realTimeDictData[item.label] = item.value
				})
				this.segmentedControlItems = Object.keys(this.realTimeDictData)
				this.segmentedControlItems.push('通知公告', '消息提醒')
				this.getCurrentRealTimeInfo()
			},
			//加载更多
			loadMore() {
				if (this.params.total / this.params.pageSize > this.params.pageNo) {
					this.loadMoreStatus = 'loading'
					this.params.pageNo++
					this.getCurrentRealTimeInfo()
				}
			}
		},
		onLoad() {
			const that = this
			that.Props.statusBarHeight = getApp().globalData.statusBarHeight
			that.Props.capsuleTop = getApp().globalData.capsuleTop
			uni.$on('loadMore', that.loadMore)
			that.getComplaintList()
		}
	}
</script>

<style lang="scss">
	.content {
		// margin: 30rpx;
		background: #F4F8FC;
		min-height: 100vh;

		.top {
			background-color: #ffffff;
			box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.15);
			border-radius: 0rpx 0rpx 20rpx 20rpx;
			padding-bottom: 40rpx;
			margin-bottom: 32rpx;

			.Province {
				text-align: center;
				font-family: -apple-system;
				background-color: #ffffff;
				padding-top: 36rpx;
				display: flex;
				justify-content: space-around;
				background-color: white;

				.text {
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 30rpx;
					color: rgba(0, 0, 0, 0.9);
					line-height: 30rpx;
					font-style: normal;
				}

				.line-bottom {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 30rpx;
					color: rgba(20, 20, 20, 0.9);
					line-height: 30rpx;
					font-style: normal;
				}

				.line {
					width: 55rpx;
					height: 10rpx;
				}
			}


			.tip {
				width: 710rpx;
				height: 60rpx;
				margin: 0 20rpx;
				display: flex;
				align-items: center;
				background-color: #fff;
				border-radius: 3px;
				z-index: 200;
				border-radius: 30rpx;
				border: 2rpx solid #DDDDDD;

				.uni-input {
					flex: 1;
					height: 20rpx;
					padding-left: 40rpx;
				}

				span {
					width: 40px;
					color: #8b8c8f;
				}

				.search_boxs {
					width: 50rpx;
					height: 50rpx;
					display: flex;
					align-items: center;
					/* 图片在垂直方向上居中 */
					justify-content: center;
					/* 图片在水平方向上居中 */
					margin-right: 32rpx;
				}

				.search {
					width: 32rpx;
					height: 32rpx;
					z-index: 99999;
				}
			}

			.screen {
				display: flex;
				align-items: center;
				justify-content: space-around;
				background-color: #FFF;
				padding: 30rpx 0;
				// box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0,0,0,0.15);
				border-radius: 0rpx 0rpx 20rpx 20rpx;

				.orient {
					display: flex;
					align-items: center;
				}

				.uni-list {
					display: flex;
					align-items: center;
					line-height: 30rpx;
				}
			}

		}

		.boxs {
			background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 100%);
			box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
			border-radius: 32rpx;
			// padding: 32rpx;
			margin: 0 32rpx;
			height: 100%;
			width: 686rpx;
			display: flex;
			gap: 25rpx;
			flex-direction: column;
			align-items: center;
			padding: 40rpx 20rpx;

			.reports {
				background-color: #FFF;
				display: flex;
				flex-direction: column;
				width: 100%;
				height: 188rpx;
				gap: 30rpx;
				position: relative;
				border-bottom: 2rpx solid #E7F0FF;

				.record_bg {
					width: 424rpx;
					height: 180rpx;
					position: absolute;
				}

				.type {
					position: absolute;
					font-weight: 500;
					font-size: 32rpx;
					color: #181818;
					line-height: 45rpx;
					font-style: normal;
					top: 16rpx;
					left: 77rpx;
				}

				.date {
					position: absolute;
					font-weight: 400;
					font-size: 24rpx;
					color: #ACACAC;
					line-height: 33rpx;
					font-style: normal;
					top: 91rpx;
					left: 14rpx;
				}

				.text {
					position: absolute;
					font-weight: 400;
					font-size: 28rpx;
					color: #666666;
					line-height: 40rpx;
					font-style: normal;
					left: 14rpx;
					top: 16rpx;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.icon {
					width: 48rpx;
					height: 48rpx;
					position: absolute;
					top: 14rpx;
					left: 14rpx;
				}
			}
		}
	}

	.cacel_button {
		width: 50rpx;
		height: 50rpx;
		margin-right: 10rpx;
		display: flex;
		align-items: center;
		/* 图片在垂直方向上居中 */
		justify-content: center;
		/* 图片在水平方向上居中 */
	}

	.cacel {
		width: 32rpx;
		height: 32rpx;

	}


	.answerBoxs {
		height: 500rpx;

		.answer-content {
			height: 500rpx;
			display: flex;
			align-items: center;
			flex-direction: column;
			justify-content: center;
			background: #fff;
			border-radius: 35rpx;
			background-color: #fff;
			width: 600rpx;
			position: relative;
			padding-top: 40rpx;

			.substance {
				width: 600rpx;
				margin-bottom: 40rpx;
				padding: 0 20rpx;
			}

			.query {
				width: 530rpx;
				height: 86rpx;
				background: #137AFF;
				border-radius: 50rpx;
				font-weight: 400;
				font-size: 38rpx;
				color: #FFFFFF;
				line-height: 86rpx;
				text-align: center;
				font-style: normal;
			}
		}
	}
</style>