<template>
  <view
    :style="{
      paddingTop: Props.statusBarHeight + 'px'
    }"
  >
    <navbar :Props="Props" :title="headtitle" @black="black"></navbar>
    <view class="container">
      <image src="https://cwtest.lcqf.net/static/img/wx-bg.png" mode="aspectFill" />
      <button type="primary" open-type="getPhoneNumber" @getphonenumber="login">
        <text>手机号快捷登录</text>
      </button>
      <!-- 选择身份 -->
      <uni-popup ref="popup" border-radius="10px 10px 0 0">
        <view class="popup-content">
          <view class="popup-bg"> <image src="@/static/images/login/login-bg.png"></image></view>
          <view class="popup-title">
            <image class="false" src="@/static/images/login/title.png"></image>
            嘉有小乔
          </view>
          <view class="popup-body">
            <view @click="handleLoginType(0)">
              <image src="@/static/images/login/bb.png"></image>
              帮办员登录</view
            >
            <view @click="handleLoginType(1)">
              <image src="@/static/images/login/yh.png"></image>
              普通用户登录</view
            >
          </view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<script>
import Verify from '@/components/verifition/Verify'
import { wxLogin, createUser, authorizeLogin, checkToken } from '@/api/login'
import { newSetToken, removeToken } from '@/utils/auth'
import navbar from '@/components/Navbar/index.vue'
export default {
  name: 'Login',
  components: {
    Verify,
    navbar
  },
  data() {
    return {
      headtitle: '微信登陆',
      Props: {
        imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: '', //导航高度(动态获取传参)
        bgColor: '', //导航栏背景色,不传参则默认#9CF
        capsuleTop: '', //胶囊顶部距离(动态获取传参)
        textColor: '', //导航标题字体颜色(不传默认#FFF)
        iconColor: '', //icon图标颜色(不传默认#FFF)
        blackShow: '', //是否显示返回字体及icon图标(不传默认显示true)
        backText: '' //默认字体(返回)
      },
      captchaEnabled: false, // 验证码开关 TODO 芋艿：需要抽到配置里
      globalConfig: getApp().globalData.config,
      loginForm: {
        username: 'admin',
        password: 'Zhcskj@2024',
        captchaVerification: ''
      },
      loginTabs: [
        {
          name: '微信一键登录'
        },
        {
          name: '账号密码登录'
        }
      ],
      loginType: 0,
      popup: null
    }
  },
  onShow() {
    // this.$refs.popup.open('center')
  },
  onLoad() {
    this.Props.statusBarHeight = getApp().globalData.statusBarHeight
    this.Props.capsuleTop = getApp().globalData.capsuleTop
  },
  methods: {
    async login(e) {
      const detail = e.detail
      if (detail.errMsg === 'getPhoneNumber:ok') {
        const iphoneCode = detail.code
        await this.wxAutoLogin(iphoneCode)
      }
    },

    async wxAutoLogin(iphoneCode) {
      try {
        const res = await uni.login({ onlyAuthorize: true })
        const code = res[1].code || res.code
        if (code) {
          const res2 = await wxLogin(code)
          if (res2.result.token) {
            this.wxLoginSuccess(res2.result.token)
          } else {
            const userInfo = await uni.getUserInfo({ provider: 'weixin' })
            const infoRes = userInfo[1]
            const formData = {
              encData: infoRes.encryptedData,
              iv: infoRes.iv,
              sessionKey: res2.result.sessionKey,
              openId: res2.result.openId,
              code: iphoneCode
            }
            const res3 = await createUser(formData)
            if (res3.success) {
              const res4 = await authorizeLogin(formData)
              this.wxLoginSuccess(res4.result.token)
            }
          }
        }
      } catch (error) {
        console.error('登录流程出错', error)
      }
    },

    click(e) {
      this.loginType = e.index
    },
    // 隐私协议
    handlePrivacy() {
      let site = this.globalConfig.appInfo.agreements[0]
      this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
    },
    // 用户协议
    handleUserAgrement() {
      let site = this.globalConfig.appInfo.agreements[1]
      this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
    },
    // 登录方法
    async handleLogin(params) {
      if (this.loginForm.username === '') {
        this.$modal.msgError('请输入您的账号')
      } else if (this.loginForm.password === '') {
        this.$modal.msgError('请输入您的密码')
      } else {
        // 显示验证码
        if (this.captchaEnabled) {
          this.$refs.verify.show()
        } else {
          // 直接登录
          await this.pwdLogin({})
        }
      }
    },
    // 密码登录
    async pwdLogin(captchaParams) {
      this.$modal.loading('登录中，请耐心等待...')
      // 执行登录
      this.loginForm.captchaVerification = captchaParams.captchaVerification
      this.$store
        .dispatch('Login', this.loginForm)
        .then(() => {
          this.$modal.closeLoading()
          this.loginSuccess()
        })
        .catch((err) => {
          this.$refs.verify.hide()
        })
    },
    // 登录成功后，处理函数
    loginSuccess(result) {
      // 设置用户信息
      this.$store.dispatch('GetInfo').then((res) => {
        this.$tab.reLaunch('/pages/home/<USER>')
      })
    },
    // 微信认证登录成功
    async wxLoginSuccess(token) {
      newSetToken(token)
      const res = await checkToken(token)
      const avatar = res.result.userInfo.avatar
      const nickname = res.result.userInfo.nickName
      if (res.result.roles && res.result.roles.length > 0) {
        this.$store.commit('SET_ROLES', res.result.roles)
        this.$store.commit('SET_PERMISSIONS', res.result.permissions)
      } else {
        this.$store.commit('SET_ROLES', ['ROLE_DEFAULT'])
      }
      this.$store.commit('SET_NAME', nickname)
      this.$store.commit('SET_AVATAR', avatar)
      this.$store.commit('SET_USERINFO', res.result.userInfo)

      // 普通用户
      if (res.result.userInfo.isAssist == 0) {
        uni.reLaunch({ url: '/pages/home/<USER>' })
        this.$store.commit('setRoleId', 1)
      }
      // 帮办员/管理员
      if (res.result.userInfo.isAssist == 1 || res.result.userInfo.isAssistManger == 1) {
        this.$refs.popup.open('center')
      }
    },
    handleLoginType(type) {
      uni.setStorageSync('loginType', type)
      this.$store.commit('setRoleId', type)
      if (type === 0) {
        uni.reLaunch({ url: '/pages/admin/index' })
      }
      if (type === 1) {
        uni.reLaunch({ url: '/pages/home/<USER>' })
      }
    }
  }
}
</script>

<style>
page {
  background-color: #fff;
}
</style>
<style lang="scss" scoped>
.container {
  width: 630rpx;
  margin: 60rpx auto;
  border-radius: 8rpx;
  padding: 0rpx 30rpx;
}
.popup-content {
  // background: #fff;
  position: relative;
  width: 540rpx;
  height: 380rpx;
  padding: 30rpx;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  .popup-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .popup-title {
    font-size: 36rpx;
    color: #333;
    text-align: center;
    font-weight: 600;
    width: 194rpx;
    height: 64rpx;
    line-height: 64rpx;
    position: relative;
    z-index: 1;
    margin: 0 auto 30rpx;
    image {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: -1;
    }
  }
  .popup-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 1;
    image {
      width: 56rpx;
      height: 56rpx;
      margin-right: 20rpx;
    }
    & > view {
      width: 420rpx;
      height: 90rpx;
      margin: 0 auto 30rpx;
      background: rgba(5, 127, 254, 0.1);
      border-radius: 45rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 30rpx;
      color: #333333;
      line-height: 90rpx;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
