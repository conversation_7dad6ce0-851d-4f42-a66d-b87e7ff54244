<template>
  <view class="normal-login-container">
    <image class="bg" src="/static/images/login/l-bg.png"></image>
    <view class="logo-content">
      <image class="logo" src="/static/images/login/logo.png"></image>
      <view class="title">
        <text>你好，</text>
        <text>欢迎使用"嘉有小乔"</text>
      </view>
      <view class="login-form-content">
        <view class="login-toggle">
          <view @click="loginChange(0)" :class="loginType == 0 ? 'active' : ''">
            <image
              v-show="loginType == 0"
              class="tab1"
              src="@/static/images/login/tab1.png"
            ></image>
            <view class="tab">
              <text>注册新用户</text>
              <image
                v-show="loginType == 0"
                class="tab3"
                src="@/static/images/login/tab3.png"
              ></image>
            </view>
          </view>
          <view :class="loginType == 1 ? 'active' : ''">
            <image
              v-show="loginType == 1"
              class="tab2"
              src="@/static/images/login/tab2.png"
            ></image>
            <view class="tab">
              <text></text>
              <image
                v-show="loginType == 1"
                class="tab3"
                src="@/static/images/login/tab3.png"
              ></image>
            </view>
          </view>
        </view>
        <view class="input-item">
          <input
            v-model="loginForm.username"
            class="input"
            type="tel"
            placeholder="请输入手机号"
            maxlength="11"
          />
        </view>
        <view class="input-item" v-show="loginType == 0">
          <input
            v-model="loginForm.password"
            :type="showPassword ? 'text' : 'password'"
            class="input"
            placeholder="请输入密码"
            maxlength="20"
            ref="password"
          />
          <image
            @click="handleEye"
            v-show="showPassword"
            class="eye"
            src="@/static/images/login/pwd.png"
          ></image>
          <image
            @click="handleEye"
            v-show="!showPassword"
            class="eye"
            src="@/static/images/login/pwd2.png"
          ></image>
        </view>
        <view class="input-item">
          <input
            v-model="loginForm.captcha"
            type="text'"
            class="input"
            placeholder="请输入验证码"
            maxlength="20"
            ref="password"
          />
          <view class="get-code" @tap="getCode">{{ tips }}</view>
        </view>
        <waves itemClass="custom">
          <view class="action-btn" @click="handleLogin"> 登录 </view>
        </waves>

        <view class="login-type">
          <view
            >已有账号,
            <text @click="$tab.reLaunch('/pages/login/index')" class="to_login">去登录</text></view
          >
        </view>
        <view class="agreement">
          <image
            @click="isAgreement = !isAgreement"
            v-if="!isAgreement"
            src="@/static/images/login/radio.png"
          ></image>
          <image
            @click="isAgreement = !isAgreement"
            v-if="isAgreement"
            src="@/static/images/login/radio-active.png"
          ></image>
          <view
            >已阅读并同意 <text>《用户协议》</text>和
            <text>《隐私政策》</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 选择身份 -->
    <uni-popup ref="popup" border-radius="10px 10px 0 0">
      <view class="popup-content">
        <view class="popup-bg"> <image src="@/static/images/login/login-bg.png"></image></view>
        <view class="popup-title">
          <image class="false" src="@/static/images/login/title.png"></image>
          嘉有小乔
        </view>
        <view class="popup-body">
          <view @click="handleLoginType(0)">
            <image src="@/static/images/login/bb.png"></image>
            帮办员登录</view
          >
          <view @click="handleLoginType(1)">
            <image src="@/static/images/login/yh.png"></image>
            普通用户登录</view
          >
        </view>
      </view>
    </uni-popup>
    <u-toast ref="uToast"></u-toast>
    <u-code :seconds="seconds" ref="uCode" @change="codeChange"></u-code>
  </view>
</template>

<script>
import {
  wxLogin,
  createUser,
  authorizeLogin,
  checkToken,
  sendMsg,
  loginByPasswd,
  registerUser
} from '@/api/login'
import { newSetToken, removeToken } from '@/utils/auth'
import CryptoJS from 'crypto-js'
import waves from '@/components/waves/waves.vue'
export default {
  name: 'Login',
  components: { waves },
  data() {
    return {
      captchaEnabled: false, // 验证码开关 TODO 芋艿：需要抽到配置里
      globalConfig: getApp().globalData.config,
      loginForm: {
        username: '',
        password: '',
        captcha: '',
        checkKey: ''
      },
      loginTabs: [
        {
          name: '微信一键登录'
        },
        {
          name: '账号密码登录'
        }
      ],
      loginType: 0,
      popup: null,
      showPassword: false,
      key: '1234567890adbcde',
      iv: '1234567890hjlkew',
      seconds: 60,
      tips: '',
      isAgreement: false
    }
  },
  onShow() {},
  methods: {
    loginChange(type) {
      this.loginType = type
      this.loginForm.username = ''
      this.loginForm.password = ''
      this.loginForm.captcha = ''
      this.loginForm.checkKey = ''
    },
    go(url) {
      if (!this.isAgreement) {
        uni.$u.toast('请阅读并同意《用户协议》和《隐私政策》')
        return
      }
      uni.navigateTo({
        url
      })
    },
    async login(e) {
      const detail = e.detail
      if (detail.errMsg === 'getPhoneNumber:ok') {
        const iphoneCode = detail.code
        await this.wxAutoLogin(iphoneCode)
      }
    },

    async wxAutoLogin(iphoneCode) {
      try {
        const res = await uni.login({ onlyAuthorize: true })
        const code = res[1].code || res.code
        if (code) {
          const res2 = await wxLogin(code)
          if (res2.result.token) {
            this.wxLoginSuccess(res2.result.token)
          } else {
            const userInfo = await uni.getUserInfo({ provider: 'weixin' })
            const infoRes = userInfo[1]
            const formData = {
              encData: infoRes.encryptedData,
              iv: infoRes.iv,
              sessionKey: res2.result.sessionKey,
              openId: res2.result.openId,
              code: iphoneCode
            }
            const res3 = await createUser(formData)
            if (res3.success) {
              const res4 = await authorizeLogin(formData)
              this.wxLoginSuccess(res4.result.token)
            }
          }
        }
      } catch (error) {
        console.error('登录流程出错', error)
      }
    },
    // 隐私协议
    handlePrivacy() {
      let site = this.globalConfig.appInfo.agreements[0]
      console.log('隐私协议', site)
      this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
    },
    // 用户协议
    handleUserAgrement() {
      let site = this.globalConfig.appInfo.agreements[1]
      this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
    },
    // 登录方法
    async handleLogin() {
      if (!this.isAgreement) {
        uni.$u.toast('请阅读并同意《用户协议》和《隐私政策》')
        return
      }
      if (this.loginForm.username === '') {
        uni.$u.toast('请输入手机号')
        return
      }
      if (this.loginForm.password === '' && this.loginType == 0) {
        uni.$u.toast('请输入密码')
        return
      }
      if (this.loginForm.captcha === '' && this.loginType == 1) {
        uni.$u.toast('请输入验证码')
        return
      }
      const res = await registerUser({
        username: this.encryptLoginData(this.loginForm.username),
        password: this.encryptLoginData(this.loginForm.password),
        captcha: this.loginForm.captcha,
        checkKey: this.loginForm.checkKey
      })
      if (res.success) {
        uni.$u.toast(res.message)
        // 跳转登录页
        setTimeout(() => {
          uni.redirectTo({ url: '/pages/login/login' })
        }, 1000)
      } else {
        uni.$u.toast(res.message)
      }
    },
    // 登录成功后，处理函数
    loginSuccess(result) {
      // 设置用户信息
      this.$store.dispatch('GetInfo').then((res) => {
        this.$tab.reLaunch('/pages/home/<USER>')
      })
    },
    // 微信认证登录成功
    async wxLoginSuccess(token) {
      newSetToken(token)
      const res = await checkToken(token)
      const avatar = res.result.userInfo.avatar
      const nickname = res.result.userInfo.nickName
      if (res.result.roles && res.result.roles.length > 0) {
        this.$store.commit('SET_ROLES', res.result.roles)
        this.$store.commit('SET_PERMISSIONS', res.result.permissions)
      } else {
        this.$store.commit('SET_ROLES', ['ROLE_DEFAULT'])
      }
      this.$store.commit('SET_NAME', nickname)
      this.$store.commit('SET_AVATAR', avatar)
      this.$store.commit('SET_USERINFO', res.result.userInfo)

      // 普通用户
      if (res.result.userInfo.isAssist == 0) {
        uni.reLaunch({ url: '/pages/home/<USER>' })
        this.$store.commit('setRoleId', 1)
      }
      // 帮办员/管理员
      if (res.result.userInfo.isAssist == 1 || res.result.userInfo.isAssistManger == 1) {
        this.$refs.popup.open('center')
      }
    },
    handleLoginType(type) {
      uni.setStorageSync('loginType', type)
      this.$store.commit('setRoleId', type)
      if (type === 0) {
        uni.reLaunch({ url: '/pages/admin/index' })
      }
      if (type === 1) {
        uni.reLaunch({ url: '/pages/home/<USER>' })
      }
    },
    handleEye() {
      this.showPassword = !this.showPassword
    },
    encryptLoginData(data) {
      if (typeof data !== 'string') {
        console.error('Input must be a string')
        return null
      }
      const key = CryptoJS.enc.Utf8.parse('1234567890adbcde')
      const iv = CryptoJS.enc.Utf8.parse('1234567890hjlkew')
      const blockSize = 16
      const paddingSize = blockSize - (data.length % blockSize)
      const paddedData = data + String.fromCharCode(paddingSize).repeat(paddingSize)
      try {
        const encrypted = CryptoJS.AES.encrypt(paddedData, key, {
          iv: iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.NoPadding
        })
        const result = encrypted.ciphertext.toString(CryptoJS.enc.Base64)
        console.log('Encrypted result:', result)
        return result
      } catch (error) {
        console.error('Encryption error:', error)
        return null
      }
    },
    codeChange(text) {
      this.tips = text
    },
    async getCode() {
      if (!this.loginForm.username) {
        uni.$u.toast('请输入手机号')
        return
      }
      if (this.$refs.uCode.canGetCode) {
        const res = await sendMsg({
          phone: this.encryptLoginData(this.loginForm.username),
          type: 'loginCode'
        })
        uni.$u.toast('验证码已发送')
        this.$refs.uCode.start()
      } else {
        uni.$u.toast('倒计时结束后再发送')
      }
    }
  }
}
</script>

<style>
page {
  background-color: #f3faff;
}
</style>
<style lang="scss">
.custom {
  width: 630rpx;
  height: 100rpx;
  border-radius: 49rpx;
  margin: 30rpx auto;
}
</style>
<style lang="scss" scoped>
.login-toggle {
  display: flex;
  align-items: center;
  width: 100%;
  height: 136rpx;
  & > view {
    position: relative;
    flex: 1;
    text-align: center;
    height: 100%;
    top: -26%;
    .tab {
      position: relative;
      top: 60%;
      z-index: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      & > text {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32rpx;
        color: #565d6e;
        margin-bottom: 10rpx;
      }
    }
    .tab1 {
      position: absolute;
      top: 0%;
      left: 0%;
      width: 100%;
      height: 128rpx;
    }

    .tab2 {
      position: absolute;
      top: 0%;
      right: 0%;
      width: 100%;
      height: 128rpx;
    }
    .tab3 {
      width: 55rpx;
      height: 10rpx;
    }
  }
  .active {
    .tab {
      top: 30%;
      & > text {
        font-weight: 600;
        color: #141414;
      }
    }
  }
}

.normal-login-container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  .bg {
    width: 100%;
    height: 1334rpx;
  }
  .logo {
    position: absolute;
    right: 15%;
    top: 0%;
    width: 165rpx;
    height: 131rpx;
    z-index: 3;
  }
  .logo-content {
    position: absolute;
    top: 15%;
    width: 100%;
    z-index: 2;

    .title {
      width: 686rpx;
      margin: 0 auto;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 40rpx;
      color: #ffffff;
      display: flex;
      flex-direction: column;
      text-align: left;
    }
  }

  .login-form-content {
    width: 686rpx;
    height: 960rpx;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    border-radius: 32rpx 32rpx 32rpx 32rpx;
    text-align: center;
    margin: 20px auto;
    margin-top: 15%;
    position: relative;
    .input-item {
      margin: 20px auto;
      background-color: #f5f7fb;
      width: 630rpx;
      height: 100rpx;
      background: #f5f7fb;
      border-radius: 49rpx;
      position: relative;
      .input {
        width: 100%;
        height: 100%;
        font-size: 14px;
        text-align: left;
        padding-left: 15px;
      }
      .eye {
        position: absolute;
        right: 6%;
        top: 50%;
        transform: translateY(-50%);
        width: 36rpx;
        height: 36rpx;
        z-index: 2;
      }
      .get-code {
        position: absolute;
        right: 6%;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26rpx;
        color: #057ffe;
      }
    }
    .action-btn {
      width: 630rpx;
      height: 100rpx;
      line-height: 100rpx;

      background: linear-gradient(310deg, #0cb6ff 0%, #057ffe 100%);
      border-radius: 49rpx;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 32rpx;
      color: #ffffff;
    }
    .xieyi {
      color: #333;
      margin-top: 20px;
    }
    ::-webkit-input-placeholder {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
    }

    .login-type {
      width: 80%;
      margin: 20rpx auto;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      .to_login {
        color: #057ffe;
      }
    }
    .login-other {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      margin-top: 220rpx;

      image {
        width: 90rpx;
        height: 90rpx;
        margin: 20rpx 60rpx;
      }
    }
    .agreement {
      width: 100%;
      position: absolute;
      bottom: 5%;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      image {
        width: 26rpx;
        height: 26rpx;
        margin-right: 10rpx;
      }
      text {
        color: #057ffe;
      }
    }
  }

  .easyinput {
    width: 100%;
  }
}

.login-code-img {
  height: 45px;
}
.popup-content {
  // background: #fff;
  position: relative;
  width: 540rpx;
  height: 380rpx;
  padding: 30rpx;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  .popup-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .popup-title {
    font-size: 36rpx;
    color: #333;
    text-align: center;
    font-weight: 600;
    width: 194rpx;
    height: 64rpx;
    line-height: 64rpx;
    position: relative;
    z-index: 1;
    margin: 0 auto 30rpx;
    image {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: -1;
    }
  }
  .popup-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 1;
    image {
      width: 56rpx;
      height: 56rpx;
      margin-right: 20rpx;
    }
    & > view {
      width: 420rpx;
      height: 90rpx;
      margin: 0 auto 30rpx;
      background: rgba(5, 127, 254, 0.1);
      border-radius: 45rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 30rpx;
      color: #333333;
      line-height: 90rpx;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
