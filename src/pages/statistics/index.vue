<template>
  <view :style="{
      paddingTop:  Props.statusBarHeight + 'px',
    }">
    <navbar :Props="Props" :title="headtitle" @black="black"></navbar>
    <view>some text</view>
    <TabBar :current="2" :tabBarList="tabBerLists" />
  </view>
</template>

<script>
import global from '@/utils/global.js'
import TabBar from '@/components/TabBar/index.vue'
import navbar from '@/components/Navbar/index.vue'
export default {
  components: {
    TabBar,
    navbar
  },
  data() {
    return {
      tabBerLists: [],
      headtitle: '数据分析',
      Props: {
        imgShow: '', //不传参则默认隐藏状态(false),且使用默认背景色
        statusBarHeight: '', //导航高度(动态获取传参)
        bgColor: '', //导航栏背景色,不传参则默认#9CF
        capsuleTop: '', //胶囊顶部距离(动态获取传参)
        textColor: '', //导航标题字体颜色(不传默认#FFF)
        iconColor: '', //icon图标颜色(不传默认#FFF)
        blackShow: 'true', //是否显示返回字体及icon图标(不传默认显示true)
        backText: '' //默认字体(返回)
      }
    }
  },
  onShow() {
    this.tabBerLists = uni.getStorageSync('tabBarList')
  },
  onLoad() {
    this.Props.statusBarHeight = getApp().globalData.statusBarHeight
    this.Props.capsuleTop = getApp().globalData.capsuleTop
  }
}
</script>

<style lang="scss" scoped></style>
