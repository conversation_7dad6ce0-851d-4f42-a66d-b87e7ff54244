# 目录

┌─api                   分模块封装后端接口方法，引用request.js文件，request.js里做了接口的公共配置，包括超时时间、  baseUrl、携带的header,取得数据后的处理，根据返回的code和message给出不同的提示
│─components            封装的公共组件
                        包括标题组件和登录校验组件
├─uni_modules           安装的依赖
├─pages                 业务页面文件
├─plugins               封装了auth权限、modal弹窗、tab页面调整等公共方法，并添加到原型链上
├─static                存放应用引用的本地静态资源（如图片、视频等）的目录，iconfont图标、图片、全局公共样式，app.vue里引入
├─store                 数据仓库，保存了用户id、用户名、头像、角色、权限相关的信息
├─uni_modules           安装的uni-ui扩展组件，包括数字角标、面包屑、日历等
├─utils                 封装了通用公共方法，使用时按需引入
├─App.vue               uni-app的主组件，监听应用生命周期、配置全局样式、配置全局的存储globalData
├─config.js             应用全局配置
├─main.js               uni-app的入口文件，初始化vue实例、定义全局组件、使用需要的插件
├─manifest.json         应用的配置文件，用于指定应用的名称、图标、权限等
├─pages.json            配置页面路由、导航条、选项卡等页面类信息；
├─permission.js         路由拦截，配置白名单
└─uni.scss              常用样式变量