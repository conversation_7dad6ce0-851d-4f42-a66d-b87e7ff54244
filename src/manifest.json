{
	"name": "\"嘉有小乔\"惠企服务小程序",
	"appid": "__UNI__E867BCE",
	"description": "",
	"versionName": "1.0.0",
	"versionCode": "100",
	"transformPx": false,
	"app-plus": {
		"usingComponents": true,
		"nvueCompiler": "uni-app",
		"splashscreen": {
			"alwaysShowBeforeRender": true,
			"waiting": true,
			"autoclose": true,
			"delay": 0
		},
		"modules": {
			"Share": {}
		},
		"distribute": {
			"android": {
				"permissions": [
					"<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
					"<uses-permission android:name=\"android.permission.VIBRATE\"/>",
					"<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
					"<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CAMERA\"/>",
					"<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
					"<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
					"<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
					"<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
					"<uses-feature android:name=\"android.hardware.camera\"/>",
					"<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
				]
			},
			"ios": {},
			"sdkConfigs": {
				"share": {
					"weixin": {
						"appid": "wxe363bc1e697ad449",
						"UniversalLinks": ""
					}
				}
			}
		}
	},
	"quickapp": {},
	"mp-weixin": {
		"appid": "wxe5d66251038859e0",
		"setting": {
			"urlCheck": false,
			"es6": false,
			"minified": true,
			"postcss": true
		},
		"optimization": {
			"subPackages": true
		},
		"usingComponents": true,
		"permission": {
			"scope.userLocation": {
				"desc": "获取定位"
			}
		},
		"requiredPrivateInfos": [
			"getLocation",
			"onLocationChange",
			"startLocationUpdateBackground",
			"chooseAddress",
			"startLocationUpdate"
		]
	},
	"vueVersion": "2",
	"h5": {
		"template": "static/index.html",
		"devServer": {
			"port": 9090,
			"https": false,
			"proxy": {
				"chat": {
					"target": "https://jyxq.jialangshuchan.cn/",
					"changeOrigin": true,
					"pathRewrite": {
						"^/chat": "/chat"
					}
				},
				"knowledgeBase": {
					"target": "https://jyxq.jialangshuchan.cn/",
					"changeOrigin": true,
					"pathRewrite": {
						
					}
				},
				"humanDigital" : {
				    "target" : "http://************:1985",
				    "changeOrigin" : true,
				    "pathRewrite" : {
						// "^/humanDigital" : "/humanDigital"
					}
				},
				"wss-detecting-human" : {
				    "target" : "ws://************:38010",
				    "changeOrigin" : true,
				    "pathRewrite" : {}
				},
				"tts":{
				    // "target" : "http://************:50050/tts",
					"target" : "http://************:50050",
				    // "target" : "http://************:50000/inference_sft_post",
				    "changeOrigin" : true,
				    "pathRewrite" : {
				        // "^/audioAnswer" : ""
				    }
				}
			}
		},
		"title": "xiaoqiao-h5",
		"router": {
			"mode": "hash",
			"base": "./"
		},
		"sdkConfigs": {
			"maps": {
				"qqmap": {
					"key": "G4GBZ-4SNWX-6D64Z-7CEQ5-ZK7AO-PNFIV"
				}
			}
		}
	}
}