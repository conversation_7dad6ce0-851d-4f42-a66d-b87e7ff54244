<!-- z-paging聊天item -->

<template>
	<view class="">

		<scroll-view scroll-y="true" class="scroll-box">
			<view class="chat-item">
				<!-- 		<view :class="{'chat-container':true,'chat-location-me':item.type==1}">
			<view class="chat-icon-container">
				<image class="chat-icon" :src="item.icon" mode="aspectFill" />
			</view>
			<view class="chat-content-container">
				<view class="chat-text-container-super" :style="[{justifyContent:item.type==1?'flex-end':'flex-start'}]">
					<view :class="{'chat-text-container':true,'chat-text-container-me':item.type==1}">
						<view class="bubble">
							<view :class="{'chat-text':true,'chat-text-me':item.type==1}">
								<view v-if="item.content">
									<text space="nbsp" user-select="true">{{item.content}}
										<text v-if="item.type==0 && !item.end" class="cursor">|</text>
									</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view> -->
				<view class="qa-item" v-for="(item, index) in msgList" :key="index">
					<!-- 时间戳 -->
					<!-- 			<view class="timestamp"
				v-if="index === 0 || (index !== 0 && item.timestamp && (Number(item.timestamp) - Number(msgList[index - 1].timestamp)) > timestampGap)">
				{{ moment(new Date(String(item.timestamp).length === 10 ? item.timestamp * 1000 : Number(item.timestamp))).format('MM-DD HH:mm') }}
			</view> -->

					<!-- 问题 -->
					<view class="question-item" v-if="item.is_from_self">
						<!-- <v-spinner status="default" class="qs-loading" v-if="item.is_loading"></v-spinner> -->
						<view class="answer-content">{{ item.content }}</view>
						<!-- 头像 -->
						<view class="answer-avatar">
							<image class="robot-avatar" src="@/static/images/mine/pj.png" mode=""></image>
						</view>
					</view>
					<!-- 答案 -->
					<view class="answer-item" v-if="!item.is_from_self">
						<!-- 头像 -->
						<view class="answer-avatar">
							<!-- <img class="robot-avatar" :src="item.from_avatar" /> -->
							<image src="@/static/images/xq_header.jpg" class="robot-avatar" mode=""></image>
						</view>

						<!-- 答案信息 -->
						<view class="answer-info" :ref="item.record_id">
							<view class="loading" v-if="item.loading_message">正在思考中</view>
							<!-- Markdown渲染 -->
							<view>{{ item.content }}</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>

</template>

<script>
	import {
		MESSAGE_TYPE,
		ACCESS_TYPE
	} from '@/module/constants/index.js';
	export default {
		name: "chat-item",
		props: {
			item: {
				type: Object,
				default: function() {
					return {
						time: '',
						icon: '',
						name: '',
						content: '',
						isMe: false
					}
				}
			}
		},
		data() {
			return {
				loading: false,
				historyLoading: false,
				timestampGap: 5 * 60, // 两次问题间隔大于5min，则展示时间戳（接口侧返回的时间戳是秒级）
				msgList: [], // 对话消息列表
				robotName: '', // 机器人名称
				jsScrolling: false,
				userScrolling: false,
				scrollTop: 0, // 初始滚动位置

			};
		},
		created() {
			// 监听用户端/管理端体验侧的ws事件
			this.listenClientAndManageEvent();
			// 监听公共的ws事件
			this.listenCommonEvent();
		},
		methods: {
			// 监听用户端/管理端体验侧的ws事件
			listenClientAndManageEvent() {
				console.log('listenClientAndManageEvent------------------');
				// 从缓存获取机器人信息
				let cachedConfig = null;
				cachedConfig = this.$clientData.getConfigInfo();
				if (cachedConfig) {
					this.robotName = cachedConfig.name;
				}

				// 监听答案消息队列变更事件
				this.$eventHub.$on('client_msgContentChange', (res) => {
					console.log(res, '09999---------------');
					const {
						chatsContent,
						type
					} = res;
					// PS：若新消息不属于当前机器人，则在 $clientData 中监听到ws消息后判断并屏蔽。不在此处判断和屏蔽
					this.renderMsgList(chatsContent, type);
				});
			},
			// 渲染消息会话页面
			renderMsgList(data, type) {
				// 无需滚动至底部的ws事件：用户端拉取历史记录、用户端停止生成、坐席端取历史记录、点赞点踩
				const noScrollEvt = [MESSAGE_TYPE.HISTORY, MESSAGE_TYPE.STOP, MESSAGE_TYPE.WORKBENCH_HISTORY, MESSAGE_TYPE
					.FEEDBACK
				];
				const list = data.map(el => {
					return {
						...el,
						showPop: true
					};
				});
				console.log(list, 'list---');
				this.msgList = uni.$u.deepClone(list);
				console.log(this.msgList, 'this.msgList---');
				// 对话框滚动至底部（部分ws事件类型无需执行滚动）
			},
			// 监听公共的ws事件
			listenCommonEvent() {
				this.$eventHub.$on('data_history', () => {
					this.historyLoading = false;
				});

				this.$eventHub.$on('data_historyError', () => {
					this.historyLoading = false;
				});
			},
			// 预览图片
			showPic(msg) {
				let imagesUrl = [msg.data.url];
				uni.previewImage({
					urls: imagesUrl
				});
			},
			downloadFile(data) {
				// #ifdef H5
				window.open(data.data.url, '_blank');
				// #endif
			}
		}
	}
</script>

<style lang="scss">
	.chat-item {
		display: flex;
		flex-direction: column;
		padding: 20rpx;
		overflow-y: scroll;
		/* 开启纵向滚动条 */
		height: 90vh;
		width: 100vw;

		.question-item {
			display: flex;
			float: right;
			margin-bottom: 20rpx;
			.answer-content {
				max-width: 404rpx;
				background: #DCECFC;
				box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0, 0, 0, 0.1);
				border-radius: 0rpx 20rpx 20rpx 20rpx;
				font-weight: 500;
				font-size: 28rpx;
				color: #444444;
				line-height: 44rpx;
				text-align: right;
				font-style: normal;
				padding: 19rpx 27rpx;
				margin-right: 20rpx;
			}
		}

		.answer-item {
			display: flex;
			margin-bottom: 20rpx;
			.answer-info {
				width: 530rpx;
				background: #FFFFFF;
				box-shadow: 0rpx 0rpx 17rpx 0rpx rgba(0,0,0,0.1);
				border-radius: 0rpx 20rpx 20rpx 20rpx;
				font-weight: 500;
				font-size: 28rpx;
				color: #444444;
				line-height: 44rpx;
				text-align: left;
				font-style: normal;
				margin-left: 20rpx;
				padding: 19rpx 27rpx;
			}
		}
	}

	.chat-time {
		padding: 4rpx 0rpx;
		text-align: center;
		font-size: 22rpx;
		color: #aaaaaa;
	}

	.chat-container {
		display: flex;
		flex-direction: row;
	}

	.chat-location-me {
		flex-direction: row-reverse;
		text-align: right;
	}

	.chat-icon-container {
		margin-top: 12rpx;
	}

	.chat-icon {
		width: 58rpx;
		height: 58rpx;
		border-radius: 50%;
		background-color: #eeeeee;
	}

	.chat-content-container {
		width: 100%;
		margin: 0rpx 15rpx;
	}

	.chat-user-name {
		font-size: 26rpx;
		color: #888888;
	}

	.chat-text-container {
		text-align: left;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 16rpx 30rpx;
		margin-top: 10rpx;
		max-width: calc(100% - 58rpx);

	}

	.chat-text-container-me {
		background-color: #007AFF;
	}

	.chat-text-container-super {
		display: flex;
		flex-direction: row;
		width: auto;
	}

	.chat-text {
		font-size: 28rpx;
		/* #ifndef APP-NVUE */
		word-break: break-all;
		/* #endif */
		/* #ifdef APP-NVUE */
		max-width: 500rpx;
		/* #endif */
	}

	.chat-text-me {
		color: white;
	}

	.img {
		/* #ifndef APP-NVUE */
		max-width: 500rpx;
		/* #endif */
	}

	.bubble {
		width: 100%;

		.iconfont {
			font-size: 42rpx;
			margin-right: 8rpx;
		}
	}

	.chat-text-container {
		.bubble {
			color: #333;

			.iconfont {
				color: #333;
			}
		}
	}

	.chat-text-container-me {
		.bubble {
			color: #FFFFFF;

			.iconfont {
				color: #FFFFFF;
			}
		}
	}

	.cursor {
		margin-right: 5px;
		animation: blink 0.7s step-end infinite;
		font-size: 16px;
		display: inline-block;
	}

	@keyframes blink {

		from,
		to {
			opacity: 0;
		}

		50% {
			opacity: 1;
		}
	}

	.robot-avatar {
		width: 66rpx;
		height: 66rpx;
	}
</style>