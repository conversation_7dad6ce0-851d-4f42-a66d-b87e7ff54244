<template>
  <view>
    <u-tabbar
      :value="getCurrent()"
      @change="tabbatChange"
      :fixed="true"
      :placeholder="true"
      :safeAreaInsetBottom="true"
      :border="false"
    >
      <u-tabbar-item v-for="(item, index) in tabBarList" :key="index" :text="item.text">
        <image class="u-page__item__slot-icon" slot="active-icon" :src="item.inactive"></image>
        <image class="u-page__item__slot-icon" slot="inactive-icon" :src="item.icon"></image>
      </u-tabbar-item>
    </u-tabbar>
  </view>
</template>

<script>
import global from '@/utils/global.js'
export default {
  props: ['current', 'tabBarList'],
  data() {
    return {}
  },
  methods: {
    getCurrent() {
      return global.tabBarCurrent ? global.tabBarCurrent : this.current
    },
    tabbatChange(index) {
      global.tabBarCurrent = index
      if (this.tabBarList[index].pagePath == '/pages/statistics/index') {
        uni.reLaunch({
          url: '/pages/statistics/index'
        })
      } else {
        uni.switchTab({
          url: this.tabBarList[index].pagePath,
          success: () => {
            global.tabBarCurrent = index
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
image {
  width: 40rpx;
  height: 40rpx;
}
</style>
