<template>
	<view class="header-nav-box" :style="{
      height: Props.imgShow ? '' : Props.statusBarHeight + 'px',
      background: Props.imgShow
    }">
		<!-- 导航内容 -->
		<image :style="{ height: Props.imgShow ? '' : Props.statusBarHeight + 'px' }" class="header"
			src="@/static/images/home/<USER>" mode=""></image>
		<view class="nav-box-item" :style="{ top: Props.capsuleTop + 'px', color: Props.textColor || '#FFF' }">
			<view class="use" v-if="title == '首页'">
				<view class="text" @click="changeMode">关怀模式</view>
				<view class="stick"></view>
				<image @click="goto()" class="news" src="@/static/images/home/<USER>" mode=""></image>
			</view>
			<image v-if="title != '首页' && !Props.blackShow" class="back" @click="backpage"
				src="@/static/images/home/<USER>" mode="">
			</image>
			<view class="title">
				{{ title }}
			</view>
		</view>
		<!-- 自定义内容插槽 -->
		<slot name="content"></slot>
	</view>
</template>

<script>
	// import props from '../../uni_modules/uview-ui/libs/config/props'
	export default {
		name: 'HeaderNav',
		data() {
			return {
				isChecked: true
			}
		},
		props: {
			Props: {
				type: Object,
				default: () => {}
			},
			title: {
				type: String,
				default: '首页'
			}
		},
		methods: {
			goto() {
				uni.navigateTo({
					url: '/pages/Realtime/index'
				});
			},
			changeMode() {
				console.log('mode')
				if(this.isChecked) {
					this.isChecked = false
					this.$store.dispatch('updateLargeFontSize');
				} else {
					this.isChecked = true
					this.$store.dispatch('updateNormalFontSize');
				}
			},
			// 返回按钮回调函数
			back() {
				uni.navigateBack({
					data: 1
				})
				this.$emit('back')
			},
			backpage() {
				uni.navigateBack({
					delta: 1
				})
			}
		}
	}
</script>

<style lang="scss">
	.text {
		font-family: SourceHanSansCN, SourceHanSansCN;
		font-weight: 400;
		font-size: 28rpx;
		color: #ffffff;
		font-style: normal;
	}

	.back {
		width: 34rpx;
		height: 34rpx;
		position: absolute;
		left: 34rpx;
	}

	.header-nav-box {
		position: fixed;
		top: 0;
		z-index: 999;
		width: 100%;
		
		.header {
			width: 100%;
			height: 108px;
			position: absolute;
			top: 0;
		}

		.nav-box-item {
			height: 54rpx;
			position: absolute;
			display: flex;
			align-items: center;
			font-size: 34rpx;
			width: 100%;
			text-align: center;

			.use {
				display: flex;
				align-items: center;
				position: absolute;
				left: 37rpx;

				.stick {
					width: 1rpx;
					height: 36rpx;
					background: #d8d8d8;
					margin: 0 26rpx;
				}

				.news {
					width: 30rpx;
					height: 30rpx;
				}
			}

			.title {
				width: 100%;
				text-align: center;
				font-weight: 400;
				font-size: 34rpx;
				color: #fff;
				text-align: center;
				font-style: normal;
			}
		}
	}
</style>