import upload from '@/utils/upload'
import request from '@/utils/request'

// 查询客户给的投诉举报的分页
export function createComplaint(data) {
	return request({
		url: '/jy/complaint/create',
		method: 'POST',
		data: data
	})
}

//获得地图管理列表
export function getMapList(data) {
	return request({
		url: '/common/miniapp/getMapList',
		method: 'GET',
		data: data
	})
}

//获得政务办事
export function getWorkList(data) {
	return request({
		url: '/common/miniapp/mapWorkList',
		method: 'GET',
		data: data
	})
}

//根据id获取详情获得地图管理
export function getManagement(data) {
	return request({
		url: '/common/miniapp/queryMapById',
		method: 'GET',
		data: data
	})
}


//获取地区树
export function getTreestreet(data) {
	return request({
		url: '/common/miniapp/tree-street',
		method: 'GET',
		data: data
	})
}


//获取时间
export function getAppointmentTimeList(data) {
	return request({
		url: '/common/miniapp/getAppointmentTimeList',
		method: 'GET',
		data: data
	})
}

// 预约
export function appointmentAdd(data) {
	return request({
		url: '/miniapp/api/appointment/add',
		method: 'POST',
		data: data
	})
}

//预约详情
export function getAppointmentQueryByld(data) {
	return request({
		url: '/miniapp/api/appointment/queryById',
		method: 'GET',
		data: data
	})
}

//获取用户信息
export function getCurrentUserInfo(data) {
	return request({
		url: '/miniapp/api/appointment/getCurrentUserInfo',
		method: 'GET',
		data: data
	})
}

//获取事项信息
export function getItemList(data) {
	return request({
		url: '/miniapp/api/assist/getItemListByType',
		method: 'GET',
		data: data
	})
}


export function getLocation() {
	return new Promise((resolve, reject) => {
		console.log('--------------------11123')
		uni.getLocation({
			type: 'gcj02',
			// type: 'wgs84',
			highAccuracyExpireTime: '3000',
			accuracy: 'best',
			geocode: true,
			isHighAccuracy: true,
			success: (res) => {
				resolve(res)
			},
			fail: (err) => {
				reject(err)
			},
			complete: (res) => { }
		})
	})
}


//获取流水号
export function getYyBusinessNum(data) {
	return request({
		url: '/common/miniapp/getYyBusinessNum',
		method: 'GET',
		data: data
	})
}
