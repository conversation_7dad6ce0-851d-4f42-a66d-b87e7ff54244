import request from '@/utils/request'

// 帮办左侧 /boot/miniapp/api/assist/getItemList  businessType

export function getItemList(data) {
  return request({
    url: '/miniapp/api/assist/getItemList',
    method: 'GET',
    data: data
  })
}

// 帮办右侧 /miniapp/api/assist/getAssistantByItem

export function getAssistantByItem(data) {
  return request({
    url: '/miniapp/api/assist/getAssistantByItem',
    method: 'GET',
    data: data
  })
}
// 获取人员列表  getPageAssistant

export function getPageAssistant(data) {
  return request({
    url: '/miniapp/api/assist/getPageAssistant',
    method: 'GET',
    data: data
  })
}

// 新增事项 /miniapp/api/assist/addNewRecord

export function addNewRecord(data) {
  return request({
    url: '/miniapp/api/assist/addNewRecord',
    method: 'POST',
    data: data
  })
}

// 保存事项  /miniapp/api/assist/saveRecord

export function saveRecord(data) {
  return request({
    url: '/miniapp/api/assist/saveRecord',
    method: 'POST',
    data: data
  })
}

// 提交事项 :/miniapp/api/assist/submitRecord

export function submitRecord(data) {
  return request({
    url: '/miniapp/api/assist/submitRecord',
    method: 'POST',
    data: data
  })
}

// 上传  /miniapp/api/assist/upload

export function upload(data) {
  return request({
    url: '/miniapp/api/assist/upload',
    method: 'POST',
    data: data
  })
}

// 获取申请记录详情  /miniapp/api/assist/getRecordDetail

export function getRecordDetail(data) {
  return request({
    url: '/miniapp/api/assist/getRecordDetail',
    method: 'GET',
    data: data
  })
}

// 获取字典值 /miniapp/api/assist/getDictItem

export function getDictItem(data) {
  return request({
    url: '/miniapp/api/assist/getDictItem',
    method: 'GET',
    data: data
  })
}

// 删除事项附件 /miniapp/api/assist/deleteFile

export function deleteFile(data) {
  return request({
    url: '/miniapp/api/assist/deleteFile',
    method: 'GET',
    data: data
  })
}

// 获取省市县 列表 /miniapp/api/assist/administrativeDivisionRootList

export function administrativeDivisionRootList(data) {
  return request({
    url: '/miniapp/api/assist/administrativeDivisionRootList',
    method: 'GET',
    data: data
  })
}

// 通知列表 /miniapp/api/assist/getFrequentItem

export function getFrequentItem(data) {
  return request({
    url: '/miniapp/api/assist/getFrequentItem',
    method: 'GET',
    data: data
  })
}

// // /miniapp/api/assist/getAssistantByPoint

export function getAssistantByPoint(data) {
  return request({
    url: '/miniapp/api/assist/getAssistantByPoint',
    method: 'GET',
    data: data
  })
}

// 获取事项  新  /miniapp/api/assist/getItemListByType

export function getItemListByType(data) {
  return request({
    url: '/miniapp/api/assist/getItemListByType',
    method: 'GET',
    data: data
  })
}

// /boot/miniapp/api/help/addTelRecord

export function addTelRecord(data) {
  return request({
    url: '/miniapp/api/help/addTelRecord',
    method: 'POST',
    data: data
  })
}

// 事项类型 /boot/miniapp/api/help/getItemType

export function getItemType(data) {
  return request({
    // url: '/miniapp/api/help/getItemType',
    url: '/miniapp/api/help/getItemTypeByLevel',
    method: 'GET',
    data: data
  })
}

// /boot/miniapp/api/help/getItemList

export function getItemListNew(data) {
  return request({
    url: '/miniapp/api/help/getItemList',
    method: 'GET',
    data: data
  })
}

// 获取呼叫中心号码 /boot/miniapp/api/help/getHelpTelInfo

export function getHelpTelInfo(data) {
  return request({
    url: '/miniapp/api/help/getHelpTelInfo',
    method: 'GET',
    data: data
  })
}

// 办理事项 /boot/miniapp/api/help/addNewRecord

export function addNewRecordNew(data) {
  return request({
    url: '/miniapp/api/help/addNewRecord',
    method: 'POST',
    data: data
  })
}

// /boot/miniapp/api/help/listRecord

export function listRecordNew(data) {
  return request({
    url: '/miniapp/api/help/listRecord',
    method: 'GET',
    data: data
  })
}

// 提交  assist/commitRecord

export function commitRecord(data) {
  return request({
    url: '/miniapp/api/help/commitRecord',
    method: 'POST',
    data: data
  })
}
// 提交  assist/commitRecord

export function saveRecordNew(data) {
  return request({
    url: '/miniapp/api/help/saveRecord',
    method: 'POST',
    data: data
  })
}

export function getInstructionInfo(data) {
  return request({
    url: '/common/miniapp/getInstructionInfo',
    method: 'GET',
    data: data
  })
}

//  转派记录  /miniapp/help/getJyAssistRecordHandle

export function getJyAssistRecordHandle(data) {
  return request({
    url: '/miniapp/help/getJyAssistRecordHandle',
    method: 'GET',
    data: data
  })
}
