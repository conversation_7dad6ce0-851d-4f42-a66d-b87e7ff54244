import request from '@/utils/request'

// 查询管理分页记录  /miniapp/assist/listRecord

export function listRecord(data) {
  return request({
    url: '/miniapp/assist/listRecord',
    method: 'GET',
    data: data
  })
}

// 退回 /boot/miniapp/assist/back

export function back(data) {
  return request({
    url: '/miniapp/assist/back',
    method: 'POST',
    data: data
  })
}

// 通过 /boot/miniapp/assist/pass

export function pass(data) {
  return request({
    url: '/miniapp/assist/pass',
    method: 'POST',
    data: data
  })
}

// 分配 /boot/miniapp/manage/assign

export function assign(data) {
  return request({
    url: '/miniapp/manage/assign',
    method: 'POST',
    data: data
  })
}

// 分页查询帮办员进度  /boot/miniapp/assist/listAssistantProcess

export function listAssistantProcess(data) {
  return request({
    url: '/miniapp/assist/listAssistantProcess',
    method: 'GET',
    data: data
  })
}

// 查询当前帮办员进度  /boot/miniapp/assist/getMyProcess

export function getMyProcess(data) {
  return request({
    url: '/miniapp/assist/getMyProcess',
    method: 'GET',
    data: data
  })
}

// 首页进度   /boot/miniapp/help/progressAssist

export function progressAssist(data) {
  return request({
    url: '/miniapp/help/progressAssist',
    method: 'GET',
    data: data
  })
}

// 首页-帮办员及帮办管理员进度详情 /boot/miniapp/help/progressDetail

export function progressDetail(data) {
  return request({
    url: '/miniapp/help/progressDetail',
    method: 'GET',
    data: data
  })
}

// 首页-用户信息  接口地址:/boot/miniapp/help/userInfo

export function getUserInfo(data) {
  return request({
    url: '/miniapp/help/userInfo',
    method: 'GET',
    data: data
  })
}

// 获取帮办员列表

export function getAssistantList(data) {
  return request({
    url: '/common/miniapp/getAssistantList',
    method: 'GET',
    data: data
  })
}

// 获取管理端预约列表

export function getAppointment(data) {
  return request({
    url: '/miniapp/api/appointment-handle/list',
    method: 'GET',
    data: data
  })
}

// 分配

export function appointmentAssign(data) {
  return request({
    url: '/miniapp/api/appointment-handle/assign',
    method: 'PUT',
    data: data
  })
}

// 预约

export function appointmentProcess(data) {
  return request({
    url: '/miniapp/api/appointment-handle/appointmentProcess',
    method: 'PUT',
    data: data
  })
}

// 办结

export function appointmentFinish(data) {
  return request({
    url: '/miniapp/api/appointment-handle/appointmentFinish',
    method: 'PUT',
    data: data
  })
}

// 获取管理端预约详情

export function getAppointmentQueryById(data) {
  return request({
    url: '/miniapp/api/appointment-handle/queryById',
    method: 'GET',
    data: data
  })
}

// /miniapp/help/helpHandAdminList  帮办管理员列表

export function helpHandAdminList(data) {
  return request({
    // url: '/miniapp/help/helpHandAdminList',
    url: '/miniapp/help/adminList', // 新
    method: 'GET',
    data: data
  })
}

// 转派 /miniapp/help/transfer

export function transfer(data) {
  return request({
    url: '/miniapp/help/transfer',
    method: 'POST',
    data: data
  })
}

// /miniapp/help/helpHandList  帮办列表

export function helpHandList(data) {
  return request({
    // url: '/miniapp/help/helpHandList',
    url: '/common/miniapp/getAssistantList',
    method: 'GET',
    data: data
  })
}
export function helpHandListNew(data) {
  return request({
    // url: '/miniapp/help/helpHandList',
    url: '/miniapp/help/list', // 新
    method: 'GET',
    data: data
  })
}

// /miniapp/help/back 帮办退回

export function helpBack(data) {
  return request({
    url: '/miniapp/help/back',
    method: 'POST',
    data: data
  })
}
// 办事
export function finish(data) {
  return request({
    url: '/miniapp/help/finish',
    method: 'POST',
    data: data
  })
}

// 预约列表 /miniapp/help/progressDetail

// export function progressDetail(data) {
//   return request({
//     url: '/miniapp/help/progressDetail',
//     method: 'GET',
//     data: data
//   })
// }

// 呼叫中心列表 /miniapp/help/helpHandleTelRecordList

export function helpHandleTelRecordList(data) {
  return request({
    url: '/miniapp/help/helpHandleTelRecordList',
    method: 'GET',
    data: data
  })
}

// /miniapp/help/receiveList

export function receiveList(data) {
  return request({
    url: '/miniapp/help/receiveList',
    method: 'GET',
    data: data
  })
}

// 发件 /miniapp/help/send

export function send(data) {
  return request({
    url: '/miniapp/help/send',
    method: 'POST',
    data: data
  })
}

// /miniapp/help/reject 拒接

export function reject(data) {
  return request({
    url: '/miniapp/help/reject',
    method: 'POST',
    data: data
  })
}

// /miniapp/help/receiveAndBack 接收与退回

export function receiveAndBack(data) {
  return request({
    url: '/miniapp/help/receiveAndBack',
    method: 'POST',
    data: data
  })
}

// 免登录 loginByAppCode
export function loginByAppCode(data) {
  return request({
    url: '/miniapp/auth/loginByAppCode',
    method: 'POST',
    data: data
  })
}
