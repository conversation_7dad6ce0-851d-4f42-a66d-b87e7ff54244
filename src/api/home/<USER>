import upload from '@/utils/upload'
import request from '@/utils/request'

// 查询获得实时信息分页
export function getRealTimeInfo(data) {
  return request({
    url: '/jy/real-time-info/page',
    method: 'GET',
		data: data
  })
}
// 获得企心协力列表
export function getCooperationList(data) {
  return request({
    url: '//common/miniapp/getPreferentialPolicyList',
    method: 'GET',
		data: data
  })
}
// 获得特色服务列表
export function getFeatureList(data) {
  return request({
    url: '/jy/feature-service/list',
    method: 'GET',
		data: data
  })
}

// 获得轮播图管理列表
export function getSlideshowList(data) {
  return request({
    url: '/common/miniapp/getSlideShowList',
    method: 'GET',
		data: data
  })
}

// 小程序端获取通知 消息接口
export function getNoticelistByUser(data) {
  return request({
    url: '/miniapp/api/notice/listByUser',
    method: 'GET',
		data: data
  })
}
