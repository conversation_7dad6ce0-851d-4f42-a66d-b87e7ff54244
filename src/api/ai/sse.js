import {
	fetchEventSource
} from '@microsoft/fetch-event-source';


export function connectToSSE(controller, url, token, data, onMessage, onError, onComplete, onOpen) {
	console.log(token, 'token1-------')
	console.log(url, 'url-------')
	fetchEventSource(url, {
		method: 'POST',
		signal: controller.signal,
		headers: {
			'Content-Type': 'application/json',
			'Authorization': `Bear<PERSON> ${token}`
		},
		body: JSON.stringify(data), // 发送的数据
		onopen(response) {
			// console.log('触发1111')
			if (onOpen) onOpen(response); // SSE连接打开时调用
			if (response.ok && response.headers.get('content-type') === 'text/event-stream') {
				console.log('SSE connection established');
			} else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
				console.error('Client error:', response.statusText);
			}
		},
		onmessage(event) {
			if (onMessage) onMessage(event.data); // 将接收到的消息传递给回调函数
		},
		onerror(err) {
			if (onError) onError(err); // 处理错误
			console.error('SSE connection error:', err);
		},
		onclose() {
			if (onComplete) onComplete(); // SSE连接关闭时调用
			console.log('SSE connection closed');
		},
	});
}
