import request from '@/utils/request'

// 查询聊天列表 /boot/miniapp/api/assist/getPageMsgListd

export function getPageMsgListd(data) {
  return request({
    url: '/miniapp/api/help/getPageHelpMsgList',
    method: 'GET',
    data: data
  })
}
// 查询聊天记录  /boot/miniapp/api/assist/getPageRecordMsg

export function getPageRecordMsg(data) {
  return request({
    url: '/miniapp/api/help/getPageHelpRecordMsg',
    method: 'GET',
    data: data
  })
}

// 帮办员查询聊天记录  /miniapp/assist/getPageMsgList

export function getHelpList(data) {
  return request({
    url: '/miniapp/help/getPageHelpMsgList',
    method: 'GET',
    data: data
  })
}

// 查询帮办员聊天记录 /miniapp/assist/getPageRecordMsg

export function getHelpRecord(data) {
  return request({
    url: '/miniapp/api/help/getPageHelpRecordMsg',
    method: 'GET',
    data: data
  })
}

// 发送消息 /boot/miniapp/api/assist/sendTxtMsg

export function sendTxtMsg(data) {
  return request({
    url: '/miniapp/api/help/sendHelpTxtMsg',
    method: 'POST',
    data: data
  })
}

// 聊天文件上传 /boot/miniapp/api/assist/sendMediaMsg

export function sendMediaMsg(data) {
  return request({
    url: '/miniapp/api/help/sendHelpMediaMsg',
    method: 'POST',
    data: data
  })
}
