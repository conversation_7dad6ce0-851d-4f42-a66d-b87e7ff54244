import request from '@/utils/request'

// 登录方法
export function login(username, password, captchaVerification) {
  const data = {
    username,
    password,
    captchaVerification
  }
  return request({
    url: '/system/auth/login',
    headers: {
      isToken: false
    },
    method: 'POST',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/system/auth/get-permission-info',
    method: 'GET'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/system/auth/logout',
    method: 'POST'
  })
}

// 微信认证登录

// 根据token返回用户信息 /event/swp/auth/checkToken
export function getUserInfos(token) {
  return request({
    url: '/miniapp/auth/checkToken',
    header: {
      Authorization: token
    },
    method: 'GET'
  })
}

// 根据code 返回openid
export function wxLogin(code) {
  return request({
    url: '/miniapp/auth/code2Session',
    method: 'GET',
    data: {
      code: code
    }
  })
}

// 绑定微信用户
export function createUser(data) {
  return request({
    url: '/miniapp/auth/createUser',
    method: 'POST',
    data: data
  })
}

// 授权登录
export function authorizeLogin(data) {
  return request({
    url: '/miniapp/auth/authorizeLogin',
    method: 'POST',
    data: data
  })
}

// 获取当前登录用户
export function checkToken() {
  return request({
    url: '/miniapp/api/user/checkToken',
    method: 'GET',
    isToken: true
  })
}

// 注销
export function newLogout() {
  return request({
    url: '/miniapp/api/user/logout',
    method: 'POST'
  })
}

// 发送短信验证码 /boot/miniapp/auth/sendMsg

export function sendMsg(data) {
  return request({
    url: '/miniapp/auth/sendMsg',
    method: 'GET',
    data: data
  })
}

// 登录 /boot/miniapp/auth/loginByPasswd

export function loginByPasswd(data) {
  return request({
    url: '/miniapp/auth/loginByPasswd',
    method: 'POST',
    data: data
  })
}

// 更新密码 /boot/miniapp/api/user/updatePasswd

export function updatePasswd(data) {
  return request({
    url: '/miniapp/api/user/updatePasswd',
    method: 'POST',
    data: data
  })
}


//  注册 /boot/miniapp/auth/registerUser

export function registerUser(data) {
  return request({
    url: '/miniapp/auth/registerUser',
    method: 'POST',
    data: data
  })
}
