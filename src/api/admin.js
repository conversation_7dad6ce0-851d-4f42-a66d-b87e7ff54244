import request from '@/utils/request'

// 帮办委托书管理表-分页列表查询 /listJyProxyManagementService
export const listJyProxyManagementService = (data) => {
  return request({
    url: '/miniapp/help/listJyProxyManagementService',
    method: 'GET',
    data: data
  })
}
// 委托书存储 fileSaveOrUpdate
export const fileSaveOrUpdate = (data) => {
  return request({
    url: '/miniapp/help/fileSaveOrUpdate',
    method: 'POST',
    data: data
  })
}

// 帮办申请记录表-编辑 /recordEdit
export const recordEdit = (data) => {
  return request({
    url: '/miniapp/help/recordEdit',
    method: 'PUT',
    data: data
  })
}

// 删除委托书 /deleteFile
export const deleteFile = (data) => {
  return request({
    url: `/miniapp/help/deleteFile?id=${data.id}`,
    method: 'DELETE'
  })
}

// 添加补充字段  /addJyHelpHandleRecordField
export const addJyHelpHandleRecordField = (data) => {
  return request({
    url: '/miniapp/help/addJyHelpHandleRecordField',
    method: 'POST',
    data: data
  })
}
// 删除补充字段 /deleteJyHelpHandleRecordField  id
export const deleteJyHelpHandleRecordField = (id) => {
  return request({
    url: `/miniapp/help/deleteJyHelpHandleRecordField?id=${id}`,
    method: 'DELETE'
  })
}

// 获取委托书 /miniapp/api/help/getProxyManagement

export const getProxyManagement = (data) => { 
  return request({
    url: '/miniapp/api/help/getProxyManagement',
    method: 'GET',
    data: data
  })
}
//  编辑保存补充字段 /editJyHelpHandleRecordField
export const editJyHelpHandleRecordField = (data) => {
  return request({
    url: '/miniapp/help/editJyHelpHandleRecordField',
    method: 'PUT',
    data: data
  })
}

// 呼叫中心分配 /miniapp/help/transfer
export const telTransfer = (data) => {
  return request({
    url: '/miniapp/help/telTransfer',
    method: 'POST',
    data: data
  })
}
