import request from '@/utils/request'

// 查询个人分页记录  /miniapp/api/assist/listRecord

export function listRecord(data) {
  return request({
    url: '/miniapp/api/assist/listRecord',
    method: 'GET',
    data: data
  })
}

// 评价 /miniapp/api/assist/rateRecord

// export function rateRecord(data) {
//   return request({
//     url: '/miniapp/api/assist/rateRecord',
//     method: 'POST',
//     data: data
//   })
// }
export function rateRecord(data) {
  return request({
    url: '/miniapp/api/help/rateRecord',
    method: 'POST',
    data: data
  })
}

// 更新用户信息  /boot/miniapp/api/user/updateUser

export function updateUser(data) {
  return request({
    url: '/miniapp/api/user/updateUser',
    method: 'POST',
    data: data
  })
}

// 查询预约记录

export function appointMent(data) {
  return request({
    url: '/miniapp/api/appointment/list',
    method: 'GET',
    data: data
  })
}

// 获取帮办员信息 /miniapp/api/assist/getAssistantById

export function getAssistantById(data) {
  return request({
    url: '/miniapp/api/assist/getAssistantById',
    method: 'GET',
    data: data
  })
}
