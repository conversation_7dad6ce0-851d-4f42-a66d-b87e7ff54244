import Vue from 'vue'
import App from './App'
import store from './store' // store
import plugins from './plugins' // plugins
// import './permission' // permission
import uView from '@/uni_modules/uview-ui'
import Socket from './module/Socket.js'
import EventHub from './module/EventHub.js'
// import axios from 'axios'
import { VueJsonp } from 'vue-jsonp'
import config from './config'
// import Vconsole from'vconsole'
// 登录导入
import { checkToken, loginByPasswd } from '@/api/login'
import { newSetToken, removeToken } from '@/utils/auth'
import CryptoJS from 'crypto-js'
import { getAccessToken } from '@/utils/auth'
// let vConsole = new Vconsole()

Vue.use(plugins)
Vue.use(uView)
Vue.use(VueJsonp)
// Vue.use(vConsole)
Vue.config.productionTip = false
Vue.prototype.$store = store
Vue.prototype.$Socket = Socket
Vue.prototype.$EventHub = EventHub
App.mpType = 'app'
const ACCESS_TYPE = 'ws'
const app = new Vue({
  ...App
})
const webIMType = [5]
app.$mount()

// // 免认证登录
let code = getCleanedCode()
let openId = getOpenId()
console.log('登录参数', code, openId)

if ((code || openId) && !getAccessToken()) {
  const requestData = {}
  if (code) {
    requestData.code = code
  }
  if (openId) {
    requestData.openId = openId
  }

  uni.request({
    url: config.baseUrl + '/boot/miniapp/auth/loginByCode',
    method: 'POST',
    data: requestData,
    success: (res) => {
      console.log(res, 'res')
      wxLoginSuccess(res.data.result.token)
    },
    fail: (err) => {
      console.error(err)
    }
  })
} else {
  if (process.env.NODE_ENV === 'development' && !getAccessToken()) {
    uni.request({
      url: config.baseUrl + '/boot/miniapp/auth/sm4Code',
      method: 'GET',
      data: {
        // 普通
        // openId: 'oZFuf7YA06JIMPLbo8JcszE7SaRI'
        // 管理员
        openId: 'oEeMj7exXUSinB2VoUGUfnYrT8js'
      },
      success: (res) => {
        newLogin(res.data.result)
      },
      fail: (err) => {
        console.error(err)
      }
    })
  }
  if (!code && !getAccessToken()) {
    // setTimeout(() => {
    // 暂时无法登录
    // uni.showModal({
    //   content: '暂时无法登录,请联系管理员',
    //   showCancel: false
    // })
    // }, 500)
  }
}

try {
  let tokenGet = webIMType.map((type) => {
    return new Promise(async (resolve, reject) => {
      let demoToken = ''
      if (ACCESS_TYPE === 'ws') {
        console.log(4)
        // const res = await axios.get('http://10.110.115.11:8032/boot/common/miniapp/getToken');

        uni.request({
          // url: 'http://117.73.12.25:42877/boot/common/miniapp/getToken', // 你的API地址
          url: 'https://jyxq.jialangshuchan.cn/boot/common/miniapp/getToken', // 你的API地址
          method: 'GET',
          header: {
            'content-type': 'application/json' // 请求头
          },
          success: (res) => {
            demoToken = res.data.result
            let result = {
              type: type,
              token: demoToken,
              access: ACCESS_TYPE
            }
            resolve(result)
          },
          fail: (err) => {
            console.error(err) // 请求失败的错误信息
          }
        })
      }
    })
  })

  Promise.all(tokenGet)
    .then((res) => {
      // Object.defineProperty(window, 'webimToken', {
      // 	writable: false,
      // 	enumerable: false,
      // 	configurable: false,
      // 	value: res
      // });
      // console.log(window.webimToken, '1222877777');
      if (ACCESS_TYPE === 'ws') {
        ;(async () => {
          let mixin = await import('@/module/index.js')
          console.log('【init msg----------mixin---->】', mixin)
        })()
      }
    })
    .catch((e) => {})
} catch (error) {
  console.log(error)
}

function newLogin(code2) {
  console.log('code2code2', code2)
  uni.request({
    url: config.baseUrl + '/boot/miniapp/auth/loginByCode',
    method: 'POST',
    data: {
      code: code2
    },
    success: (res) => {
      console.log(res, 'res')
      wxLoginSuccess(res.data.result.token)
    },
    fail: (err) => {
      console.error(err)
    }
  })
}
// 登录方法
async function handleLogin() {
  let loginForm = {
    username: '15571578328',
    password: 'Hwj@15571578328',
    captcha: '',
    checkKey: ''
  }
  const res = await loginByPasswd({
    username: encryptLoginData(loginForm.username),
    password: encryptLoginData(loginForm.password),
    captcha: loginForm.captcha,
    checkKey: loginForm.checkKey
  })
  if (res.success) {
    wxLoginSuccess(res.result.token)
  }
}
// 认证登录成功
async function wxLoginSuccess(token) {
  newSetToken(token)
  const res = await checkToken(token)
  if (!res.success) {
    newLogin(code)
    return
  }
  const avatar = res.result.userInfo.avatar
  const nickname = res.result.userInfo.nickName
  let loginType = null
  if (res.result.userInfo.isAssistManger || res.result.userInfo.isAssist) {
    loginType = 0
  } else {
    loginType = 1
  }

  store.commit('SET_NAME', nickname)
  store.commit('SET_AVATAR', avatar)
  store.commit('SET_USERINFO', res.result.userInfo)
  store.commit('setRoleId', 1)
  uni.setStorageSync('loginType', loginType)
  window.location.reload()
  // setTimeout(() => {
  //   uni.reLaunch({
  //     url: '/pages/assistant/index'
  //   })
  // }, 50);
}
function encryptLoginData(data) {
  if (typeof data !== 'string') {
    console.error('Input must be a string')
    return null
  }
  const key = CryptoJS.enc.Utf8.parse('1234567890adbcde')
  const iv = CryptoJS.enc.Utf8.parse('1234567890hjlkew')
  const blockSize = 16
  const paddingSize = blockSize - (data.length % blockSize)
  const paddedData = data + String.fromCharCode(paddingSize).repeat(paddingSize)
  try {
    const encrypted = CryptoJS.AES.encrypt(paddedData, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.NoPadding
    })
    const result = encrypted.ciphertext.toString(CryptoJS.enc.Base64)
    console.log('Encrypted result:', result)
    return result
  } catch (error) {
    console.error('Encryption error:', error)
    return null
  }
}
function getCleanedCode() {
  let query = ''
  // 获取传统模式的查询参数（?code=xxx&openId=xxx）
  if (window.location.search) {
    query = window.location.search.substring(1)
  }
  // 获取 hash 模式的查询参数（#/path?code=xxx&openId=xxx）
  else if (window.location.hash.includes('?')) {
    query = window.location.hash.split('?')[1]
  }
  // 如果没有找到 query，返回 false
  if (!query) {
    return false
  }
  // 解析查询参数
  const vars = query.split('&')
  let code = ''

  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=')
    const key = decodeURIComponent(pair[0])
    const value = decodeURIComponent(pair[1])

    if (key === 'code') {
      code = value
      break // 找到后可以提前结束循环
    }
  }
  // 去除 code 中的空格和特殊字符
  if (code) {
    // 去除空格
    // let cleanedCode = code.replace(/\s*/g, '')
    // 处理特殊字符
    // cleanedCode = encodeURIComponent(cleanedCode)

    let cleanedCode = code
    return cleanedCode
  }

  return false
}

function getOpenId() {
  let query = ''
  // 获取传统模式的查询参数（?code=xxx&openId=xxx）
  if (window.location.search) {
    query = window.location.search.substring(1)
  }
  // 获取 hash 模式的查询参数（#/path?code=xxx&openId=xxx）
  else if (window.location.hash.includes('?')) {
    query = window.location.hash.split('?')[1]
  }
  // 如果没有找到 query，返回 false
  if (!query) {
    return false
  }
  // 解析查询参数
  const vars = query.split('&')
  let openId = ''

  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=')
    const key = decodeURIComponent(pair[0])
    const value = decodeURIComponent(pair[1])

    if (key === 'openId') {
      openId = value
      break // 找到后可以提前结束循环
    }
  }

  if (openId) {
    return openId
  }

  return false
}

// // Token 相关操作
// function getCleanedCode() {
//   let query = ''
//   // 获取传统模式的查询参数
//   if (window.location.search) {
//     query = window.location.search.substring(1)
//   }
//   // 获取 hash 模式的查询参数
//   else if (window.location.hash.includes('?')) {
//     query = window.location.hash.split('?')[1]
//   }

//   if (!query) return false

//   const vars = query.split('&')
//   let code = ''

//   for (let i = 0; i < vars.length; i++) {
//     const pair = vars[i].split('=')
//     if (decodeURIComponent(pair[0]) === 'code') {
//       code = decodeURIComponent(pair[1])
//       break
//     }
//   }

//   return code || false
// }

// // 检查登录状态
// async function checkLoginStatus() {
//   const token = getAccessToken()
//   const code = getCleanedCode()

//   // 如果 URL 中有 code，优先使用 code 登录
//   if (code) {
//     // 清除旧的登录信息
//     removeToken()
//     await loginWithCode(code)
//     // 清除 URL 中的 code 参数，防止刷新时重复登录
//     cleanUrlCode()
//     return
//   }

//   // 如果有 token，验证 token 有效性
//   if (token) {
//     try {
//       const res = await checkToken(token)
//       if (res.success) {
//         // token 有效，更新用户信息
//         await updateUserInfo(res.result.userInfo)
//       } else {
//         // token 无效，需要重新登录
//         removeToken()
//         await handleLogin()
//       }
//     } catch (error) {
//       console.error('Token check failed:', error)
//       removeToken()
//       await handleLogin()
//     }
//   } else {
//     // 无 token，进行登录
//     await handleLogin()
//   }
// }

// // 使用 code 登录
// function loginWithCode(code) {
//   return new Promise((resolve, reject) => {
//     uni.request({
//       url: config.baseUrl + '/boot/miniapp/auth/loginByCode',
//       method: 'POST',
//       data: { code },
//       success: async (res) => {
//         if (res.data.success) {
//           await wxLoginSuccess(res.data.result.token)
//           resolve(res.data)
//         } else {
//           reject(res.data)
//         }
//       },
//       fail: (err) => {
//         console.error('Login with code failed:', err)
//         reject(err)
//       }
//     })
//   })
// }

// // 清除 URL 中的 code 参数
// function cleanUrlCode() {
//   const url = new URL(window.location.href)
//   url.searchParams.delete('code')
//   // 使用 history.replaceState 更新 URL，不触发页面刷新
//   window.history.replaceState({}, '', url.toString())
// }

// // 登录方法
// async function handleLogin() {
//   const loginForm = {
//     username: '15571578328',
//     password: 'Hwj@15571578328',
//     captcha: '',
//     checkKey: ''
//   }

//   try {
//     const res = await loginByPasswd({
//       username: encryptLoginData(loginForm.username),
//       password: encryptLoginData(loginForm.password),
//       captcha: loginForm.captcha,
//       checkKey: loginForm.checkKey
//     })

//     if (res.success) {
//       await wxLoginSuccess(res.result.token)
//     } else {
//       console.error('Login failed:', res)
//     }
//   } catch (error) {
//     console.error('Login error:', error)
//   }
// }

// // 更新用户信息
// async function updateUserInfo(userInfo) {
//   const { avatar, nickName } = userInfo
//   store.commit('SET_NAME', nickName)
//   store.commit('SET_AVATAR', avatar)
//   store.commit('SET_USERINFO', userInfo)
//   store.commit('setRoleId', 1)
//   uni.setStorageSync('loginType', 1)
// }

// // 认证登录成功
// async function wxLoginSuccess(token) {
//   newSetToken(token)
//   try {
//     const res = await checkToken(token)
//     if (res.success) {
//       await updateUserInfo(res.result.userInfo)
//       // 使用 reload 时机标记避免无限刷新
//       if (!sessionStorage.getItem('initialLoad')) {
//         sessionStorage.setItem('initialLoad', 'true')
//         window.location.reload()
//       }
//     } else {
//       removeToken()
//       await handleLogin()
//     }
//   } catch (error) {
//     console.error('Token check failed after login:', error)
//     removeToken()
//     await handleLogin()
//   }
// }

// // 数据加密
// function encryptLoginData(data) {
//   if (typeof data !== 'string') {
//     console.error('Input must be a string')
//     return null
//   }

//   const key = CryptoJS.enc.Utf8.parse('1234567890adbcde')
//   const iv = CryptoJS.enc.Utf8.parse('1234567890hjlkew')
//   const blockSize = 16
//   const paddingSize = blockSize - (data.length % blockSize)
//   const paddedData = data + String.fromCharCode(paddingSize).repeat(paddingSize)

//   try {
//     const encrypted = CryptoJS.AES.encrypt(paddedData, key, {
//       iv: iv,
//       mode: CryptoJS.mode.CBC,
//       padding: CryptoJS.pad.NoPadding
//     })
//     return encrypted.ciphertext.toString(CryptoJS.enc.Base64)
//   } catch (error) {
//     console.error('Encryption error:', error)
//     return null
//   }
// }

// // 初始化调用
// checkLoginStatus()
